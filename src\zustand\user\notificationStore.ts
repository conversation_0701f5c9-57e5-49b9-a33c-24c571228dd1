import { create } from 'zustand';

interface NotificationState {
  message: string | null;
  type: 'success' | 'error' | null;
  showNotification: (_message: string, _type: 'success' | 'error') => void;
  clearNotification: () => void;
}

export const useNotificationStore = create<NotificationState>((set) => ({
  message: null,
  type: null,

  showNotification: (message, type) => {
    set({ message: null, type: null });
    setTimeout(() => set({ message, type }), 100);
  },

  clearNotification: () => set({ message: null, type: null }),
}));
