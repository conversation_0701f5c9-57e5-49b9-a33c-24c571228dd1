/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Search, X, CalendarIcon } from 'lucide-react';
import type { CourseFilters } from '@/types/courseType';
import { AdminValuesCategories } from '@/constants';
import { useAdminValues } from '@/queries';
import type { Topic } from '@/type';
import { formatDateDDMonthYYYY } from '@/utils';
import ResetIcon from '@/components/icons/resetIcon';

interface CourseFiltersProps {
  filters: CourseFilters;
  onFilterChange: (_filters: CourseFilters) => void;
  onApplyFilters: (_filters?: CourseFilters) => void;
  onResetFilters: () => void;
}

interface Cities {
  id: string;
  value: string;
  label: string;
}

export const CourseFilterPanel: React.FC<CourseFiltersProps> = ({
  filters,
  onFilterChange,
  onApplyFilters,
  onResetFilters,
}) => {
  const [appliedFilters, setAppliedFilters] = useState<
    { key: string; value: string }[]
  >([]);
  const [isResetDisabled, setIsResetDisabled] = useState(true);
  const [showAppliedFilters, setShowAppliedFilters] = useState(false);

  useEffect(() => {
    const hasFilters = Object.entries(filters).some(([key, value]) => {
      return value !== undefined && value !== '' && key !== 'status';
    });
    setIsResetDisabled(!hasFilters);
  }, [filters]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange({ ...filters, search: e.target.value });
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleApplyFilters();
    }
  };

  const isDateRangeValid = () => {
    if (filters.dateType === 'startDate') {
      if (!filters.startDate || !filters.endDate) return true;
      return new Date(filters.startDate) <= new Date(filters.endDate);
    } else if (filters.dateType === 'createdOn') {
      if (!filters.dateFrom || !filters.dateTo) return true;
      return new Date(filters.dateFrom) <= new Date(filters.dateTo);
    } else if (filters.dateType === 'modifiedOn') {
      if (!filters.dateFrom || !filters.dateTo) return true;
      return new Date(filters.dateFrom) <= new Date(filters.dateTo);
    }
    return true;
  };

  const getFromDate = () => {
    if (filters.dateType === 'startDate') {
      return filters.startDate;
    } else if (
      filters.dateType === 'createdOn' ||
      filters.dateType === 'modifiedOn'
    ) {
      return filters.dateFrom;
    }
    return undefined;
  };

  const getToDate = () => {
    if (filters.dateType === 'startDate') {
      return filters.endDate;
    } else if (
      filters.dateType === 'createdOn' ||
      filters.dateType === 'modifiedOn'
    ) {
      return filters.dateTo;
    }
    return undefined;
  };

  const handleFromDateChange = (date: Date | undefined) => {
    const isoDate = date
      ? new Date(
          Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)
        ).toISOString()
      : undefined;

    if (filters.dateType === 'startDate') {
      onFilterChange({
        ...filters,
        startDate: isoDate,
      });
    } else if (
      filters.dateType === 'createdOn' ||
      filters.dateType === 'modifiedOn'
    ) {
      onFilterChange({
        ...filters,
        dateFrom: isoDate,
      });
    }
  };

  const handleToDateChange = (date: Date | undefined) => {
    const isoDate = date
      ? new Date(
          date.getFullYear(),
          date.getMonth(),
          date.getDate(),
          23,
          59,
          59
        ).toISOString()
      : undefined;

    if (filters.dateType === 'startDate') {
      onFilterChange({
        ...filters,
        endDate: isoDate,
      });
    } else if (
      filters.dateType === 'createdOn' ||
      filters.dateType === 'modifiedOn'
    ) {
      onFilterChange({
        ...filters,
        dateTo: isoDate,
      });
    }
  };

  const removeFilter = (key: string) => {
    const updatedFilters = { ...filters };

    switch (key) {
      case 'Main Topic':
        updatedFilters.mainTopic = undefined;
        break;
      case 'Delivery':
        updatedFilters.deliveryMode = undefined;
        break;
      case 'City':
        updatedFilters.city = undefined;
        break;
      case 'Experience Level':
        updatedFilters.experienceLevel = undefined;
        break;
      case 'Start Date':
        updatedFilters.dateType = undefined;
        updatedFilters.startDate = undefined;
        updatedFilters.endDate = undefined;
        break;
      case 'Created On':
        updatedFilters.dateType = undefined;
        updatedFilters.dateFrom = undefined;
        updatedFilters.dateTo = undefined;
        break;
      case 'Last Modified On':
        updatedFilters.dateType = undefined;
        updatedFilters.dateFrom = undefined;
        updatedFilters.dateTo = undefined;
        break;
    }

    onFilterChange(updatedFilters);
    const newAppliedFilters = appliedFilters.filter((f) => f.key !== key);
    setAppliedFilters(newAppliedFilters);
    setShowAppliedFilters(newAppliedFilters.length > 0);
    onApplyFilters(updatedFilters);
  };

  const handleApplyFilters = () => {
    const newAppliedFilters: { key: string; value: string }[] = [];

    if (filters.mainTopic) {
      newAppliedFilters.push({ key: 'Main Topic', value: filters.mainTopic });
    }

    if (filters.deliveryMode) {
      newAppliedFilters.push({ key: 'Delivery', value: filters.deliveryMode });
    }

    if (filters.city) {
      newAppliedFilters.push({ key: 'City', value: filters.city });
    }

    if (filters.experienceLevel) {
      newAppliedFilters.push({
        key: 'Experience Level',
        value: filters.experienceLevel,
      });
    }

    if (filters.dateType) {
      let dateLabel = '';
      switch (filters.dateType) {
        case 'startDate':
          dateLabel = 'Start Date';
          break;
        case 'createdOn':
          dateLabel = 'Created On';
          break;
        case 'modifiedOn':
          dateLabel = 'Last Modified On';
          break;
      }

      const dateValue = [];
      const fromDate = getFromDate();
      const toDate = getToDate();

      if (fromDate) {
        dateValue.push(`From: ${formatDateDDMonthYYYY(fromDate)}`);
      }
      if (toDate) {
        dateValue.push(`To: ${formatDateDDMonthYYYY(toDate)}`);
      }

      if (dateValue.length > 0) {
        newAppliedFilters.push({ key: dateLabel, value: dateValue.join(' ') });
      }
    }

    setAppliedFilters(newAppliedFilters);
    setShowAppliedFilters(newAppliedFilters.length > 0);
    onApplyFilters();
  };

  const handleResetFilters = () => {
    const resetFilters = {
      search: undefined,
      mainTopic: undefined,
      deliveryMode: undefined,
      city: undefined,
      experienceLevel: undefined,
      dateType: undefined,
      startDate: undefined,
      endDate: undefined,
      dateFrom: undefined,
      dateTo: undefined,
    };
    onFilterChange(resetFilters);
    setShowAppliedFilters(false);
    setAppliedFilters([]);
    onResetFilters();
  };

  const trainingTopics = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });

  const mainTopics: Topic[] =
    trainingTopics.data?.data?.data?.customValues || [];

  const allCities = useAdminValues({
    category: AdminValuesCategories?.cities?.category,
    subcategory: AdminValuesCategories?.cities.subcategories.Level_2,
  });

  const cities: Cities[] = allCities.data?.data?.data?.customValues || [];
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 border border-neutral-300 px-2 rounded-lg">
        <Search className="text-neutral-400" />
        <Input
          placeholder="Search keyword..."
          value={filters.search || ''}
          onChange={handleSearchChange}
          onKeyDown={handleSearchKeyDown}
          className="w-full border-none"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 max-w-full">
        <div>
          <Select
            value={filters.mainTopic || ''}
            onValueChange={(value) =>
              onFilterChange({ ...filters, mainTopic: value })
            }
          >
            <SelectTrigger
              className={filters.mainTopic ? 'border-secondary-500' : ''}
            >
              <SelectValue placeholder="Main Topic" />
            </SelectTrigger>
            <SelectContent>
              {mainTopics.map((topic) => (
                <SelectItem key={topic?.id} value={topic?.label}>
                  {topic?.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Select
            value={filters.deliveryMode || ''}
            onValueChange={(value) =>
              onFilterChange({ ...filters, deliveryMode: value as any })
            }
          >
            <SelectTrigger
              className={filters.deliveryMode ? 'border-secondary-500' : ''}
            >
              <SelectValue placeholder="Delivery" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="In Person">In Person</SelectItem>
              <SelectItem value="Online">Online</SelectItem>
              <SelectItem value="Hybrid">Hybrid</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Select
            value={filters.city || ''}
            onValueChange={(value) =>
              onFilterChange({ ...filters, city: value })
            }
          >
            <SelectTrigger
              className={filters.city ? 'border-secondary-500' : ''}
            >
              <SelectValue placeholder="City" />
            </SelectTrigger>
            <SelectContent>
              {cities.map((city) => (
                <SelectItem key={city?.id} value={city?.label}>
                  {city?.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Select
            value={filters.experienceLevel || ''}
            onValueChange={(value) =>
              onFilterChange({ ...filters, experienceLevel: value as any })
            }
          >
            <SelectTrigger
              className={filters.experienceLevel ? 'border-secondary-500' : ''}
            >
              <SelectValue placeholder="Experience Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Beginner">Beginner</SelectItem>
              <SelectItem value="Intermediate">Intermediate</SelectItem>
              <SelectItem value="Advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="max-w-3xl">
        <p className="text-[16px] font-medium mb-2">Dates:</p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Select
              value={filters.dateType || ''}
              onValueChange={(value) =>
                onFilterChange({ ...filters, dateType: value as any })
              }
            >
              <SelectTrigger
                className={filters.dateType ? ' border-secondary-500' : ''}
              >
                <SelectValue placeholder="Date Filter Name" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="startDate">Start Date</SelectItem>
                <SelectItem value="createdOn">Created On</SelectItem>
                <SelectItem value="modifiedOn">Last Modified On</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={`w-full text-left font-normal justify-between rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor] ${
                    !getFromDate() && 'text-muted-foreground'
                  } ${getFromDate() ? 'border-secondary-500' : ''} ${
                    !isDateRangeValid() ? 'border-destructive-500' : ''
                  }`}
                  disabled={!filters.dateType}
                >
                  {getFromDate() ? (
                    formatDateDDMonthYYYY(getFromDate() as string)
                  ) : (
                    <span>From: DD/MM/YYYY</span>
                  )}
                  <CalendarIcon className="mr-2 h-4 w-4 !text-[--bodyTextColor]" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <div className="flex flex-col">
                  <Calendar
                    mode="single"
                    selected={
                      getFromDate()
                        ? new Date(getFromDate() as string)
                        : undefined
                    }
                    onSelect={(date) => handleFromDateChange(date)}
                    initialFocus
                  />
                  <Button
                    variant="ghost"
                    onClick={() => handleFromDateChange(undefined)}
                    className="text-destructive-500 hover:text-destructive-600"
                  >
                    Clear
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={`w-full justify-between text-left font-normal rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor] ${
                    !getToDate() && 'text-muted-foreground'
                  } ${getToDate() ? 'border-secondary-500' : ''} ${!isDateRangeValid() ? 'border-destructive-500' : ''}`}
                  disabled={!filters.dateType}
                >
                  {getToDate() ? (
                    formatDateDDMonthYYYY(getToDate() as string)
                  ) : (
                    <span>To: DD/MM/YYYY</span>
                  )}
                  <CalendarIcon className="mr-2 h-4 w-4 !text-[--bodyTextColor]" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <div className="flex flex-col">
                  <Calendar
                    mode="single"
                    selected={
                      getToDate() ? new Date(getToDate() as string) : undefined
                    }
                    onSelect={(date) => handleToDateChange(date)}
                    initialFocus
                  />
                  <Button
                    variant="ghost"
                    onClick={() => handleToDateChange(undefined)}
                    className="text-destructive-500 hover:text-destructive-600"
                  >
                    Clear
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
        {!isDateRangeValid() && (
          <p className="text-destructive-500 text-[14px] mt-1">
            Start date must be earlier than or equal to end date
          </p>
        )}
      </div>

      <div className="flex gap-2 items-center">
        <Button
          variant="outline"
          onClick={handleResetFilters}
          disabled={isResetDisabled}
        >
          <ResetIcon />
          Reset
        </Button>

        <Button
          className="px-4"
          onClick={handleApplyFilters}
          disabled={!isDateRangeValid()}
        >
          Apply
        </Button>
      </div>

      {showAppliedFilters && appliedFilters.length > 0 && (
        <div className="mt-4">
          <p className="text-[16px] font-medium mb-2">Filters Applied:</p>
          <div className="flex flex-wrap gap-2">
            {appliedFilters.map((filter) => (
              <div
                key={filter.key}
                className="flex items-center bg-neutral-100 rounded-md px-3 py-1 text-[16px]"
              >
                <span className="font-medium mr-1">{filter.key}:</span>
                <span>{filter.value}</span>
                <button
                  className="ml-2 text-neutral-500 hover:text-neutral-700"
                  onClick={() => removeFilter(filter.key)}
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
