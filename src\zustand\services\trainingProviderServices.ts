/* eslint-disable @typescript-eslint/no-explicit-any */
import axiosClient from '@/utils/axiosClient';
import type { TrainingProviderRequest } from '@/types/courseType';

export const checkUserIsTrainer = async (userId: string): Promise<boolean> => {
  try {
    const response = await axiosClient.get(`/TrainingProvider/user/${userId}`);
    return !!response.data;
  } catch (error) {
    console.error('Error checking trainer status:', error);
    return false;
  }
};

export const checkTrainingProviderAccess = async (): Promise<boolean> => {
  try {
    const response = await axiosClient.get('/TrainingProvider');
    return response.data.hasAccess;
  } catch (error) {
    console.error('Error checking training provider access:', error);
    return false;
  }
};

export const requestTrainingProviderAccess = async (
  data: TrainingProviderRequest
): Promise<any> => {
  try {
    const response = await axiosClient.post('/TrainingProvider', data);
    return response.data;
  } catch (error) {
    console.error('Error requesting training provider access:', error);
    throw error;
  }
};

export const uploadAccreditation = async (file: File): Promise<any> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve({
        id: Date.now().toString(),
        name: file.name,
        url: reader.result as string,
      });
    };
    reader.onerror = (event) => {
      const error =
        event instanceof ProgressEvent && reader.error
          ? reader.error
          : new Error('File reading failed');
      reject(error);
    };
    reader.readAsDataURL(file);
  });
};
