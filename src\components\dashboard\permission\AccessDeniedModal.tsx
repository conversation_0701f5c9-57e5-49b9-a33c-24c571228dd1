'use client';

import type React from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Mail, RefreshCw, X } from 'lucide-react';
import { usePermission } from '@/hooks/usePermission';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

interface AccessDeniedModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  requiredPermission?: string | string[];
  showContactSupport?: boolean;
  showRefreshButton?: boolean;
  onRefreshComplete?: () => void;
  customActions?: React.ReactNode;
  variant?: 'default' | 'warning' | 'error';
}

export function AccessDeniedModal({
  isOpen,
  onClose,
  title = 'Access Restricted',
  description = "You don't have permission to perform this action.",
  requiredPermission,
  showContactSupport = true,
  showRefreshButton = true,
  onRefreshComplete,
  customActions,
  variant = 'default',
}: AccessDeniedModalProps) {
  const { refreshPermissions, isLoading } = usePermission();
  const { user } = useAuthStore();

  const handleRefreshPermissions = async () => {
    await refreshPermissions();
    onRefreshComplete?.();
  };

  const handleContactSupport = () => {
    const subject = encodeURIComponent('Permission Request');
    const body = encodeURIComponent(
      `Hello,\n\nI need access to a feature that requires the following permission(s):\n${
        Array.isArray(requiredPermission)
          ? requiredPermission.join(', ')
          : requiredPermission || 'Not specified'
      }\n\nUser: ${user?.fullName || user?.email || 'Unknown'}\n\nPlease review my access level.\n\nThank you.`
    );
    window.open(
      `mailto:<EMAIL>?subject=${subject}&body=${body}`,
      '_blank'
    );
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'warning':
        return {
          iconColor: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          alertVariant: 'default' as const,
        };
      case 'error':
        return {
          iconColor: 'text-red-600',
          bgColor: 'bg-red-100',
          alertVariant: 'destructive' as const,
        };
      default:
        return {
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-100',
          alertVariant: 'default' as const,
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className={`rounded-full p-2 ${styles.bgColor}`}>
              <Shield className={`h-5 w-5 ${styles.iconColor}`} />
            </div>
            <div>
              <DialogTitle className="text-left">{title}</DialogTitle>
              <DialogDescription className="text-left">
                {description}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* Required Permission Info */}
          {requiredPermission && (
            <Alert variant={styles.alertVariant}>
              <AlertDescription>
                <strong>Required permission(s):</strong>
                <br />
                <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">
                  {Array.isArray(requiredPermission)
                    ? requiredPermission.join(', ')
                    : requiredPermission}
                </code>
              </AlertDescription>
            </Alert>
          )}

          {/* User Info */}
          {user && (
            <div className="bg-gray-50 border rounded-lg p-3">
              <p className="text-sm text-gray-700">
                <strong>Current user:</strong> {user.fullName || user.email}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Contact your administrator if you believe you should have access
                to this feature.
              </p>
            </div>
          )}

          {/* Custom Actions */}
          {customActions && <div>{customActions}</div>}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <div className="flex flex-col sm:flex-row gap-2 w-full">
            {showRefreshButton && (
              <Button
                onClick={handleRefreshPermissions}
                variant="outline"
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                />
                {isLoading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}

            {showContactSupport && (
              <Button
                onClick={handleContactSupport}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Mail className="h-4 w-4" />
                Contact Support
              </Button>
            )}

            <Button onClick={onClose} className="flex items-center gap-2">
              <X className="h-4 w-4" />
              Close
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
