'use client';

import { Button } from '@/components/ui/button';
import { IterationCw, X } from 'lucide-react';

type FilterValues = {
  listingType: string;
  createdOnFrom: string;
  createdOnTo: string;
  closeDateFrom: string;
  closeDateTo: string;
};

type FilterModalProps = {
  show: boolean;
  filters: FilterValues;
  setFilters: (_filters: FilterValues) => void;
  onApply: (_filters: FilterValues) => void;
  onReset: () => void;
  onClose: () => void;
};

export function FilterModal({
  show,
  filters,
  setFilters,
  onApply,
  onReset,
  onClose,
}: FilterModalProps) {
  if (!show) return null;

  const handleResetFilters = () => {
    setFilters({
      listingType: '',
      createdOnFrom: '',
      createdOnTo: '',
      closeDateFrom: '',
      closeDateTo: '',
    });
    onReset();
    onClose();
  };

  const handleApplyFilters = () => {
    onApply(filters);
    onClose();
  };

  return (
    <div className="fixed inset-0 flex justify-center items-center bg-[#0000]/40 z-50">
      <div className="bg-white rounded-lg w-full max-w-md p-6 relative">
        <button
          className="absolute top-4 right-4 text-neutral-500 hover:text-black"
          onClick={onClose}
        >
          <X className="w-5 h-5" />
        </button>

        <h2 className="text-xl font-semibold mb-4">Filter:</h2>

        <div className="mb-4">
          <label
            htmlFor="listingType"
            className="block text-[16px] font-medium mb-1"
          >
            Listing Type
          </label>
          <select
            id="listingType"
            className="w-full border border-gray-300 rounded-md p-2"
            value={filters.listingType}
            onChange={(e) =>
              setFilters({ ...filters, listingType: e.target.value })
            }
          >
            <option value="">Select</option>
            <option value="Apprenticeship">Apprenticeship</option>
            <option value="Job">Job</option>
          </select>
        </div>

        <div className="mb-4">
          <label
            htmlFor="createdOn"
            className="block text-[16px] font-medium mb-1"
          >
            Created On
          </label>
          <div className="flex gap-2">
            <div className="flex-1">
              <div className="text-[16px] mb-1">From:</div>
              <input
                type="date"
                className="w-full border border-gray-300 rounded-md p-2"
                value={filters.createdOnFrom}
                onChange={(e) =>
                  setFilters({ ...filters, createdOnFrom: e.target.value })
                }
                placeholder="DD / MM / YYYY"
              />
            </div>
            <div className="flex-1">
              <div className="text-[16px] mb-1">To:</div>
              <input
                type="date"
                className="w-full border border-gray-300 rounded-md p-2"
                value={filters.createdOnTo}
                onChange={(e) =>
                  setFilters({ ...filters, createdOnTo: e.target.value })
                }
                placeholder="DD / MM / YYYY"
              />
            </div>
          </div>
        </div>

        <div className="mb-6">
          <label
            htmlFor="closeDate"
            className="block text-[16px] font-medium mb-1"
          >
            Close Date
          </label>
          <div className="flex gap-2">
            <div className="flex-1">
              <div className="text-[16px] mb-1">From:</div>
              <input
                type="date"
                className="w-full border border-gray-300 rounded-md p-2"
                value={filters.closeDateFrom}
                onChange={(e) =>
                  setFilters({ ...filters, closeDateFrom: e.target.value })
                }
                placeholder="DD / MM / YYYY"
              />
            </div>
            <div className="flex-1">
              <div className="text-[16px] mb-1">To:</div>
              <input
                type="date"
                className="w-full border border-gray-300 rounded-md p-2"
                value={filters.closeDateTo}
                onChange={(e) =>
                  setFilters({ ...filters, closeDateTo: e.target.value })
                }
                placeholder="DD / MM / YYYY"
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleResetFilters}
          >
            <IterationCw className="mr-2" /> Reset Filters
          </Button>

          <Button
            className="w-full bg-[#0F3A5D] hover:bg-[#0c2e4a]"
            onClick={handleApplyFilters}
          >
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
}
