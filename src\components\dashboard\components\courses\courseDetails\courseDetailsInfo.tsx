'use client';

import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { Course } from '@/types/courseType';

interface CourseDetailsInfoProps {
  course: Course;
}

export default function CourseDetailsInfo({ course }: CourseDetailsInfoProps) {
  const formattedPrice = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(course.courseFee || 0);

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const getExperienceLevelColor = () => {
    switch (course.experienceLevel) {
      case 'Beginner':
        return 'bg-success-100 text-neutral-900 hover:bg-success-200 rounded-lg border-none text-[16px]';
      case 'Intermediate':
        return 'bg-warning-100 text-neutral-900 hover:bg-yellow-200 rounded-lg border-none text-[16px] ';
      case 'Advanced':
        return 'bg-destructive-100 text-neutral-900 hover:bg-red-100 rounded-lg border-none text-[16px]';
      default:
        return 'bg-neutral-500 text-neutral-900 hover:bg-neutral-500 rounded-lg border-none text-[16px]';
    }
  };

  const getLocationString = () => {
    const parts = [];
    if (course.city) parts.push(course.city);
    if (course.state) parts.push(course.state);
    if (course.country) parts.push(course.country);
    return parts.join(', ');
  };

  const getAdditionalStartDates = () => {
    return [
      new Date(2025, 8, 15),
      new Date(2025, 9, 1),
      new Date(2025, 9, 15),
    ].map((date) =>
      date.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      })
    );
  };

  return (
    <div className="space-y-3">
      <div className="text-[24px] font-semibold text-neutral-700 leading-[32px] mt-4">
        USD {formattedPrice}
      </div>

      <div className="flex flex-wrap gap-4 text-[16px]">
        <Badge variant="outline" className={getExperienceLevelColor()}>
          {course.experienceLevel}
        </Badge>

        <Badge
          variant="outline"
          className="bg-neutral-100 text-neutral-900 rounded-lg text-[16px]"
        >
          {course.deliveryMode}
        </Badge>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className="bg-neutral-100 text-neutral-900 rounded-lg text-[16px]"
              >
                Next Start: {formatDate(course.startDate)}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="p-2">
                <p className="font-medium mb-1">All Start Dates:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>{formatDate(course.startDate)}</li>
                  {getAdditionalStartDates().map((date) => (
                    <li key={date}>{date}</li>
                  ))}
                </ul>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {!course.isSelfPaced && course.duration && course.durationUnit && (
          <Badge
            variant="outline"
            className="bg-neutral-100 text-neutral-900 rounded-lg text-[16px]"
          >
            {course.duration} {course.durationUnit}
          </Badge>
        )}

        {course.isSelfPaced && (
          <Badge
            variant="outline"
            className="bg-neutral-100 text-neutral-900 rounded-lg text-[16px]"
          >
            Self-paced
          </Badge>
        )}
      </div>

      {(course.deliveryMode === 'In Person' ||
        course.deliveryMode === 'Hybrid') &&
        getLocationString() && (
          <div className="flex items-center">
            <span className="text-neutral-500 font-normal text-[16px] leading-[24px] capitalize">
              Location: {getLocationString()}
            </span>
          </div>
        )}
    </div>
  );
}
