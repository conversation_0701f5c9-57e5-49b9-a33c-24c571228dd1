# Docker Setup for Career Navigator Pro Partner UI

This document provides instructions for running the Career Navigator Pro Partner UI application using Docker.

## Prerequisites

- Docker Engine 20.10.0 or later
- Docker Compose 2.0.0 or later

## Quick Start

### Production Build

1. **Build and run the production container:**
   ```bash
   docker-compose up --build
   ```

2. **Run in detached mode:**
   ```bash
   docker-compose up -d --build
   ```

3. **Access the application:**
   Open your browser and navigate to `http://localhost:3000`

### Development Build

1. **Build and run the development container with hot reloading:**
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

2. **Run in detached mode:**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d --build
   ```

## Environment Variables

1. **Copy the environment template:**
   ```bash
   cp .env.example .env.local
   ```

2. **Edit the environment variables as needed:**
   ```bash
   # Edit .env.local with your specific configuration
   nano .env.local
   ```

## Docker Commands

### Building Images

```bash
# Build production image
docker build -t career-navigator-ui .

# Build development image
docker build -f Dockerfile.dev -t career-navigator-ui:dev .
```

### Running Containers

```bash
# Run production container
docker run -p 3000:3000 --env-file .env.local career-navigator-ui

# Run development container with volume mounting
docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules --env-file .env.local career-navigator-ui:dev
```

### Managing Containers

```bash
# Stop all containers
docker-compose down

# Stop and remove volumes
docker-compose down -v

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f career-navigator-ui
```

## Health Check

The application includes a health check endpoint at `/api/health` that returns:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "environment": "production"
}
```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Change the port mapping in docker-compose.yml
   ports:
     - "3001:3000"  # Use port 3001 instead
   ```

2. **Permission issues on Linux/macOS:**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

3. **Node modules issues:**
   ```bash
   # Remove node_modules and rebuild
   docker-compose down
   docker-compose build --no-cache
   docker-compose up
   ```

### Debugging

1. **Access container shell:**
   ```bash
   docker-compose exec career-navigator-ui sh
   ```

2. **View container logs:**
   ```bash
   docker-compose logs career-navigator-ui
   ```

3. **Inspect container:**
   ```bash
   docker inspect career-navigator-pro-partner-ui
   ```

## Production Deployment

For production deployment, consider:

1. **Using a reverse proxy (nginx):**
   ```yaml
   # Add to docker-compose.yml
   nginx:
     image: nginx:alpine
     ports:
       - "80:80"
       - "443:443"
     volumes:
       - ./nginx.conf:/etc/nginx/nginx.conf
   ```

2. **Setting up SSL certificates**
3. **Configuring environment-specific variables**
4. **Setting up monitoring and logging**

## Performance Optimization

The Docker setup includes several optimizations:

- Multi-stage build to reduce image size
- Standalone Next.js output for minimal runtime
- Non-root user for security
- Health checks for container monitoring
- Proper layer caching for faster builds

## Security Considerations

- The application runs as a non-root user (nextjs:nodejs)
- Sensitive files are excluded via .dockerignore
- Environment variables should be properly secured
- Consider using Docker secrets for sensitive data in production
