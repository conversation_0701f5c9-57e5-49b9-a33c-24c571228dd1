/* eslint-disable @typescript-eslint/no-explicit-any */
import type { StaticImageData } from 'next/image';

export interface UserProfile {
  id: string;
  name: string;
  location: string;
  photo: StaticImageData;
  citizenDetails: {
    userId: string;
    registrationDate: string;
    age: number;
    gender: string;
    nationality: string;
    nationalId: string;
    email: string;
    mobile: string;
  };
  employmentDetails: {
    status: string;
    type: string;
    startDate: string;
    employer: string;
    position: string;
    category: string;
    monthlySalary: number;
    pensionableSalary: number;
  };
  benefits: {
    monthlyBenefits: number;
    lastProcessedDate: string;
    totalBenefits: number;
    eligibility: string;
    status: string;
  };
}

export interface CompanyDetails {
  id: number;
  name: string;
  location: string;
  companyId: string;
  registrationDate: string;
  industry: string;
  companySize: string;
  companyAddress: string;
  postCode: string;
  email: string;
}

export interface Representative {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  status?: 'active' | 'inactive' | 'invite-sent';
  isActive?: boolean;
  permissions: {
    Full_Admin_Access: boolean;
    career: boolean;
    training: boolean;
    finance: boolean;
  };
}

export interface Benefit {
  monthlyBenefits: number;
  lastPaymentDate: string;
  TotalBenefitsReceived: string;
  CompanyEligibility: string;
}

export interface HiringInformation {
  hiringStatus: string;
  activeJobPosts: number;
  hiredOnPlatfom: number;
}

export interface JobPosition {
  id: number;
  position: string;
  company_name: string;
  listing_type: string;
  job_id: string;
  applicants: string;
  new_applicants: string;
  created_on: string;
  closed_date: string;
}

export interface IRegisterProps {
  email: string;
  password: string;
  // userName?: string
  fullName?: string;
  role: 'partner';
}

export interface ILoginProps {
  email: string;
  password: string;
  role: 'partner';
}

export interface IUser {
  id: string;
  email: string;
  // username?: string
  fullName?: string;
  phone?: string;
  role?: string;
  emailverified: boolean;
}

export interface AuthState {
  user: IUser | null;
  token: string | null;
  isloading: boolean;
  showLoader: boolean;
  register: (_credentials: IRegisterProps) => Promise<void>;
  login: (_credentials: ILoginProps) => Promise<void>;
  logout: () => void;
  setUser: (_user: IUser) => void;
  role: string;
}

export interface Applicant {
  applicantId: string;
  id: string;
  userId: string;
  jobId: string;
  appliedDate: string;
  status: 'Pending' | 'Shortlisted' | 'Contacted' | 'Rejected' | 'Hired';
  attributes: any[];
  cv: {
    id: string;
    base64Content: string;
    fileName: string;
  };
  job: {
    id: string;
    title: string;
    description: string;
    location: string;
    monthlyFrom: number;
    monthlyTo: number;
    expiryDate: string;
    jobType: string;
    jobMode: string;
    jobPosition: string;
    industry: string;
    experienceLevel: string;
    companyName: string;
    currency: string;
    createdById: string;
    lastModifiedById: string;
  };
  user: {
    fullName: string;
    employmentStatus: string | null;
    educationLevel: string | null;
    experienceLevel: string | null;
    industry: string | null;
    specialization: string;
    subSpecialization: string;
    attributes: any[];
    cVs: any[];
    id: string;
    userName: string;
    normalizedUserName: string;
    email: string;
    normalizedEmail: string;
    emailConfirmed: boolean;
    passwordHash: string;
    securityStamp: string;
    concurrencyStamp: string;
    phoneNumber: string | null;
    phoneNumberConfirmed: boolean;
    twoFactorEnabled: boolean;
    lockoutEnd: string | null;
    lockoutEnabled: boolean;
    accessFailedCount: number;
  };
}

export interface ApplicantResponse {
  data: Applicant[];
  page: number;
  size: number;
  total: number;
  statusCounts?: {
    Pending: number;
    Shortlisted: number;
    Contacted: number;
    Hired: number;
    Rejected: number;
  };
  jobTitles?: [];
}

export interface StatusCounts {
  Pending: number;
  Shortlisted: number;
  Contacted: number;
  Hired: number;
  Rejected: number;
}

export interface getAdminValuesProps {
  page?: number;
  pageSize?: number;
  language?: string;
  status?: string;
  category?: string;
  subcategory?: string;
}

export interface AdminValue {
  id: string;
  category: string;
  subCategory: string | null;
  value: string;
  label: string;
  language: string;
  status: string;
  comment: string | null;
  path: string | null;
  createdById: string;
  createdBy: string | null;
  lastModifiedById: string;
  lastModifiedBy: string | null;
}

// types/IPartner.ts
export interface IPartner {
  name?: string;
  partnerId?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  industry?: string;
  size?: string;
  type?: number;
  status?: string;
  logo?: string;
  cover?: string;
  socialMediaLinks?: string;
  country?: string;
  state?: string;
  addressLine1?: string;
  city?: string;
  postCode?: string;
  mailAddressLine1?: string;
  mailCity?: string;
  mailPostCode?: string;
  createdById?: string;
  lastModifiedById?: string;
  privacyPolicyAccepted?: boolean;
  termsAccepted?: boolean;
}
export interface PartnerState {
  partner: IPartner | null;
  isLoading: boolean;
  showLoader: boolean;
}
export interface IJob {
  id: string;
  title: string;
  description: string;
  location: string;
  monthlyFrom: number;
  monthlyTo: number;
  currency: string;
  postedDate: string;
  expiryDate: string;
  startDate: string;
  companyName: string;
  jobType: string;
  jobMode: string;
  jobPosition: string;
  industry: string;
  experienceLevel: string;
}

export interface Topic {
  id: string;
  value: string;
  label: string;
}

export interface IndustryOption {
  id: string;
  value: string;
  label: string;
}

export interface ApplicantFilters {
  name?: string;
  position?: string;
  appliedOnFrom?: string;
  appliedOnTo?: string;
}

// settings types
export interface ISettings {
  id: string;
  platformLogo: string;
  platformLogoDark: string;
  governmentEmblem: string;
  governmentEmblemDark: string;
  brandColor: string;
  topBarBgColor: string;
  topBarButtonColor: string;
  navMenuColor: string;
  navMenuPositionTop: boolean;
  headingFont: string;
  headingTextColor: string;
  bodyTextFont: string;
  bodyTextColor: string;
  buttonStyle: string;
  buttonColor: string;
  bgStyleColor: string;
  bgStyleImg: string;
  isBgStyleColor: boolean;
  overlayColor: string;
  overlayPercentage: string;
  isDefault: boolean;
}

export interface IGetPartners {
  page: number;
  pageSize: number;
  search?: string;
  sortField?: string;
  filter?: string;
  status?: string;
  userId?: string;
  HiringStatus?: string;
  industry?: string;
  partnerId?: string;
  ascending?: boolean;
  id?: string;
  enableQuery?: boolean;
  addressLine1?: string;
  mailAddressLine1?: string;
  addressLine2?: string;
  city?: string;
  country?: string;
  createdBy?: string;
  createdById?: string;
  createdOn?: string;
  description?: string;
  email?: string;
  lastModifiedBy?: string;
  lastModifiedById?: string;
  logo?: string;
  mailCity?: string;
  mailPostCode?: string;
  modifiedOn?: string;
  name?: string;
  phone?: string;
  postCode?: string;
  size?: string;
  socialMediaLinks?: string;
  state?: string;
}

export interface InviteRepresentativePayload {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
}

export interface ApiRepresentative {
  id: string;
  userId: string;
  user: {
    fullName: string;
    email: string;
    emailConfirmed: boolean;
  };
  partnerId: string;
  partner: {
    name: string;
  };
  role: string;
  isActive?: boolean;
  createdOn: string;
  modifiedOn: string;
}

export interface UpdateUserProfileData {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: string;
}
