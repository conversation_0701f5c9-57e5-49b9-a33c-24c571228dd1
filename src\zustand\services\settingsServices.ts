import axiosClient from '@/utils/axiosClient';
import type { ISettings } from '@/type';

const API_URL = '/AppearanceSettings';

const SettingServices = {
  updateSettings: async (
    id: string,
    updatedSettings: Partial<ISettings>
  ): Promise<ISettings> => {
    const response = await axiosClient.put<ISettings>(
      `${API_URL}/${id}`,
      updatedSettings
    );
    return response.data;
  },

  getSettings: async (): Promise<ISettings> => {
    const response = await axiosClient.get<ISettings>(`${API_URL}`);
    return response.data;
  },
};

export default SettingServices;
