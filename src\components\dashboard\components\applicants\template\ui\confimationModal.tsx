'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText: string;
  confirmVariant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  applicantName: string;
  positionName?: string;
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText,
  confirmVariant = 'default',
  applicantName,
  positionName,
}: ConfirmationModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[32rem]">
        <DialogHeader>
          <DialogTitle className=" text-[24px] text-neutral-900 font-semibold leading-[32px]">
            {title}
          </DialogTitle>
          <button
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          ></button>
        </DialogHeader>
        <DialogDescription className=" text-[18px] text-neutral-500 font-normal leading-[28px]">
          {description}
        </DialogDescription>
        <div className="py-2">
          <ul className="list-disc pl-10 space-y-1 text-[18px] font-semibold leading-[28px] text-neutral-900">
            <li>
              {applicantName}
              {positionName && ` for the position ${positionName}`}
            </li>
          </ul>
        </div>
        <DialogFooter className="flex sm:justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant={confirmVariant}
            onClick={onConfirm}
            className={
              confirmVariant === 'destructive'
                ? 'bg-destructive-500 rounded-full hover:bg-destructive-600'
                : ''
            }
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
