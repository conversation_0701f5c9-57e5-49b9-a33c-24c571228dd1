import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { Course, CourseFormStep, SkillsCovered } from '@/types/courseType';
import { DELIVERY_MODES, DURATION_UNITS } from '@/types/courseType';

interface CourseFormState {
  // Current step
  currentStep: CourseFormStep;

  course: Omit<Course, 'sessions'>;

  skillsCovered: SkillsCovered[];

  isDirty: boolean;
  isSubmitting: boolean;
  startDates: string[];

  setCurrentStep: (_step: CourseFormStep) => void;
  updateCourse: (_partialCourse: Partial<Omit<Course, 'sessions'>>) => void;
  addProgramOutline: () => void;
  updateProgramOutline: (_index: number, _key: string, _value: string) => void;
  removeProgramOutline: (_index: number) => void;
  addStartDate: (_date: string) => void;
  removeStartDate: (_index: number) => void;
  addSkillCovered: (_skill: SkillsCovered) => void;
  removeSkillCovered: (_skillKey: string) => void;
  setIsDirty: (_isDirty: boolean) => void;
  setIsSubmitting: (_isSubmitting: boolean) => void;
  resetForm: () => void;
  validateStep: (_step: CourseFormStep) => {
    isValid: boolean;
    errors: string[];
  };
}

const initialCourse: Omit<Course, 'sessions'> = {
  name: '',
  description: '',
  coverImage: '',
  mainTopic: '',
  subTopic: '',
  learningObjective: '',
  courseLink: '',
  deliveryMode: DELIVERY_MODES[0],
  address: '',
  city: '',
  state: '',
  country: '',
  duration: '',
  durationUnit: DURATION_UNITS[2],
  isSelfPaced: false,
  isFlexible: false,
  experienceLevel: '',
  startDate: '',
  endDate: '',
  courseFee: 0,
  programOutlines: [
    { key: '', value: '' },
    // { key: '', value: '' },
  ],
  skillsCovered: [],
  createdById: '',
  lastModifiedById: '',
};

export const useCourseFormStore = create<CourseFormState>()(
  devtools(
    persist(
      (set, get) => ({
        currentStep: 'summary',
        course: { ...initialCourse },
        skillsCovered: [],
        isDirty: false,
        isSubmitting: false,
        startDates: [],

        // Actions
        setCurrentStep: (step) => set({ currentStep: step }),

        updateCourse: (partialCourse) =>
          set((state) => ({
            course: { ...state.course, ...partialCourse },
            isDirty: true,
          })),

        addProgramOutline: () =>
          set((state) => {
            const newOutlines = [...state.course.programOutlines];
            if (newOutlines.length >= 30) {
              alert('Maximum of 30 program outline sections allowed');
              return state;
            }

            const sectionNumber = newOutlines.length + 1;
            newOutlines.push({ key: `Section ${sectionNumber}`, value: '' });

            return {
              course: { ...state.course, programOutlines: newOutlines },
              isDirty: true,
            };
          }),

        updateProgramOutline: (index, key, value) =>
          set((state) => {
            const newOutlines = [...state.course.programOutlines];
            newOutlines[index] = { ...newOutlines[index], key, value };

            return {
              course: { ...state.course, programOutlines: newOutlines },
              isDirty: true,
            };
          }),

        removeProgramOutline: (index) =>
          set((state) => {
            const newOutlines = [...state.course.programOutlines];
            newOutlines.splice(index, 1);

            return {
              course: { ...state.course, programOutlines: newOutlines },
              isDirty: true,
            };
          }),

        addStartDate: (date) =>
          set((state) => {
            const newDates = [...state.startDates];
            if (newDates.length >= 10) {
              alert('Maximum of 10 start dates allowed');
              return state;
            }

            newDates.push(date);

            return {
              startDates: newDates,
              isDirty: true,
            };
          }),

        removeStartDate: (index) =>
          set((state) => {
            const newDates = [...state.startDates];
            newDates.splice(index, 1);

            return {
              startDates: newDates,
              isDirty: true,
            };
          }),

        addSkillCovered: (skill) =>
          set((state) => {
            if (state.skillsCovered.some((s) => s.key === skill.key)) {
              return state;
            }
            if (state.skillsCovered.length >= 100) {
              alert('Maximum of 100 skills allowed');
              return state;
            }

            const newSkillsCovered = [...state.skillsCovered, skill];
            return {
              skillsCovered: newSkillsCovered,
              course: { ...state.course, skillsCovered: newSkillsCovered },
              isDirty: true,
            };
          }),

        removeSkillCovered: (skillKey) =>
          set((state) => {
            const newSkillsCovered = state.skillsCovered.filter(
              (skill) => skill.key !== skillKey
            );
            return {
              skillsCovered: newSkillsCovered,
              course: { ...state.course, skillsCovered: newSkillsCovered },
              isDirty: true,
            };
          }),

        setIsDirty: (isDirty) => set({ isDirty }),

        setIsSubmitting: (isSubmitting) => set({ isSubmitting }),

        resetForm: () =>
          set({
            course: { ...initialCourse },
            skillsCovered: [],
            startDates: [],
            currentStep: 'summary',
            isDirty: false,
            isSubmitting: false,
          }),

        validateStep: (step) => {
          const { course } = get();
          const errors: string[] = [];

          if (step === 'summary') {
            if (!course.name) errors.push('Course name is required');
            if (!course.mainTopic) errors.push('Main topic is required');
            if (!course.description)
              errors.push('Course description is required');
            if (!course.learningObjective)
              errors.push('Learning objectives are required');

            course.programOutlines.forEach((outline, index) => {
              if (!outline.key)
                errors.push(`Section ${index + 1} title is required`);
              if (!outline.value)
                errors.push(`Section ${index + 1} description is required`);
            });
          }

          if (step === 'details') {
            if (!course.coverImage) errors.push('Course Image is required');
            if (!course.courseLink) errors.push('Course link is required');
            if (!course.deliveryMode) errors.push('Delivery type is required');

            if (
              (course.deliveryMode === 'In Person' ||
                course.deliveryMode === 'Hybrid') &&
              !course.city
            ) {
              errors.push('City is required for in-person and hybrid courses');
            }

            if (
              !course.isFlexible &&
              !course.startDate &&
              get().startDates.length === 0
            ) {
              errors.push('At least one start date is required');
            }
            if (
              !course.isSelfPaced &&
              (!course.duration || !course.durationUnit)
            ) {
              errors.push('Duration is required');
            }

            if (!course.experienceLevel)
              errors.push('Experience level is required');
            if (course.courseFee === undefined || course.courseFee === null) {
              errors.push('Course Fee is required');
            }
          }

          return {
            isValid: errors.length === 0,
            errors,
          };
        },
      }),
      {
        name: 'course-form-storage',
        partialize: (state) => ({
          course: state.course,
          skillsCovered: state.skillsCovered,
          startDates: state.startDates,
          currentStep: state.currentStep,
        }),
      }
    )
  )
);
