import type { Applicant } from '@/type';
import { create } from 'zustand';

interface ApplicantListingState {
  search: string;
  selectedIds: string[];
  activeTab: Applicant['status'];
  filters: {
    name: string;
    position: string;
    appliedOnFrom: string;
    appliedOnTo: string;
  };
  page: number;
  size: number;
  sortField: string | null;
  ascending: boolean;
}

interface ApplicantStore {
  state: ApplicantListingState;
  updateState: (_updates: Partial<ApplicantListingState>) => void;
  resetState: () => void;
}

const initialState: ApplicantListingState = {
  search: '',
  selectedIds: [],
  activeTab: 'Pending',
  filters: {
    name: '',
    position: '',
    appliedOnFrom: '',
    appliedOnTo: '',
  },
  page: 1,
  size: 10,
  sortField: null,
  ascending: true,
};

export const useApplicantStore = create<ApplicantStore>((set) => ({
  state: initialState,
  updateState: (updates) =>
    set((store) => ({
      state: {
        ...store.state,
        ...updates,
        filters: updates.filters
          ? { ...store.state.filters, ...updates.filters }
          : store.state.filters,
        selectedIds: updates.selectedIds
          ? [...(updates.selectedIds || [])]
          : store.state.selectedIds,
      },
    })),
  resetState: () => set({ state: initialState }),
}));
