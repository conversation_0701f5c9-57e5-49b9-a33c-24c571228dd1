import { useEffect, useRef, useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface SelectAllCheckboxProps {
  allSelected: boolean;
  isIndeterminate: boolean;
  currentPageCount: number;
  totalCount: number;
  onSelectPage: () => void;
  onSelectAll: () => void;
  className?: string;
}

export function SelectAllCheckbox({
  allSelected,
  isIndeterminate,
  currentPageCount,
  totalCount,
  onSelectPage,
  onSelectAll,
  className,
}: SelectAllCheckboxProps) {
  const [open, setOpen] = useState(false);
  const checkboxRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = isIndeterminate;
    }
  }, [isIndeterminate]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <div
        className={`relative ${className || ''}`}
        role="button"
        tabIndex={0}
        aria-haspopup="menu"
        aria-expanded={open}
        onMouseEnter={() => setOpen(true)}
        onMouseLeave={() => setOpen(false)}
        onFocus={() => setOpen(true)}
        onBlur={() => setOpen(false)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setOpen((prev) => !prev);
          } else if (e.key === 'Escape') {
            setOpen(false);
          }
        }}
        onTouchStart={() => setOpen(true)}
      >
        <PopoverTrigger asChild>
          <div>
            <Checkbox
              checked={allSelected}
              onCheckedChange={onSelectPage}
              aria-label="Select all"
              ref={(el) => {
                if (el) {
                  checkboxRef.current = el as HTMLInputElement;
                }
              }}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start" sideOffset={5}>
          <div className="flex flex-col py-1">
            <button
              className="px-4 py-2 text-sm text-left hover:bg-neutral-100 transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onSelectPage();
                setOpen(false);
              }}
            >
              Select all on this page ({currentPageCount})
            </button>
            <button
              className="px-4 py-2 text-sm text-left hover:bg-neutral-100 transition-colors"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onSelectAll();
                setOpen(false);
              }}
            >
              Select entire list ({totalCount})
            </button>
          </div>
        </PopoverContent>
      </div>
    </Popover>
  );
}
