'use client';

import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { JobTableHeaders } from './table-header';
import type { Job } from '@/type/jobType';
import { formatDate } from '@/utils';

type JobTableProps = {
  jobs: Job[];
  sortField: string | null;
  ascending: boolean;
  handleSort: (_field: string) => void;
  activeTab: 'active' | 'closed';
};

export function JobTable({
  jobs,
  sortField,
  ascending,
  handleSort,
}: JobTableProps) {
  const router = useRouter();

  return (
    <div className="relative overflow-x-auto scrollbar-hide">
      <div className="min-w-[1000px]">
        <Table>
          <JobTableHeaders
            sortField={sortField}
            ascending={ascending}
            handleSort={handleSort}
          />
          <TableBody>
            {jobs.length > 0 ? (
              jobs.map((job) => (
                <TableRow
                  key={job.id}
                  className="cursor-pointer hover:bg-neutral-50 transition"
                  onClick={() =>
                    router.push(
                      `/dashboard/jobs-and-training/my-listings/${job.id}`
                    )
                  }
                >
                  <TableCell>{job.title}</TableCell>
                  <TableCell>{job.listingType || 'Job'}</TableCell>
                  <TableCell>
                    {typeof job?.totalApplicants === 'number'
                      ? job.totalApplicants
                      : 'N/A'}
                    {typeof job?.newApplicants === 'number' && (
                      <Badge
                        className="ml-1 bg-primary-50 rounded-md text-primary-500 text-[14px] font-medium leading-[16px]"
                        variant="secondary"
                      >
                        {job.newApplicants} New
                      </Badge>
                    )}
                  </TableCell>

                  <TableCell>{formatDate(job.startDate)}</TableCell>
                  <TableCell>{formatDate(job.expiryDate)}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <p className="text-neutral-500">
                    No Job found for this status
                  </p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
