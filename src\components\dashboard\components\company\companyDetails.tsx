import { normalizeText } from '@/utils';

interface CompanyDetailsProps {
  companyDetails: {
    id: string;
    partnerId: string;
    registrationDate: string;
    industry: string;
    companySize: string;
    companyAddress: string;
    postCode: string;
    email: string;
  };
  hiringInformation: {
    hiringStatus: string;
    activeJobPosts: number | undefined;
    hiredOnPlatfom: string;
  };
  benefits: {
    monthlyBenefits: string;
    lastPaymentDate: string;
    TotalBenefitsReceived: string;
    CompanyEligibility: string;
  };
}

export function CompanyDetails({
  companyDetails,
  hiringInformation,
  benefits,
}: CompanyDetailsProps) {
  return (
    <div className="space-y-6">
      {/* Company Details Section */}
      <div className="space-y-4">
        <h2 className="text-[18px] font-semibold text-neutral-900 leading-[28px]">
          Company Details
        </h2>
        <div className="space-y-2">
          <DetailRow
            label="Company ID:"
            value={companyDetails?.partnerId || 'N/A'}
          />
          <DetailRow
            label="Registration Date:"
            value={companyDetails?.registrationDate || 'N/A'}
          />
          <DetailRow
            label="Industry:"
            value={normalizeText(companyDetails?.industry || 'N/A')}
          />
          <DetailRow
            label="Company Size:"
            value={companyDetails?.companySize || 'N/A'}
          />
          <DetailRow
            label="Company Address:"
            value={companyDetails?.companyAddress || 'N/A'}
          />
          <DetailRow
            label="Post Code:"
            value={companyDetails?.postCode || 'N/A'}
          />
          <DetailRow label="Email:" value={companyDetails?.email || 'N/A'} />
        </div>
      </div>

      {/* Hiring Information Section */}
      <div className="space-y-4 pt-4 border-t border-neutral-200">
        <h2 className="text-[18px] font-semibold text-neutral-900 leading-[28px]">
          Hiring Information
        </h2>
        <div className="space-y-2">
          <DetailRow
            label="Hiring Status:"
            value={hiringInformation?.hiringStatus || 'N/A'}
          />
          <DetailRow
            label="Active Job Posts:"
            value={hiringInformation?.activeJobPosts?.toString() || '0'}
          />
          <DetailRow
            label="Hired on Platform:"
            value={hiringInformation?.hiredOnPlatfom || '0'}
          />
        </div>
      </div>

      {/* Benefits Section */}
      <div className="space-y-4 pt-4 border-t border-neutral-200">
        <h2 className="text-[18px] font-semibold text-neutral-900 leading-[28px]">
          Benefits
        </h2>
        <div className="space-y-2">
          <DetailRow
            label="Monthly Benefits:"
            value={benefits?.monthlyBenefits || '$1,000'}
          />
          <DetailRow
            label="Last Payment Date:"
            value={benefits?.lastPaymentDate || '25 Jan 2025'}
          />
          <DetailRow
            label="Total Benefits Received:"
            value={benefits?.TotalBenefitsReceived || '$25,000'}
          />
          <DetailRow
            label="Benefits This Company Is Eligible For:"
            value={
              benefits?.CompanyEligibility ||
              'Allowance xyz, Subsidy abc, Fund def'
            }
          />
        </div>
      </div>
    </div>
  );
}

function DetailRow({ label, value }: { label: string; value: string }) {
  return (
    <div className="flex justify-between items-center py-1">
      <span className="text-neutral-500 font-medium">{label}</span>
      <span className="text-neutral-500 capitalize">{value}</span>
    </div>
  );
}
