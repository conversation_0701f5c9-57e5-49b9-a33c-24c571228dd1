export type Permission =
  | 'view:dashboard'
  | 'manage:users'
  | 'edit:profile'
  | 'view:reports'
  | 'create:reports'
  | 'delete:reports'
  | 'manage:settings'
  | 'view:admin'
  | 'manage:representatives'
  | 'invite:representatives'
  | 'view:finance'
  | 'manage:courses'
  | 'view:analytics';

// Response from the permissions API
export interface PermissionsResponse {
  success: boolean;
  message: string;
  data: {
    permissions: Permission[];
  };
}

// Permission check options
export interface PermissionCheckOptions {
  all?: boolean; // If true, user must have all permissions, otherwise any one is sufficient
}
