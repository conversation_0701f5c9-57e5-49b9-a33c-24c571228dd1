'use client';

import type { ReactNode, AnchorHTMLAttributes } from 'react';
import Link from 'next/link';
import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';

interface PermissionLinkProps
  extends Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
  permission: Permission | Permission[];
  href: string;
  children: ReactNode;
  options?: PermissionCheckOptions;
  fallback?: ReactNode;
  hideWhenNoPermission?: boolean;
  className?: string;
}

/**
 * Link component that conditionally renders based on permissions
 */
export function PermissionLink({
  permission,
  href,
  children,
  options,
  fallback = null,
  hideWhenNoPermission = true,
  className,
  ...linkProps
}: PermissionLinkProps) {
  const { checkPermission, isLoading } = usePermission();

  const hasPermission = checkPermission(permission, options);

  // Show loading state
  if (isLoading) {
    return (
      <span className={`${className} opacity-50`}>
        <div className="animate-pulse">{children}</div>
      </span>
    );
  }

  // Hide link completely if no permission and hideWhenNoPermission is true
  if (!hasPermission && hideWhenNoPermission) {
    return <>{fallback}</>;
  }

  // Show disabled link if no permission but hideWhenNoPermission is false
  if (!hasPermission) {
    return (
      <span
        className={`${className} opacity-50 cursor-not-allowed`}
        title="You don't have permission to access this"
      >
        {children}
      </span>
    );
  }

  return (
    <Link href={href} className={className} {...linkProps}>
      {children}
    </Link>
  );
}
