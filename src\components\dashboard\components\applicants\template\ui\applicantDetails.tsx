import type { Applicant } from '@/type';

interface ApplicantDetailsProps {
  applicant: Applicant;
}

export default function ApplicantDetails({ applicant }: ApplicantDetailsProps) {
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date
      .toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })
      .replace(/ /g, ' ');
  };

  return (
    <div className="max-w-[480px] space-y-3">
      <h2 className="text-[20px] font-semibold mb-4 text-neutral-900 leading-[28px] -tracking-[0.4px]">
        Details
      </h2>
      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-neutral-500 text-[16px] font-semibold leading-[24px]">
            Applied Date:
          </span>
          <span className="text-neutral-500 text-[16px] font-normal leading-[24px]">
            {applicant.appliedDate ? formatDate(applicant.appliedDate) : 'N/A'}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-neutral-500 text-[16px] font-semibold leading-[24px]">
            Email:
          </span>
          <span className="text-neutral-500 text-[16px] font-normal leading-[24px]">
            {applicant.user?.email || 'N/A'}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-neutral-500 text-[16px] font-semibold leading-[24px]">
            Mobile:
          </span>
          <span className="text-neutral-500 text-[16px] font-normal leading-[24px]">
            {applicant.user?.phoneNumber || '+965 5000 1234'}
          </span>
        </div>
      </div>
    </div>
  );
}
