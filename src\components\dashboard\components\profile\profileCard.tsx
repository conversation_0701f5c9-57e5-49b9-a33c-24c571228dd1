'use client';

import { Upload } from 'lucide-react';
import { usePartnerProfile } from '@/queries';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { Label } from '@radix-ui/react-dropdown-menu';
import { Input } from '@/components/ui/input';
import { useUpdatePartnerProfile } from '@/mutations';

const excludedFields = [
  'id',
  'partnerId',
  'logo',
  'attributes',
  'createdById',
  'createdBy',
  'lastModifiedById',
  'createdOn',
  'modifiedOn',
  'lastModifiedBy',
  'passwordHash',
  'securityStamp',
  'concurrencyStamp',
];

function ProfileCard() {
  const renderProfileFields = (
    partner: Record<string, string | number | null | undefined>
  ) => {
    return Object.entries(partner)
      .filter(([key, value]) => !excludedFields.includes(key) && value)
      .map(([key, value]) => (
        <div key={key} className="mb-4 space-y-1">
          <Label className="capitalize text-[16px] text-neutral-900 font-medium leading-[28px]">
            {key === 'name' ? 'Company Name' : key.replace(/([A-Z])/g, ' $1')}:
          </Label>
          <Input className="text-neutral-500" value={value || ''} readOnly />
        </div>
      ));
  };

  const { data: profile, isLoading, refetch } = usePartnerProfile();
  const {
    updatePartnerProfile,
    isLoading: isUploading,
    isError,
    error,
  } = useUpdatePartnerProfile();

  const handleButtonClick = () => {
    const fileInput = document.getElementById(
      'file-upload'
    ) as HTMLInputElement;
    fileInput?.click();
  };

  const handlePdfUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files ? event.target.files[0] : null;
    if (file) {
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64String = reader.result as string;
        const updatedProfile = {
          ...profile?.data?.items[0]?.partner,
          LastModifiedBy: undefined,
          CreatedBy: undefined,
          logo: base64String,
        };
        updatePartnerProfile({
          partner: updatedProfile,
          id: profile?.data?.items[0]?.partner?.id,
        });
        refetch();
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <section className="bg-white mx-auto rounded-lg p-6 mt-10 h-full">
      <div className="">
        <h6 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
          {!profile?.data?.items[0]?.partner?.name
            ? 'My Company'
            : profile?.data?.items[0]?.partner?.name}
        </h6>

        <div className="rounded-lg py-4">
          <h2 className=" text-[18px] font-semibold leading-[28px] text-neutral-900 mb-4">
            Company Logo:
          </h2>
          {profile?.data?.items[0]?.partner?.logo ? (
            profile.data.items[0].partner.logo.startsWith('data:image') ||
            profile.data.items[0].partner.logo.startsWith('http') ? (
              <Image
                src={profile.data.items[0].partner.logo}
                alt="Company Logo"
                width={100}
                height={100}
                className="mb-3"
              />
            ) : (
              <p className="text-red-500">Invalid logo format</p>
            )
          ) : (
            <p className="mb-4 text-[16px] font-normal leading-[18px] tracking-wider text-[#646A77]">
              Upload your logo.
            </p>
          )}

          <input
            type="file"
            accept="image/*"
            onChange={handlePdfUpload}
            style={{ display: 'none' }}
            id="file-upload"
          />
          <label htmlFor="file-upload">
            <Button
              variant="outline"
              className="flex p-3 w-fit gap-2"
              onClick={handleButtonClick}
              disabled={isUploading}
            >
              {isUploading ? (
                <p>Uploading...</p>
              ) : (
                <>
                  <Upload />
                  <p className="!text-[--buttonColor]">
                    {!profile?.data?.items[0]?.partner?.logo
                      ? 'Choose Image'
                      : 'Choose Another Image'}
                  </p>
                </>
              )}
            </Button>
          </label>
          {isError && (
            <p className="text-red-500 mt-2">
              {error?.message || 'An error occurred while uploading the logo.'}
            </p>
          )}
        </div>
      </div>
      <div className="">
        {isLoading ? (
          <p>Loading...</p>
        ) : profile.data?.items?.length > 0 ? (
          <>
            <div className="">
              {renderProfileFields(profile?.data?.items[0]?.partner)}
            </div>
          </>
        ) : (
          <p>No data found</p>
        )}
      </div>
    </section>
  );
}

export default ProfileCard;
