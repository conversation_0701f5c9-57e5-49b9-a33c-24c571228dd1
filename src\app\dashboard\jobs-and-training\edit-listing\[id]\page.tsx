'use client';

import { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import type { Job } from '@/type/jobType';
import { Formik, Form, useField } from 'formik';
import * as Yup from 'yup';
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { useJobById } from '@/queries/useJobQueries';
import { useUpdateJob } from '@/mutations/useJobMutations';
import { useQueryClient } from '@tanstack/react-query';
import { RichTextEditor } from '@/components/ui/text-editor';
import FormikSelect from '@/components/lib/formikSelect';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { JobType } from '@/types';
import { validationMessages } from '@/constants/validationMessages';
import InputField from '@/components/common/InputField';

const {
  jobTitleRequired,
  companyNameRequired,
  locationRequired,
  minmumSalaryRequired,
  positiveSalary,
  validNumber,
  jobTypeRequired,
  jobModeRequired,
  jobPositionRequired,
  jobDescriptionRequired,
} = validationMessages;
// Define options for dropdowns
// const JOB_TYPES = [
//   'Full-time',
//   'Part-time',
//   'Contract',
//   'Temporary',
//   'Internship',
//   'Volunteer',
// ]

const JOB_MODES = ['On-site', 'Remote', 'Hybrid'];

const validationSchema = Yup.object({
  title: Yup.string().required(jobTitleRequired).trim(),
  companyName: Yup.string().required(companyNameRequired).trim(),
  location: Yup.string().required(locationRequired).trim(),
  monthlyFrom: Yup.number()
    .required(minmumSalaryRequired)
    .min(0, positiveSalary)
    .typeError(validNumber),
  jobType: Yup.string().required(jobTypeRequired).trim(),
  jobMode: Yup.string().required(jobModeRequired).trim(),
  jobPosition: Yup.string().required(jobPositionRequired).trim(),
  description: Yup.string()
    .required(jobDescriptionRequired)
    .test(
      'is-not-empty',
      jobDescriptionRequired,
      (value) => !!value && value.replace(/<[^>]*>/g, '').trim().length > 0
    ),
});

const DescriptionField = ({ ...props }) => {
  const [field, meta, helpers] = useField(props);

  const handleEditorChange = (value: string) => {
    helpers.setValue(value);
    helpers.setTouched(true, false);
  };

  return (
    <div>
      <RichTextEditor
        value={field.value}
        onChange={handleEditorChange}
        placeholder="Enter job description, responsibilities, and qualifications..."
        maxWords={1200}
      />
      {meta.touched && meta.error ? (
        <div className="text-destructive-500 text-[16px] mt-1">
          {meta.error}
        </div>
      ) : null}
    </div>
  );
};

export default function EditJobPage() {
  const { id } = useParams<{ id: string }>();
  const { data: currentJob, isLoading } = useJobById(id as string);
  const updateJobMutation = useUpdateJob();
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const queryClient = useQueryClient();

  const getJobType = useAdminValues({
    category: AdminValuesCategories?.jobType?.category,
  });
  const JobType = getJobType.data?.data?.data?.customValues || [];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!currentJob) {
    return <div>Job not found</div>;
  }

  const handleCancel = () => {
    setIsModalOpen(true);
  };

  const confirmCancel = () => {
    setIsModalOpen(false);
    router.replace(`/dashboard/jobs-and-training/my-listings/${currentJob.id}`);
  };

  const expiryDate = new Date();
  expiryDate.setMonth(expiryDate.getMonth() + 1);
  const formattedDate = expiryDate.toISOString();

  const initialValues: Job = {
    createdById: currentJob.createdById || '',
    title: currentJob.title || '',
    description: currentJob.description || '',
    location: currentJob.location || '',
    monthlyFrom: currentJob.monthlyFrom || 0,
    expiryDate: formattedDate || '',
    companyName: currentJob.companyName || '',
    jobType: currentJob.jobType || '',
    jobMode: currentJob.jobMode || '',
    industry: currentJob.industry || '',
    experienceLevel: currentJob.experienceLevel || '',
    jobPosition: currentJob.jobPosition || '',
    lastModifiedById: currentJob.lastModifiedById || '',
  };

  const handleSubmit = async (values: Job) => {
    const updatedJob: Job = { ...currentJob, ...values };
    await updateJobMutation.mutateAsync(
      { id: id as string, job: updatedJob },
      {
        onSuccess: () => {
          queryClient.setQueryData(['job', id], updatedJob);
          router.replace(
            `/dashboard/jobs-and-training/my-listings/${currentJob.id}`
          );
        },
        onError: (error) => {
          console.error('Error updating job:', error);
        },
      }
    );
  };

  return (
    <div className="mt-10">
      <h1 className="text-[24px] font-bold mb-4">Edit Job Listing</h1>
      <Card className="mx-auto p-6 rounded-md">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnChange={true}
          validateOnBlur={true}
        >
          {({
            isSubmitting,
            errors,
            isValid,
            dirty,
            handleBlur,
            handleChange,
            values,
          }) => (
            <Form className="space-y-4">
              <div>
                <div className="">
                  <InputField
                    label="Job Title"
                    name="title"
                    error={errors.title}
                    type={'text'}
                    value={values.title}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                    steric={true}
                  />
                </div>

                <div className="mt-4">
                  <InputField
                    label="Company Name"
                    name="companyName"
                    error={errors.companyName}
                    type={'text'}
                    value={values.companyName}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                    steric={true}
                  />
                </div>
              </div>

              <div>
                <InputField
                  label="Location"
                  name="location"
                  error={errors.location}
                  type={'text'}
                  value={values.location}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  steric={true}
                />
              </div>

              <div>
                <InputField
                  label="Minimum Salary"
                  name="monthlyFrom"
                  error={errors.monthlyFrom}
                  type={'text'}
                  value={values.monthlyFrom}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  steric={true}
                />
              </div>

              <div>
                <label htmlFor="jobType" className="block font-medium">
                  Job Type <span className="text-destructive-500">*</span>
                </label>
                {getJobType.isLoading ? (
                  <div>Loading job types...</div>
                ) : (
                  <FormikSelect
                    name="jobType"
                    options={JobType.map((type: JobType) => type.value) || []}
                    placeholder="Select job type"
                  />
                )}
              </div>

              <div>
                <label htmlFor="jobMode" className="block font-medium">
                  Job Mode <span className="text-destructive-500">*</span>
                </label>
                <FormikSelect
                  name="jobMode"
                  options={JOB_MODES}
                  placeholder="Select job mode"
                />
              </div>

              <div>
                <InputField
                  label="Job Position"
                  name="jobPosition"
                  error={errors.jobPosition}
                  type={'text'}
                  value={values.jobPosition}
                  handleChange={handleChange}
                  handleBlur={handleBlur}
                  steric={true}
                />
              </div>

              <div>
                <label htmlFor="description" className="block font-medium">
                  Details <span className="text-destructive-500">*</span>
                </label>
                <div className="rounded-md mt-4">
                  <DescriptionField
                    id="description"
                    name="description"
                    placeholder="Enter job description, responsibilities, and qualifications..."
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  type="submit"
                  variant="default"
                  disabled={
                    isSubmitting ||
                    updateJobMutation.isPending ||
                    !isValid ||
                    !dirty ||
                    Object.keys(errors).length > 0
                  }
                >
                  {isSubmitting || updateJobMutation.isPending ? (
                    <div className="flex text-center items-center gap-3">
                      <Loader2 className="animate-spin" />
                      <p>Submitting</p>
                    </div>
                  ) : (
                    'Update Job'
                  )}
                </Button>

                <Button type="button" onClick={handleCancel} variant="outline">
                  Cancel
                </Button>
              </div>
            </Form>
          )}
        </Formik>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Are you sure?</DialogTitle>
            </DialogHeader>
            <p>All unsaved changes will be lost.</p>
            <DialogFooter>
              <Button onClick={confirmCancel}>Yes</Button>
              <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                No
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Card>
    </div>
  );
}
