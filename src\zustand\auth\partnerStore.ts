// stores/usePartnerStore.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { partnerReducer } from '../reducers/partnerReducer';
import type { IPartner, PartnerState } from '@/type';
import partnerService from '../services/partnerServices';

const initialState: Omit<
  PartnerState,
  'registerPartner' | 'setPartner' | 'updateOnboardingData'
> = {
  partner: null,
  isLoading: false,
  showLoader: false,
};

export const usePartnerStore = create<
  PartnerState & {
    registerPartner: (
      _partnerData: Omit<IPartner, 'partnerId'>
    ) => Promise<void>;
    setPartner: (_partner: IPartner) => void;
    updateOnboardingData: (_data: Partial<IPartner>) => void;
  }
>()(
  persist(
    (set) => ({
      ...initialState,

      registerPartner: async (partnerData) => {
        const { showNotification } = useNotificationStore.getState();
        try {
          set((state) => partnerReducer(state, { type: 'START_LOADING' }));
          set((state) => partnerReducer(state, { type: 'SHOW_LOADER' }));

          const registeredPartner =
            await partnerService.registerPartner(partnerData);
          set((state) =>
            partnerReducer(state, {
              type: 'SET_PARTNER',
              payload: registeredPartner,
            })
          );

          showNotification('Partner registered successfully!', 'success');
        } catch (error) {
          console.error('Partner registration failed', error);
          set((state) => partnerReducer(state, { type: 'STOP_LOADING' }));
          set((state) => partnerReducer(state, { type: 'HIDE_LOADER' }));

          showNotification(
            'Partner registration failed. Please try again.',
            'error'
          );
          throw error;
        }
      },

      setPartner: (partner) => {
        set((state) =>
          partnerReducer(state, { type: 'SET_PARTNER', payload: partner })
        );
      },

      updateOnboardingData: (data) => {
        set((state) => ({
          ...state,
          partner: { ...state.partner, ...data } as IPartner,
        }));
      },
    }),
    {
      name: 'partner-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
