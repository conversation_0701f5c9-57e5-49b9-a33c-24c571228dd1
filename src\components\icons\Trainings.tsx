import React from 'react';

interface TrainingIconProps {
  width?: number;
  height?: number;
  stroke?: string;
}

const TrainingIcon: React.FC<TrainingIconProps> = ({
  width = 32,
  height = 32,
  stroke = '#043C5B',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 4V19C5 20.6569 6.34315 22 8 22H11M5 4H3M5 4H27M27 4H29M27 4V19C27 20.6569 25.6569 22 24 22H21M11 22H21M11 22L9.66667 26M21 22L22.3333 26M22.3333 26L23 28M22.3333 26H9.66667M9.66667 26L9 28M12 15V17M16 12V17M20 9V17"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default TrainingIcon;
