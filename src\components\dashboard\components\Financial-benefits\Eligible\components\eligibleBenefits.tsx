import React from 'react';

import { BenefitCard } from '@/components/dashboard/components/benefits/benefitCard';
import { DashboardImage1, DashboardImage2, DashboardImage3 } from '@/assets';

function EligibleBenefits() {
  return (
    <div className="flex-grow">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <BenefitCard
          title="Family Support Allowance Scheme"
          description="Get financial support to help you manage your family's essential living expenses."
          amount="$300 per month"
          banner={DashboardImage1}
        />
        <BenefitCard
          title="National Savings Program"
          description="Start saving securely and grow your funds with government-backed benefits."
          amount="$100 per month"
          banner={DashboardImage3}
        />
        <BenefitCard
          title="Housing Assistance"
          description="Get financial support to help you manage your family's essential living expenses."
          amount="$350 per month"
          banner={DashboardImage2}
        />
      </div>
    </div>
  );
}

export default EligibleBenefits;
