// components/ImageUpload.tsx
'use client';

import type { ChangeEvent } from 'react';
import { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { UploadIcon } from 'lucide-react';
import Image from 'next/image';
import FallbackImage from '@/assets/fallback_image.svg';

interface ImageUploadProps {
  value: string;
  onChange: (_value: string) => void;
  alt: string;
}

export const ImageUpload = ({ value, onChange, alt }: ImageUploadProps) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      // 2MB limit
      alert('Image size should be less than 2MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        onChange(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="space-y-4">
      <div className="h-[80px] w-[250px] relative rounded overflow-hidden mt-1">
        <Image
          src={value || FallbackImage}
          alt={alt}
          fill
          className="object-cover"
        />
      </div>

      <input
        type="file"
        ref={inputRef}
        onChange={handleImageChange}
        accept="image/png, image/jpeg, image/webp"
        className="hidden"
      />

      <Button
        type="button"
        variant="outline"
        onClick={() => inputRef.current?.click()}
        className="gap-2"
      >
        <UploadIcon className="h-4 w-4" />
        {value ? 'Choose Another Image' : 'Upload Image'}
      </Button>

      {!value && (
        <p className="text-sm text-muted-foreground">PNG, JPG up to 2MB</p>
      )}
    </div>
  );
};
