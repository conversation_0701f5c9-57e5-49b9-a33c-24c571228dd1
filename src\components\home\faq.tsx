'use client';
import React, { useState } from 'react';
import CaretDown from '../icons/caret-down';
import CaretUp from '../icons/caret-up';

const faqsData = [
  {
    id: 1,
    question: 'What is Career Navigator?',
    answer:
      'Career Navigator helps you find jobs, plan your career, and access training and financial support.',
  },
  {
    id: 2,
    question: 'How do I apply for jobs?',
    answer:
      'You can search for job listings and apply directly through the platform.',
  },
  {
    id: 3,
    question: 'Is Career Navigator free to use?',
    answer: 'Yes! Our platform is completely free for job seekers.',
  },
  {
    id: 4,
    question: 'Is Career Navigator the best?',
    answer: 'Yes! Our platform is completely free for job seekers.',
  },
];

const FAQs = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <div className="flex justify-center items-center h-[65vh]  bg-neutral-100">
      <div className="bg-white shadow-lg rounded-lg w-[45%] p-6">
        <h2 className="text-2xl text-neutral-900 font-extrabold text-[30px] leading-[44px] text-left mb-4">
          Frequently Asked Questions
        </h2>
        <div className="space-y-4">
          {faqsData.map((faq) => (
            <div key={faq.id} className="border-b pb-2">
              <button
                className="w-full text-left flex justify-between items-center font-semibold text-base py-2 !text-[--headingTextColor]"
                onClick={() => toggleFAQ(faq.id)}
              >
                {faq.question}
                <span className="!text-[--headingTextColor] text-[16px] font-normal">
                  {openFAQ === faq.id ? (
                    <CaretUp stroke="var(--headingTextColor)" />
                  ) : (
                    <CaretDown stroke="var(--headingTextColor)" />
                  )}
                </span>
              </button>
              {openFAQ === faq.id && (
                <p className="text-neutral-500 text-[16px] font-normal mt-2">
                  {faq.answer}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQs;
