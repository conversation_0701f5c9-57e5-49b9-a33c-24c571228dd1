/* eslint-disable @typescript-eslint/no-empty-object-type */
import React from 'react';

interface SendIconProps extends React.SVGProps<SVGSVGElement> {}

export const SendIcon: React.FC<SendIconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={32}
      height={32}
      viewBox="0 0 32 32"
      fill="none"
      {...props}
    >
      <path
        d="M3.20574 27.3626C3.1032 27.7156 3.20258 28.0964 3.46454 28.3542C3.7265 28.6121 4.1088 28.7055 4.46013 28.5974L15 25.3543L15 14C15 13.4477 15.4477 13 16 13C16.5523 13 17 13.4477 17 14L17 25.3543L27.5395 28.5972C27.8908 28.7053 28.2732 28.612 28.5351 28.3541C28.7971 28.0963 28.8965 27.7155 28.7939 27.3625C26.1993 18.431 22.1086 10.1368 16.8116 2.76897C16.6237 2.50763 16.3215 2.35271 15.9997 2.35271C15.6778 2.35271 15.3756 2.50764 15.1877 2.76899C9.89089 10.1368 5.80028 18.4311 3.20574 27.3626Z"
        fill="var(--buttonColor)"
      />
    </svg>
  );
};
