import React from 'react';

const CheckCircleIcon = ({
  width = 48,
  height = 48,
  stopColor = '#4568DC',
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 24L22 28L30 20M42 24C42 33.9411 33.9411 42 24 42C14.0589 42 6 33.9411 6 24C6 14.0589 14.0589 6 24 6C33.9411 6 42 14.0589 42 24Z"
      stroke={`url(#paint0_linear_${width}_${height})`}
      strokeWidth="3.85714"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <defs>
      <linearGradient
        id={`paint0_linear_${width}_${height}`}
        x1="6"
        y1="6"
        x2="42"
        y2="42"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={stopColor} />
        <stop offset="1" stopColor={stopColor} />
      </linearGradient>
    </defs>
  </svg>
);

export default CheckCircleIcon;
