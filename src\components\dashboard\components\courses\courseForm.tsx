'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { createCourse } from '@/zustand/services/courseServices';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ChevronLeft, ChevronRight, Loader2, AlertCircle } from 'lucide-react';
import { CourseSummaryStep } from './courseSummaryStep1';
import { CourseDetailsStep } from './courseSummaryStep2';
import { CourseSkillsStep } from './courseSummaryStep3';
import { CourseFormStepper } from './coursesStepper';
import { UnsavedChangesDialog } from './unsavedChanges';
import { CourseSummary } from './courseSummary';
import { SuccessDialog } from './sucessModal';
import toast from 'react-hot-toast';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import type { Course } from '@/types/courseType';
import { Card } from '@/components/ui/card';

export const CourseForm: React.FC = () => {
  const userId = useAuthStore((state) => state.user?.id);
  const [isLoading, setIsLoading] = useState(false);

  const {
    currentStep,
    setCurrentStep,
    course,
    setIsDirty,
    resetForm,
    validateStep,
    skillsCovered,
  } = useCourseFormStore();

  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [showSummaryDialog, setShowSummaryDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    resetForm();
  }, [resetForm]);

  useEffect(() => {
    return () => {
      resetForm();
    };
  }, [resetForm]);

  const createCourseMutation = useMutation({
    mutationFn: createCourse,
    onSuccess: () => {
      setShowSuccessDialog(true);
    },
    onError: () => {
      toast.error('Error submitting course. Please try again later');
      setShowSummaryDialog(false);
    },
  });

  const handleNext = () => {
    const validation = validateStep(currentStep);
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }

    setValidationErrors([]);

    if (currentStep === 'summary') {
      setCurrentStep('details');
    } else if (currentStep === 'details') {
      setCurrentStep('skills');
    } else if (currentStep === 'skills') {
      setShowSummaryDialog(true);
    }
  };

  const handleBack = () => {
    navigateBack();
  };

  const navigateBack = () => {
    if (currentStep === 'details') {
      setCurrentStep('summary');
    } else if (currentStep === 'skills') {
      setCurrentStep('details');
    }
  };

  const saveDraftMutation = useMutation({
    mutationFn: (draftData: Partial<Course>) =>
      createCourse({ ...draftData, status: 'Draft' } as Course),
    onSuccess: () => {
      resetForm();
    },
    onError: () => {
      toast.error('Error saving draft. Please try again later');
    },
  });

  const handleSaveDraft = async () => {
    if (!course.name || !course.description) {
      toast.error('Name and description are required to save draft');
      return;
    }

    setIsLoading(true);

    try {
      const draftData: Course = {
        id: course.id || '',
        name: course.name,
        description: course.description,
        coverImage: course.coverImage,
        mainTopic: course.mainTopic || '',
        subTopic: course.subTopic || '',
        learningObjective: course.learningObjective || '',
        courseLink: course.courseLink || '',
        deliveryMode: course.deliveryMode || 'In Person',
        address: course.address || '',
        city: course.city || '',
        state: course.state || '',
        country: course.country || '',
        duration: course.duration || '',
        durationUnit: course.durationUnit || 'Weeks',
        isSelfPaced: course.isSelfPaced || false,
        isFlexible: course.isFlexible || false,
        experienceLevel: course.experienceLevel || '',
        certificationType: course.certificationType || '',
        startDate: course.startDate || new Date().toISOString(),
        endDate: course.endDate || new Date().toISOString(),
        courseFee: course.courseFee || 0,
        createdById: userId || '',
        lastModifiedById: userId || '',
        // sessions: course.sessions || [],
        programOutlines: course.programOutlines || [
          { key: '', value: '' },
          // { key: '', value: '' },
        ],
        skillsCovered: skillsCovered || [],
        status: 'Draft',
      };

      await saveDraftMutation.mutateAsync(draftData);
      toast.success('Draft saved successfully');
      window.location.href = '/dashboard/courses';
    } catch (error) {
      toast.error('Failed to save draft');
      console.error('Error saving draft:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitCourse = async () => {
    const courseData = {
      name: course.name,
      description: course.description,
      coverImage: course.coverImage,
      mainTopic: course.mainTopic,
      subTopic: course.subTopic || '',
      learningObjective: course.learningObjective,
      courseLink: course.courseLink,
      deliveryMode: course.deliveryMode,
      address: course.address || '',
      city: course.city || '',
      state: course.state || '',
      country: course.country || '',
      duration: course.duration || '',
      durationUnit: course.durationUnit,
      isSelfPaced: course.isSelfPaced,
      isFlexible: course.isFlexible,
      experienceLevel: course.experienceLevel || '',
      certificationType: course.certificationType || '',
      startDate: course.startDate || new Date().toISOString(),
      endDate: course.endDate || new Date().toISOString(),
      courseFee: course.courseFee || 0,
      createdById: userId,
      lastModifiedById: userId,
      // sessions: course.sessions || [],
      programOutlines: course.programOutlines || [],
      skillsCovered: skillsCovered || [],
    };

    try {
      setIsLoading(true);
      await createCourseMutation.mutateAsync(courseData);
      setShowSummaryDialog(false);
      setShowSuccessDialog(true);
    } catch (error) {
      console.error('Error submitting course:', error);
      toast.error('Error submitting course. Please try again later');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDiscardChanges = () => {
    setIsDirty(false);
    setShowUnsavedDialog(false);
  };

  const handleKeepEditing = () => {
    setShowUnsavedDialog(false);
  };

  const handleAddAnotherCourse = () => {
    resetForm();
    setShowSuccessDialog(false);
  };

  const handleReturnHome = () => {
    resetForm();
    setShowSuccessDialog(false);
    window.location.href = '/dashboard/courses';
  };

  return (
    <div className="mx-auto">
      <Card className="mb-8 px-4 py-10 rounded-lg">
        {currentStep === 'summary' && <CourseSummaryStep />}
        {currentStep === 'details' && <CourseDetailsStep />}
        {currentStep === 'skills' && <CourseSkillsStep />}

        {validationErrors.length > 0 && (
          <Alert variant="destructive" className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Errors</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                {validationErrors.map((error) => (
                  <li key={error}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </Card>

      <CourseFormStepper />

      <div className="flex justify-between mt-8">
        {currentStep !== 'summary' ? (
          <Button
            className="cursor-pointer py-[14px] px-[28px] h-[48px]"
            variant="outline"
            onClick={handleBack}
          >
            <ChevronLeft />
            Back
          </Button>
        ) : (
          <div></div>
        )}

        <div className="flex gap-3">
          <Button
            className="cursor-pointer py-[14px] px-[28px] h-[48px]"
            variant="outline"
            onClick={handleSaveDraft}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
              </div>
            ) : (
              'Save Draft'
            )}
          </Button>

          {currentStep === 'skills' ? (
            <Button
              className="cursor-pointer py-[14px] px-[28px] h-[48px]"
              onClick={handleNext}
            >
              Review & Submit
            </Button>
          ) : (
            <Button
              className="cursor-pointer py-[14px] px-[28px] h-[48px]"
              onClick={handleNext}
            >
              Next <ChevronRight />
            </Button>
          )}
        </div>
      </div>

      <UnsavedChangesDialog
        isOpen={showUnsavedDialog}
        onClose={() => setShowUnsavedDialog(false)}
        onDiscard={() => {
          handleDiscardChanges();
          window.location.href = '/dashboard/courses';
        }}
        onKeepEditing={handleKeepEditing}
      />

      <CourseSummary
        isOpen={showSummaryDialog}
        onClose={() => setShowSummaryDialog(false)}
        onSubmit={handleSubmitCourse}
        onContinueEditing={() => setShowSummaryDialog(false)}
        onSaveDraft={handleSaveDraft}
      />

      <SuccessDialog
        isOpen={showSuccessDialog}
        onAddAnother={handleAddAnotherCourse}
        onReturnHome={handleReturnHome}
      />
    </div>
  );
};
