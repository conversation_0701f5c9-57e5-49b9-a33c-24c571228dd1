'use client';

import React, { useEffect, useState } from 'react';
import EligibleBenefits from './eligibleBenefits';
import TopNewApplicant from '../components/top-new-applicant';
import OverviewSection from './overview-section';
import DashboardSuccessModal from '@/components/modals/login-success-modal';
import { useSearchParams } from 'next/navigation';
import { useOverviewStats } from '@/queries';
import { TrainingSection } from './trainingCard';
import Nolisting from './Nolisting';
import { getStats } from '@/zustand/services/courseServices';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
// import { Skeleton } from '@/components/ui/skeleton'
import { useTrainingProviderStore } from '@/zustand/store/trainerProvider';

const DashboardTemplate = () => {
  const searchParams = useSearchParams();
  const showModal = searchParams.get('showModal') === 'true';
  const { user } = useAuthStore();

  // Jobs data
  const { data: jobsData, isLoading: isJobsLoading } = useOverviewStats();
  const [isCoursesLoading, setIsCoursesLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCourses: 0,
    activeCourses: 0,
    completedEnrollments: 0,
  });
  const [open, setOpen] = useState(false);

  // Calculate states
  const noListings = !isJobsLoading && jobsData?.data?.totalActiveJobs === 0;
  const noCourses = !isCoursesLoading && stats.totalCourses === 0;

  const { isTrainer } = useTrainingProviderStore();

  const fetchStats = async () => {
    try {
      setIsCoursesLoading(true);
      const statsResponse = await getStats({ createdById: user?.id });
      setStats({
        totalCourses: statsResponse.data.totalCourses,
        activeCourses: statsResponse.data.activeCourses,
        completedEnrollments: statsResponse.data.completedEnrollments,
      });
    } catch (error) {
      console.error('Error fetching statistics:', error);
    } finally {
      setIsCoursesLoading(false);
    }
  };
  // Fetch course stats
  useEffect(() => {
    if (user?.id) {
      fetchStats();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id]);

  useEffect(() => {
    if (showModal) {
      setOpen(true);
    }
  }, [showModal]);

  return (
    <>
      <div className="space-y-8">
        <div className="space-y-5 mb-8">
          <h4 className="text-neutral-900 font-semibold text-[40px] leading-[48px]">
            Welcome to Your Dashboard!
          </h4>
          <p className="text-neutral-700 max-w-[870px] text-[20px] font-normal">
            Easily manage job postings, training programs, and financial
            benefits—all in one place.
          </p>
        </div>

        {/* Conditional rendering */}
        <div className="space-y-8">
          {(noListings || noCourses) && <Nolisting />}

          {isTrainer && stats.totalCourses > 0 && <TrainingSection />}
          {!noListings && <OverviewSection />}
        </div>

        {!noListings && <TopNewApplicant />}

        <EligibleBenefits />
      </div>

      <p className="pt-8 font-normal text-[20px] leading-[16px] text-neutral-900">
        Need assistance?{' '}
        <span className="font-semibold text-[20px] leading-[16px] text-primary-500 ml-1">
          Contact Support
        </span>
      </p>

      <DashboardSuccessModal open={open} setOpen={setOpen} />
    </>
  );
};

export default DashboardTemplate;
