import React from 'react';
import type { Applicant } from '@/type';
import { StatusCountBadge } from '@/utils';

interface TabsProps {
  activeTab: Applicant['status'];
  onTabChange: (_status: Applicant['status']) => void;
  counts: {
    Pending: number;
    Shortlisted: number;
    Contacted: number;
    Rejected: number;
    Hired: number;
  };
}

const Tabs: React.FC<TabsProps> = ({ activeTab, onTabChange, counts }) => {
  const statusDisplayNames: Record<Applicant['status'], string> = {
    Pending: 'New',
    Shortlisted: 'Shortlisted',
    Contacted: 'Contacted',
    Rejected: 'Rejected',
    Hired: 'Hired',
  };

  return (
    <div className="flex space-x-4 mb-4 overflow-x-auto mt-6">
      {Object.entries(statusDisplayNames).map(([status, displayName]) => {
        const typedStatus = status as Applicant['status'];
        return (
          <button
            key={status}
            onClick={() => onTabChange(typedStatus)}
            className={`px-4 py-2 rounded-lg transition flex items-center ${
              activeTab === typedStatus
                ? 'border-2 border-primary-500 font-medium text-primary-500 text-[20px] bg-neutral-50'
                : 'bg-white text-neutral-500 font-medium border-neutral-300 text-[20px]'
            }`}
          >
            {displayName}
            <StatusCountBadge
              count={counts[typedStatus]}
              isActive={activeTab === typedStatus}
            />
          </button>
        );
      })}
    </div>
  );
};

export default Tabs;
