import React from 'react';

interface StudyIconProps {
  stroke?: string;
  width?: number;
  height?: number;
}

function StudyIcon({
  stroke = 'var(--primary-500)',
  width = 49,
  height = 48,
}: StudyIconProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 49 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.8531 12.8001H8.6531C6.17886 12.8001 4.1731 14.8058 4.1731 17.2801V37.4401C4.1731 39.9143 6.17886 41.9201 8.6531 41.9201H40.0131C42.4873 41.9201 44.4931 39.9143 44.4931 37.4401V17.2801C44.4931 14.8058 42.4873 12.8001 40.0131 12.8001H28.8131M19.8531 12.8001V10.5601C19.8531 8.08584 21.8589 6.08008 24.3331 6.08008C26.8073 6.08008 28.8131 8.08584 28.8131 10.5601V12.8001M19.8531 12.8001C19.8531 15.2743 21.8589 17.2801 24.3331 17.2801C26.8073 17.2801 28.8131 15.2743 28.8131 12.8001M17.6131 30.7201C20.0873 30.7201 22.0931 28.7143 22.0931 26.2401C22.0931 23.7658 20.0873 21.7601 17.6131 21.7601C15.1389 21.7601 13.1331 23.7658 13.1331 26.2401C13.1331 28.7143 15.1389 30.7201 17.6131 30.7201ZM17.6131 30.7201C20.539 30.7201 23.0281 32.59 23.9506 35.2001M17.6131 30.7201C14.6872 30.7201 12.1979 32.59 11.2754 35.2001M31.0531 24.0001H37.7731M31.0531 32.9601H35.5331"
        stroke={stroke}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default StudyIcon;
