'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { MapPin, Edit } from 'lucide-react';
import { Card } from '@/components/ui/card';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import FallbackImage from '@/assets/fallback_image.svg';
import { CompanyDetails } from './companyDetails';
import { CompanyRepresentatives } from './companyRepresentative';
import { Loader } from '../../common/Loader';
import { Button } from '@/components/ui/button';
import { normalizeText } from '@/utils';

interface CompanyProfileProps {
  companyDetails: {
    name: string;
    id: string;
    partnerId: string;
    logo: string;
    registrationDate: string;
    companySize: string;
    industry: string;
    companyAddress: string;
    postCode: string;
    email: string;
    location: string;
  };
  representatives: Array<{
    id: number;
    role: string;
    name: string;
    email: string;
  }>;
  hiringInformation: {
    hiringStatus: string;
    activeJobPosts: number | undefined;
    hiredOnPlatfom: string;
  };
  benefits: {
    monthlyBenefits: string;
    lastPaymentDate: string;
    TotalBenefitsReceived: string;
    CompanyEligibility: string;
  };
  isLoading: boolean;
}

export function CompanyProfile({
  companyDetails,
  hiringInformation,
  benefits,
  isLoading,
}: CompanyProfileProps) {
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
        <Loader />
      </div>
    );
  }

  const handleEditCompany = () => {
    router.push(`/dashboard/company/edit/${companyDetails.id}`);
  };

  return (
    <Card className="mx-auto bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="h-[80px] w-[250px] relative rounded overflow-hidden mt-1">
            {companyDetails.logo ? (
              <Image
                src={companyDetails.logo || '/placeholder.svg'}
                alt={`${companyDetails.name || 'Company'} logo`}
                fill
                className="object-cover"
              />
            ) : (
              <Image
                src={FallbackImage || '/placeholder.svg'}
                alt="Company logo"
                width={84}
                height={48}
              />
            )}
          </div>
          <Button
            variant="default"
            className="flex items-center gap-2"
            onClick={handleEditCompany}
          >
            <Edit className="h-4 w-4" />
            Edit Company
          </Button>
        </div>

        {/* Company Name and Location */}
        <div className="mb-6">
          <h1 className="text-[28px] font-semibold text-neutral-700 leading-[36px]">
            {companyDetails?.name || 'N/A'}
          </h1>
          <div className="flex items-center mt-1 text-neutral-400">
            <MapPin className="h-4 w-4 mr-1" />
            <span className="text-[16px] text-neutral-500 font-normal leading-[24px] capitalize">
              {normalizeText(companyDetails?.location || 'N/A')}
            </span>
          </div>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid grid-cols-2 max-w-[400px] gap-4 mb-6 bg-transparent">
            <TabsTrigger value="details" className="px-6">
              Company Details
            </TabsTrigger>
            <TabsTrigger value="representatives" className="px-6">
              Representatives
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details">
            <CompanyDetails
              companyDetails={companyDetails}
              hiringInformation={hiringInformation}
              benefits={benefits}
            />
          </TabsContent>

          <TabsContent value="representatives">
            <CompanyRepresentatives />
          </TabsContent>
        </Tabs>
      </div>
    </Card>
  );
}
