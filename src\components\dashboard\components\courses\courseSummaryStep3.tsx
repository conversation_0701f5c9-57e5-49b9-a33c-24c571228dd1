'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { XIcon, ChevronDown, ChevronUp, Search } from 'lucide-react';
import type { SkillsCovered } from '@/types/courseType';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

const EXTRACTED_SKILLS: SkillsCovered[] = [
  { key: 'Angular (Web Framework)', value: '' },
  { key: 'Blockchain', value: '' },
  { key: 'Bootstrap (Front-End Framework)', value: '' },
  { key: 'Finance', value: '' },
  { key: 'Market Research', value: '' },
  { key: 'Ideation', value: '' },
  { key: 'Market Research', value: '' },
];

const AVAILABLE_SKILLS: SkillsCovered[] = [
  ...EXTRACTED_SKILLS,
  { key: 'JavaScript', value: 'Technology' },
  { key: 'Python', value: 'Technology' },
  { key: 'React', value: 'Technology' },
  { key: 'Data Analysis', value: 'Business' },
  { key: 'Project Management', value: 'Business' },
  { key: 'UI/UX Design', value: 'Design' },
  { key: 'Content Marketing', value: 'Marketing' },
  { key: 'SEO', value: 'Marketing' },
];

export const CourseSkillsStep: React.FC = () => {
  const { skillsCovered, addSkillCovered, removeSkillCovered } =
    useCourseFormStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showLastSkillWarning, setShowLastSkillWarning] = useState(false);
  const [skillToRemove, setSkillToRemove] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (skillsCovered.length === 0) {
      EXTRACTED_SKILLS.forEach((skill) => addSkillCovered(skill));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node) &&
      inputRef.current &&
      !inputRef.current.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredSkills = AVAILABLE_SKILLS.filter(
    (skill) =>
      skill.key.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !skillsCovered.some((s) => s.key === skill.key)
  );

  const handleAddSkill = (skill: SkillsCovered) => {
    addSkillCovered(skill);
    setSearchTerm('');
    setIsDropdownOpen(false);
    inputRef.current?.focus();
  };

  const handleRemoveSkill = (skillKey: string) => {
    if (skillsCovered.length === 1) {
      setSkillToRemove(skillKey);
      setShowLastSkillWarning(true);
    } else {
      removeSkillCovered(skillKey);
    }
  };

  const handleConfirmRemoveLastSkill = () => {
    if (skillToRemove) {
      removeSkillCovered(skillToRemove);
    }
    setShowLastSkillWarning(false);
    setSkillToRemove(null);
  };

  const handleCancelRemoveLastSkill = () => {
    setShowLastSkillWarning(false);
    setSkillToRemove(null);
  };

  const handleInputFocus = () => {
    setIsDropdownOpen(true);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
    if (!isDropdownOpen) {
      inputRef.current?.focus();
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-[20px] font-semibold text-neutral-900 leading-[28px]">
        Step 3: Customize Course Skills
      </h2>

      <p className="text-neutral-500 text-[16px] font-normal leading-[24px]">
        The following skills have been extracted from the course details,
        learning objectives and program outline.
      </p>

      <div>
        <h3 className="font-medium mb-2 text-[20px] text-neutral-900 leading-[28px]">
          {skillsCovered.length} Total Skills
        </h3>

        <div className="relative mb-4">
          <div className="relative flex items-center w-[500px] border-2 border-neutral-200 px-4 rounded-lg">
            <Search className="text-neutral-200" />
            <Input
              ref={inputRef}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownOpen(true);
              }}
              onFocus={handleInputFocus}
              placeholder="Add another skill"
              className="pr-10 border-none border-neutral-100"
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 h-8 w-8"
              onClick={toggleDropdown}
            >
              {isDropdownOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>

          {isDropdownOpen && (
            <div
              ref={dropdownRef}
              className="absolute z-10 mt-1 bg-white border border-neutral-100 rounded shadow-lg max-h-60 w-[500px] overflow-auto"
            >
              {filteredSkills.length > 0 ? (
                filteredSkills.map((skill) => (
                  <div
                    key={skill.key}
                    role="option"
                    tabIndex={0}
                    aria-selected={skillsCovered.some(
                      (s) => s.key === skill.key
                    )}
                    className={cn(
                      'px-4 py-2 hover:bg-neutral-100 cursor-pointer flex justify-between items-center focus:bg-neutral-200 outline-none',
                      skillsCovered.some((s) => s.key === skill.key) &&
                        'bg-neutral-200'
                    )}
                    onClick={() => handleAddSkill(skill)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleAddSkill(skill);
                      }
                    }}
                    onTouchStart={() => handleAddSkill(skill)}
                  >
                    <span>
                      {skill.key}
                      {skill.value && (
                        <span className="text-[14px] text-neutral-500 ml-2">
                          ({skill.value})
                        </span>
                      )}
                    </span>
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-neutral-500">
                  No matching skills found
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex flex-col gap-2">
          {skillsCovered.map((skill) => (
            <Badge
              key={skill.key}
              variant="secondary"
              className="text-[16px] rounded-md bg-neutral-100 text-neutral-900 py-1.5 px-3 flex items-center w-fit"
            >
              <span className="truncate">
                {skill.key}
                {skill.value && (
                  <span className="text-[14px] text-neutral-900 ml-1">
                    ({skill.value})
                  </span>
                )}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveSkill(skill.key);
                }}
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent hover:text-destructive-600"
              >
                <XIcon className="h-3 w-3" />
                <span className="sr-only">Remove</span>
              </Button>
            </Badge>
          ))}
        </div>
      </div>

      <Dialog
        open={showLastSkillWarning}
        onOpenChange={setShowLastSkillWarning}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogDescription>
              Are you sure you want to remove the last skill?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              className=" rounded-full"
              onClick={handleCancelRemoveLastSkill}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              className="bg-destructive-500 rounded-full"
              onClick={handleConfirmRemoveLastSkill}
            >
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
