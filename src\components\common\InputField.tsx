import type { FC, ChangeEvent, FocusEvent } from 'react';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { ErrorMessage } from 'formik';

interface InputFieldProps {
  label?: string;
  error?: string;
  name: string;
  type?: string;
  value?: string | number;
  placeholder?: string;
  handleChange: (_e: ChangeEvent<HTMLInputElement>) => void;
  handleBlur?: (_e: FocusEvent<HTMLInputElement>) => void;
  labelClass?: string;
  disabled?: boolean;
  steric?: boolean;
  startTitle?: string;
}

const InputField: FC<InputFieldProps> = ({
  label,
  error,
  name,
  value,
  type,
  placeholder,
  handleBlur,
  handleChange,
  labelClass,
  disabled = false,
  steric = false,
  startTitle,
}) => {
  return (
    <>
      {label && (
        <Label
          htmlFor={name}
          className={
            labelClass ? labelClass : 'text-neutral-900 font-medium text-[16px]'
          }
        >
          {label} {steric && <span className="!text-destructive-500">*</span>}
        </Label>
      )}
      <div className="flex rounded-md">
        {startTitle && (
          <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-neutral-100 bg-neutral-50 text-neutral-500 text-[16px]">
            {startTitle}
          </span>
        )}
        <Input
          id={name}
          name={name}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`text-[16px] text-[--bodyTextColor] font-normal ${disabled && 'bg-neutral-100 disabled'} ${error && 'border border-destructive-500'} ${startTitle && 'rounded-none rounded-r-md'}`}
          disabled={disabled}
        />
      </div>
      {error && (
        <ErrorMessage
          name={name}
          component="div"
          className="text-[16px] !text-destructive-500"
        />
      )}
    </>
  );
};

export default InputField;
