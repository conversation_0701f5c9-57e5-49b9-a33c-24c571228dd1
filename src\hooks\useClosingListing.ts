// hooks/useCloseListing.ts
import { useState, useMemo, useEffect, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import applicantService from '@/zustand/services/applicantServices';
import jobService from '@/zustand/services/jobServices';
// import { useRouter } from 'next/navigation'

interface UseCloseListingProps {
  jobId: string;
  isOpen: boolean;
}

export const useCloseListing = ({ jobId, isOpen }: UseCloseListingProps) => {
  const queryClient = useQueryClient();
  // const router = useRouter()

  // State management
  const [hiringSource, setHiringSource] = useState<string>('');
  const [selectedApplicantIds, setSelectedApplicantIds] = useState<string[]>(
    []
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [prefilledApplicantIds, setPrefilledApplicantIds] = useState<
    Set<string>
  >(new Set());
  const initialLoadRef = useRef(true);

  // Fetch job-specific applicants
  const { data: jobApplicants, isLoading: isApplicantsLoading } = useQuery({
    queryKey: ['job-applicants', jobId],
    queryFn: async () => {
      const res = await applicantService.getApplicantsbyApplicantId(
        jobId,
        1,
        1000,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      );
      return res.data;
    },
    enabled: isOpen && !!jobId,
    staleTime: 60 * 1000, // 1 minute
  });

  // Set initial hired applicants
  useEffect(() => {
    if (jobApplicants && initialLoadRef.current) {
      const hiredIds = jobApplicants
        .filter((applicant) => applicant.status === 'Hired')
        .map((applicant) => applicant.id);
      if (hiredIds.length > 0) {
        setSelectedApplicantIds(hiredIds);
        setPrefilledApplicantIds(new Set(hiredIds));
        setHiringSource('On this platform');
        initialLoadRef.current = false;
      }
    }
  }, [jobApplicants]);

  // Memoized filtered applicants
  const hiredApplicants = useMemo(() => {
    if (!jobApplicants) return [];
    return jobApplicants.filter((applicant) => applicant.status === 'Hired');
  }, [jobApplicants]);

  const eligibleApplicants = useMemo(() => {
    if (!jobApplicants) return [];
    return jobApplicants.filter((applicant) => {
      const statusFilter = ['Pending', 'Contacted', 'Shortlisted'].includes(
        applicant.status
      );
      const searchFilter =
        searchTerm === '' ||
        applicant.user?.userName
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        applicant.user?.email?.toLowerCase().includes(searchTerm.toLowerCase());
      return statusFilter && searchFilter;
    });
  }, [jobApplicants, searchTerm]);

  const { mutateAsync: closeJob } = useMutation({
    mutationFn: async () => {
      setLoading(true);
      try {
        await jobService.updateJobsClose(jobId, selectedApplicantIds);
        queryClient.invalidateQueries({ queryKey: ['applicants', jobId] });
        queryClient.invalidateQueries({ queryKey: ['jobs'] });
        window.location.href = '/dashboard/jobs-and-training/my-listings';
      } finally {
        setLoading(false);
      }
    },
  });

  // Handlers
  const handleAddAnother = () => {
    setSelectedApplicantIds((prev) => [...prev, '']);
  };

  const handleSelectChange = (value: string, index: number) => {
    const newIds = [...selectedApplicantIds];
    newIds[index] = value;
    setSelectedApplicantIds(newIds);
  };

  const handleRemoveApplicant = (index: number) => {
    const newIds = [...selectedApplicantIds];
    if (!prefilledApplicantIds.has(newIds[index])) {
      newIds.splice(index, 1);
      setSelectedApplicantIds(newIds.length > 0 ? newIds : []);
    }
  };

  const handleHiringSourceChange = (value: string) => {
    setHiringSource(value);
    if (value === 'On this platform') {
      if (selectedApplicantIds.length === 0) {
        const hiredIds = hiredApplicants.map((applicant) => applicant.id);
        setSelectedApplicantIds(hiredIds.length > 0 ? hiredIds : []);
        if (hiredIds.length > 0) {
          setPrefilledApplicantIds(new Set(hiredIds));
        }
      }
    } else {
      setSelectedApplicantIds([]);
      setPrefilledApplicantIds(new Set());
    }
  };

  const handleConfirmClose = async () => {
    await closeJob();
    setConfirmModalOpen(false);
  };

  const resetModal = () => {
    setHiringSource('');
    setSelectedApplicantIds([]);
    setPrefilledApplicantIds(new Set());
    setSearchTerm('');
    initialLoadRef.current = true;
  };

  return {
    state: {
      hiringSource,
      selectedApplicantIds,
      loading,
      searchTerm,
      confirmModalOpen,
      isApplicantsLoading,
      jobApplicants,
      hiredApplicants,
      eligibleApplicants,
      prefilledApplicantIds,
    },
    actions: {
      setSearchTerm,
      setConfirmModalOpen,
      handleAddAnother,
      handleSelectChange,
      handleRemoveApplicant,
      handleHiringSourceChange,
      handleConfirmClose,
      resetModal,
    },
  };
};
