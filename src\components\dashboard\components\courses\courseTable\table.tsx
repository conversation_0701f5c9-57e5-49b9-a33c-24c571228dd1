'use client';

import type React from 'react';
import type { Course } from '@/types/courseType';
import { ChevronDown, ChevronUp, ChevronsUpDown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { normalizeText } from '@/utils';

interface CourseTableProps {
  courses: Course[];
  onSort: (_field: string, _ascending: boolean) => void;
  sortField?: string;
  ascending?: boolean;
  onEdit: (_course: Course) => void;
  onDelete: (_courseId: string) => void;
}

export const CourseTable: React.FC<CourseTableProps> = ({
  courses,
  onSort,
  sortField,
  ascending,
}) => {
  const router = useRouter();

  const handleSort = (field: string) => {
    if (sortField === field) {
      onSort(field, !ascending);
    } else {
      onSort(field, false);
    }
  };

  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ChevronsUpDown className="ml-2 h-4 w-4" />;
    }
    return ascending ? (
      <ChevronUp className="ml-2 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-2 h-4 w-4" />
    );
  };

  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';

      // Format day with leading zero
      const day = date.getDate().toString().padStart(2, '0');

      // Get abbreviated month name
      const month = date.toLocaleDateString('en-US', { month: 'short' });

      // Get full year
      const year = date.getFullYear();

      return `${day} ${month} ${year}`;
    } catch {
      return '';
    }
  };

  const viewCourseDetails = (courseId: string) => {
    router.push(`/dashboard/courses/${courseId}`);
  };

  return (
    <div className="w-full overflow-auto">
      <Table className="">
        <TableHeader className="bg-neutral-50 py-[20px] px-[16px] gap-[12px]">
          <TableRow>
            <TableHead className="text-[16px] leading-[24px] font-semibold text-neutral-700">
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent font-medium text-[16px]"
                onClick={() => handleSort('name')}
              >
                <span className="flex items-center text-[16px] leading-[24px] font-semibold text-neutral-700 ">
                  Course {renderSortIcon('name')}
                </span>
              </Button>
            </TableHead>
            <TableHead className="text-[16px] leading-[24px] font-semibold text-neutral-700">
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent font-medium text-[16px]"
                onClick={() => handleSort('deliveryMode')}
              >
                <span className="flex items-center text-[16px] leading-[24px] font-semibold text-neutral-700 ">
                  Delivery {renderSortIcon('deliveryMode')}
                </span>
              </Button>
            </TableHead>
            <TableHead className="text-[16px] leading-[24px] font-semibold text-neutral-700">
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent font-medium text-[16px]"
                onClick={() => handleSort('experienceLevel')}
              >
                <span className="flex items-center text-[16px] leading-[24px] font-semibold text-neutral-700 ">
                  Experience Level {renderSortIcon('experienceLevel')}
                </span>
              </Button>
            </TableHead>
            <TableHead className=" text-[16px]">
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent font-medium text-[16px]"
                onClick={() => handleSort('mainTopic')}
              >
                <span className="flex items-center text-[16px] leading-[24px] font-semibold text-neutral-700 ">
                  Main Topic {renderSortIcon('mainTopic')}
                </span>
              </Button>
            </TableHead>
            <TableHead className="text-[16px] leading-[24px] font-semibold text-neutral-700">
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent font-medium text-[16px]"
                onClick={() => handleSort('modifiedOn')}
              >
                <span className="flex items-center text-[16px] leading-[24px] font-semibold text-neutral-700 ">
                  Last Updated {renderSortIcon('modifiedOn')}
                </span>
              </Button>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {courses.map((course) => (
            <TableRow
              key={course.id}
              className="hover:bg-neutral-50 cursor-pointer"
              onClick={() => course.id && viewCourseDetails(course.id)}
            >
              <TableCell className="font-medium text-[16px] text-neutral-900 leading-6">
                {course.name}
              </TableCell>
              <TableCell className="text-[16px] text-neutral-600 font-normal leading-6">
                {course.deliveryMode}
              </TableCell>
              <TableCell className="text-[16px] text-neutral-600 font-normal leading-6">
                {course.experienceLevel}
              </TableCell>
              <TableCell className="text-[16px] text-neutral-600 font-normal leading-6 capitalize">
                {' '}
                {normalizeText(course.mainTopic || 'Not Specified')}{' '}
              </TableCell>
              <TableCell className="text-[16px] text-neutral-600 font-normal leading-6">
                {formatDate(course.modifiedOn)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {courses.length === 0 && (
        <div className="text-center py-8 text-neutral-500 text-[16px]">
          No courses found
        </div>
      )}
    </div>
  );
};
