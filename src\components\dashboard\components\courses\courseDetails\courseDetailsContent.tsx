'use client';

import { useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import type { Course } from '@/types/courseType';

interface CourseDetailsContentProps {
  course: Course;
}

export default function CourseDetailsContent({
  course,
}: CourseDetailsContentProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  const parseLearningObjectives = (objectives: string) => {
    if (!objectives) return [];
    // Split by new lines and filter out empty lines
    return objectives.split('\n').filter((line) => line.trim() !== '');
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  return (
    <div className="space-y-8 mt-5">
      {/* Description Section */}
      <div className="space-y-3">
        <h2 className="text-[24px] font-semibold text-neutral-900 leading-[32px]">
          Description
        </h2>
        <div className="text-neutral-500 whitespace-pre-line text-[16px] font-normal leading-[24px]">
          {course?.description}
        </div>
      </div>
      <div>
        <h3 className="text-[24px] font-semibold mb-4 text-neutral-900 leading-8 -tracking-[0.48px]">
          Learning Objectives
        </h3>
        <ul className="space-y-2">
          {parseLearningObjectives(course.learningObjective).map(
            (objective) => (
              <li
                key={objective}
                className="text-neutral-500 text-[16px] font-normal"
              >
                {objective}
              </li>
            )
          )}
        </ul>
      </div>

      {/* Program Outline Section */}
      <div className="space-y-3">
        <h2 className="text-[24px] font-semibold text-neutral-900 leading-[32px]">
          Program Outline
        </h2>
        <Accordion
          type="multiple"
          value={expandedSections}
          className="border border-neutral-50 text-[16px] max-w-[98%]"
        >
          {course.programOutlines?.map((outline, index) => {
            const sectionValue = `section-${index}`;
            const isOpen = expandedSections.includes(sectionValue);

            return (
              <AccordionItem
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                value={sectionValue}
                className={`
                  ${index !== course.programOutlines.length - 1 ? 'border-b' : ''}
                  ${isOpen ? 'bg-transparent' : 'bg-neutral-50'}
                `}
              >
                <AccordionTrigger
                  onClick={() => toggleSection(sectionValue)}
                  className="px-4 hover:no-underline"
                >
                  <div className="text-left font-medium text-[16px] text-neutral-900 capitalize">
                    {/* Module {index + 1}:  */}
                    {outline.key}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pt-2 pb-4 text-neutral-500 text-[14px] bg-white space-y-2">
                  {outline.value.split('\n').map((line) => (
                    <p key={line} className="whitespace-pre-line">
                      {line}
                    </p>
                  ))}
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>

      <div className="space-y-3">
        <h2 className="text-[24px] font-semibold text-neutral-900 leading-[32px]">
          {' '}
          Course Skills
        </h2>
        <div className="flex flex-col gap-3 w-max-[800px]">
          {course.skillsCovered && course.skillsCovered.length > 0 ? (
            course.skillsCovered.map((skill) => (
              <Badge
                key={skill.key}
                variant="secondary"
                className="text-[14px] text-neutral-900 rounded-md bg-neutral-100 w-fit"
              >
                {skill.key}
              </Badge>
            ))
          ) : (
            <p className="text-neutral-500">
              No skills have been added to this course.
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
