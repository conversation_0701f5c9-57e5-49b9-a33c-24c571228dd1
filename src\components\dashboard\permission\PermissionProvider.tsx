'use client';

import { useEffect, createContext, useContext, type ReactNode } from 'react';
import { usePermissionStore } from '@/zustand/store/usePermissionStore';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

interface PermissionContextType {
  isLoading: boolean;
  error: string | null;
}

const PermissionContext = createContext<PermissionContextType | undefined>(
  undefined
);

interface PermissionProviderProps {
  children: ReactNode;
}

export function PermissionProvider({ children }: PermissionProviderProps) {
  const { fetchPermissions, isLoading, error, clearPermissions } =
    usePermissionStore();
  const { user, token } = useAuthStore();

  const isAuthenticated = !!(user && token);

  useEffect(() => {
    if (isAuthenticated && user && token) {
      fetchPermissions();
    } else {
      clearPermissions();
    }
  }, [isAuthenticated, user, token, fetchPermissions, clearPermissions]);

  const value = {
    isLoading,
    error,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermissionContext() {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error(
      'usePermissionContext must be used within a PermissionProvider'
    );
  }
  return context;
}
