'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Filter, Search, X } from 'lucide-react';
import { useState, useEffect } from 'react';

type SearchFilterProps = {
  search: string;
  setSearch: (_value: string) => void;
  setShowFilterModal: (_show: boolean) => void;
  refetch: () => void; // Add refetch function prop
};

export function SearchFilter({
  search,
  setSearch,
  setShowFilterModal,
  refetch,
}: SearchFilterProps) {
  const [inputValue, setInputValue] = useState(search);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  // Update local state when external search changes
  useEffect(() => {
    setInputValue(search);
  }, [search]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeout) clearTimeout(typingTimeout);
    };
  }, [typingTimeout]);

  const handleSearch = () => {
    setSearch(inputValue);
    refetch(); // Trigger refetch whenever search changes
  };

  const handleClear = () => {
    setInputValue('');
    setSearch('');
    refetch(); // Trigger refetch when clearing
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Cancel any pending debounced search
      if (typingTimeout) clearTimeout(typingTimeout);
      handleSearch();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setInputValue(value);

    // Clear previous timeout
    if (typingTimeout) clearTimeout(typingTimeout);

    // Set new timeout for debounced search
    if (value === '' || value.length > 2) {
      setTypingTimeout(
        setTimeout(() => {
          handleSearch();
        }, 2000)
      );
    }
  };

  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center w-full border border-neutral-200 px-2 rounded-md relative">
        <Search className="w-6 h-6 text-neutral-200" />
        <Input
          placeholder="Search listing..."
          className="border-none focus:border-none ring-0 outline-none py-0 pr-8"
          value={inputValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
        />
        {inputValue && (
          <button
            onClick={handleClear}
            className="absolute right-2 text-neutral-400 hover:text-neutral-600"
            aria-label="Clear search"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
      <Button variant="outline" onClick={() => setShowFilterModal(true)}>
        <Filter className="w-4 h-4 mr-2" />
        Filter
      </Button>
    </div>
  );
}
