'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { X, CalendarIcon } from 'lucide-react';
import { formatDateDDMonthYYYY } from '@/utils';
import type { JobFilters } from '@/type/jobType';
import ResetIcon from '@/components/icons/resetIcon';

interface ListingFiltersProps {
  filters: JobFilters;
  onFilterChange: (_filters: JobFilters) => void;
  onApplyFilters: () => void;
  onResetFilters: () => void;
}

export const ListingFilterPanel: React.FC<ListingFiltersProps> = ({
  filters,
  onFilterChange,
  onApplyFilters,
  onResetFilters,
}) => {
  const [appliedFilters, setAppliedFilters] = useState<
    { key: string; value: string }[]
  >([]);
  const [isResetDisabled, setIsResetDisabled] = useState(true);
  const [showAppliedFilters, setShowAppliedFilters] = useState(false);

  const listingTypes = [
    { id: 'job', label: 'Job' },
    { id: 'apprenticeship', label: 'Apprenticeship' },
  ];

  useEffect(() => {
    const hasFilters = Object.entries(filters).some(([key, value]) => {
      return (
        value !== undefined &&
        value !== '' &&
        key !== 'status' &&
        key !== 'search' &&
        key !== 'ascending' &&
        key !== 'sortField'
      );
    });
    setIsResetDisabled(!hasFilters);
  }, [filters]);

  const isDateRangeValid = () => {
    if (filters.dateType === 'createdOn') {
      if (!filters.createdOnFrom || !filters.createdOnTo) return true;
      return new Date(filters.createdOnFrom) <= new Date(filters.createdOnTo);
    } else if (filters.dateType === 'closedDate') {
      if (!filters.closedDateFrom || !filters.closedDateTo) return true;
      return new Date(filters.closedDateFrom) <= new Date(filters.closedDateTo);
    }
    return true;
  };

  const removeFilter = (key: string) => {
    const updatedFilters = { ...filters };

    switch (key) {
      case 'Listing Type':
        updatedFilters.listingType = undefined;
        break;
      case 'Created On':
        updatedFilters.dateType = undefined;
        updatedFilters.createdOnFrom = undefined;
        updatedFilters.createdOnTo = undefined;
        break;
      case 'Closed Date':
        updatedFilters.dateType = undefined;
        updatedFilters.closedDateFrom = undefined;
        updatedFilters.closedDateTo = undefined;
        break;
    }

    const newAppliedFilters = appliedFilters.filter((f) => f.key !== key);
    setAppliedFilters(newAppliedFilters);
    setShowAppliedFilters(newAppliedFilters.length > 0);

    onFilterChange(updatedFilters);

    if (newAppliedFilters.length === 0) {
      onResetFilters();
    } else {
      onApplyFilters();
    }
  };

  const handleApplyFilters = () => {
    const newAppliedFilters: { key: string; value: string }[] = [];

    if (filters.listingType) {
      newAppliedFilters.push({
        key: 'Listing Type',
        value: filters.listingType,
      });
    }

    if (filters.dateType) {
      let dateLabel = '';
      let fromDate = '';
      let toDate = '';

      switch (filters.dateType) {
        case 'createdOn':
          dateLabel = 'Created On';
          fromDate = filters.createdOnFrom || '';
          toDate = filters.createdOnTo || '';
          break;
        case 'closedDate':
          dateLabel = 'Closed Date';
          fromDate = filters.closedDateFrom || '';
          toDate = filters.closedDateTo || '';
          break;
      }

      const dateValue = [];
      if (fromDate) {
        dateValue.push(`From: ${formatDateDDMonthYYYY(fromDate)}`);
      }
      if (toDate) {
        dateValue.push(`To: ${formatDateDDMonthYYYY(toDate)}`);
      }

      if (dateValue.length > 0) {
        newAppliedFilters.push({ key: dateLabel, value: dateValue.join(' ') });
      }
    }

    setAppliedFilters(newAppliedFilters);
    setShowAppliedFilters(newAppliedFilters.length > 0);
    onApplyFilters();
  };

  // Helper function to get the current date values based on the selected date type
  const getDateValues = () => {
    if (filters.dateType === 'createdOn') {
      return {
        fromDate: filters.createdOnFrom,
        toDate: filters.createdOnTo,
        setFromDate: (date?: string) =>
          onFilterChange({ ...filters, createdOnFrom: date }),
        setToDate: (date?: string) =>
          onFilterChange({ ...filters, createdOnTo: date }),
      };
    } else if (filters.dateType === 'closedDate') {
      return {
        fromDate: filters.closedDateFrom,
        toDate: filters.closedDateTo,
        setFromDate: (date?: string) =>
          onFilterChange({ ...filters, closedDateFrom: date }),
        setToDate: (date?: string) =>
          onFilterChange({ ...filters, closedDateTo: date }),
      };
    }

    // Default empty values
    return {
      fromDate: undefined,
      toDate: undefined,
      setFromDate: () => {},
      setToDate: () => {},
    };
  };

  const { fromDate, toDate, setFromDate, setToDate } = getDateValues();

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 max-w-full">
        {/* Listing Type Dropdown */}
        <div>
          <Select
            value={filters.listingType || ''}
            onValueChange={(value) =>
              onFilterChange({ ...filters, listingType: value })
            }
          >
            <SelectTrigger
              className={
                filters.listingType ? 'border-secondary-500 border-2' : ''
              }
            >
              <SelectValue placeholder="Listing Type" />
            </SelectTrigger>
            <SelectContent>
              {listingTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Date Filters */}
      <div className="max-w-3xl">
        <p className="text-[16px] font-semibold leading-[24px] text-neutral-900 mb-2">
          Dates:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Select
              value={filters.dateType || ''}
              onValueChange={(value) =>
                onFilterChange({
                  ...filters,
                  dateType: value as 'createdOn' | 'closedDate',
                  createdOnFrom:
                    value === 'createdOn' ? filters.createdOnFrom : undefined,
                  createdOnTo:
                    value === 'createdOn' ? filters.createdOnTo : undefined,
                  closedDateFrom:
                    value === 'closedDate' ? filters.closedDateFrom : undefined,
                  closedDateTo:
                    value === 'closedDate' ? filters.closedDateTo : undefined,
                })
              }
            >
              <SelectTrigger
                className={
                  filters.dateType ? 'border-secondary-500 border-2' : ''
                }
              >
                <SelectValue placeholder="Date Filter Name" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdOn">Created On</SelectItem>
                <SelectItem value="closedDate">Closed Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={`w-full text-left font-normal justify-between rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor] ${
                    !fromDate && 'text-muted-foreground'
                  } ${fromDate ? 'border-secondary-500' : ''} ${!isDateRangeValid() ? 'border-destructive-500' : ''}`}
                  disabled={!filters.dateType}
                >
                  {fromDate ? (
                    formatDateDDMonthYYYY(fromDate)
                  ) : (
                    <span>From: DD/MM/YYYY</span>
                  )}
                  <CalendarIcon className="mr-2 h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <div className="flex flex-col">
                  <Calendar
                    mode="single"
                    selected={fromDate ? new Date(fromDate) : undefined}
                    onSelect={(date) =>
                      setFromDate(
                        date
                          ? new Date(
                              Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate(),
                                0,
                                0,
                                0
                              )
                            ).toISOString()
                          : undefined
                      )
                    }
                    initialFocus
                  />
                  <Button
                    variant="ghost"
                    onClick={() => setFromDate(undefined)}
                    className="text-destructive-500 hover:text-destructive-600"
                  >
                    Clear
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={`w-full justify-between text-left font-normal rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor] ${
                    !toDate && 'text-muted-foreground'
                  } ${toDate ? 'border-secondary-500' : ''} ${!isDateRangeValid() ? 'border-destructive' : ''}`}
                  disabled={!filters.dateType}
                >
                  {toDate ? (
                    formatDateDDMonthYYYY(toDate)
                  ) : (
                    <span>To: DD/MM/YYYY</span>
                  )}
                  <CalendarIcon className="mr-2 h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <div className="flex flex-col">
                  <Calendar
                    mode="single"
                    selected={toDate ? new Date(toDate) : undefined}
                    onSelect={(date) =>
                      setToDate(
                        date
                          ? new Date(
                              Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate(),
                                23,
                                59,
                                59
                              )
                            ).toISOString()
                          : undefined
                      )
                    }
                    initialFocus
                  />
                  <Button
                    variant="ghost"
                    onClick={() => setToDate(undefined)}
                    className="text-destructive-500 hover:text-destructive-600"
                  >
                    Clear
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
        {!isDateRangeValid() && (
          <p className="text-destructive text-[14px] mt-1">
            Start date must be earlier than or equal to end date
          </p>
        )}
      </div>

      <div className="flex gap-2 items-center">
        <Button
          variant="outline"
          onClick={onResetFilters}
          disabled={isResetDisabled}
        >
          <ResetIcon />
          Reset
        </Button>

        <Button
          className="px-4"
          onClick={handleApplyFilters}
          disabled={!isDateRangeValid()}
        >
          Apply
        </Button>
      </div>

      {showAppliedFilters && appliedFilters.length > 0 && (
        <div className="mt-4">
          <p className="text-[16px] font-medium mb-2">Filters Applied:</p>
          <div className="flex flex-wrap gap-2">
            {appliedFilters.map((filter) => (
              <div
                key={filter.key}
                className="flex items-center bg-neutral-100 rounded-md px-3 py-1 text-[16px]"
              >
                <span className="font-medium mr-1">{filter.key}:</span>
                <span>{filter.value}</span>
                <button
                  className="ml-2 text-neutral-500 hover:text-neutral-700"
                  onClick={() => removeFilter(filter.key)}
                >
                  <X className="h-3 w-3 !text-[--bodyTextColor]" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
