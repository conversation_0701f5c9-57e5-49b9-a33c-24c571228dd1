'use client';
import type { FC } from 'react';
import { useState } from 'react';
import AssistantMessage from './assistantMessage';
import WelcomeOnboardTemplate from '@/components/auth/onboarding-stepper/WelcomeOnboardTemplate';
import { GovernmentEmblem } from '@/components/auth/governmentEmblem';

const PageLogin: FC = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => setCurrentStep(currentStep + 1);

  return (
    <div className="h-screen flex flex-col">
      <div className="bg-[url('/images/ws/confirm-info-bg.png')] fixed inset-0 w-full h-screen bg-no-repeat bg-cover bg-center -z-10`py-10 overflow-y-auto">
        {currentStep === 0 ? (
          <AssistantMessage handleNext={handleNext} />
        ) : (
          <WelcomeOnboardTemplate />
        )}
      </div>
      <GovernmentEmblem />
    </div>
  );
};

export default PageLogin;
