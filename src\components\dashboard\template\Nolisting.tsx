'use client';

import { useOverviewStats } from '@/queries';
import { Button } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getStats } from '@/zustand/services/courseServices';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { useTrainingProviderStore } from '@/zustand/store/trainerProvider';
import NewListing from '@/components/icons/NewListing';
import TrainingOpenBook from '@/components/icons/OpenBook';
import useSettingsStore from '@/zustand/store/settingsStore';

function Nolisting() {
  const { appearanceSettings } = useSettingsStore();
  const router = useRouter();
  const { user } = useAuthStore();
  const { isTrainer } = useTrainingProviderStore();
  const { data, isLoading: isJobsLoading } = useOverviewStats();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCourses: 0,
    activeCourses: 0,
    completedEnrollments: 0,
  });
  const brandColor = appearanceSettings?.brandColor;

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const statsResponse = await getStats({ createdById: user?.id });
      setStats({
        totalCourses: statsResponse.data.totalCourses,
        activeCourses: statsResponse.data.activeCourses,
        completedEnrollments: statsResponse.data.completedEnrollments,
      });
    } catch (error) {
      console.error('Error fetching statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch course stats if the user is a training provider
    if (user?.id && isTrainer) {
      fetchStats();
    } else {
      setIsLoading(false); // Make sure to set loading to false if we're not fetching
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id, isTrainer]);

  const noListings = !isJobsLoading && data?.data?.totalActiveJobs === 0;
  const noCourses = !isLoading && isTrainer && stats.totalCourses === 0;

  // Don't show anything if loading or if both have content
  if (isLoading || isJobsLoading || (!noListings && !noCourses)) {
    return null;
  }

  return (
    <div className="flex flex-col md:flex-row gap-4">
      {noListings && (
        <div className="bg-white border border-neutral-200 rounded-lg p-10 flex flex-col text-center items-center justify-center flex-1">
          <NewListing stopColor1={brandColor} stopColor2={brandColor} />
          <h3 className="text-[18px] font-medium mb-2 text-neutral-700">
            Create your first listing to find the right talent!
          </h3>
          <Button
            variant="default"
            onClick={() =>
              router.push('/dashboard/jobs-and-training/posting-a-new-listing')
            }
            className="mt-5 items-center justify-center flex py-[14px] px-[28px] gap-4 mx-auto"
          >
            <PlusIcon className="h-6 w-6 text-white" />
            Add a New Listing
          </Button>
        </div>
      )}

      {/* Only show the noCourses card if the user is a training provider */}
      {noCourses && (
        <div className="bg-white border border-neutral-200 rounded-lg p-10 flex flex-col text-center items-center justify-center flex-1">
          <TrainingOpenBook stopColor={brandColor} />
          <h3 className="text-[18px] font-medium mb-2 text-neutral-700">
            Create your first course and link it to your external content!
          </h3>
          <Button
            onClick={() => router.push('/dashboard/courses/add-a-new-course')}
            className="mt-5 items-center justify-center flex py-[14px] px-[28px] gap-4 mx-auto"
          >
            <PlusIcon className="h-6 w-6 text-white" />
            Add a New Course
          </Button>
        </div>
      )}
    </div>
  );
}

export default Nolisting;
