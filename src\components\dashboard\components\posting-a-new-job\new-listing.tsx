'use client';

import AssistantTooltip from '@/components/dashboard/common/AssistantTooltip';
import { Card } from '@/components/ui/card';
import type React from 'react';
import { useRouter } from 'next/navigation';
import { WrenchScrewDriverIcon } from '@/assets';
import BagIcon from '@/components/icons/BagIcon';
import useSettingsStore from '@/zustand/store/settingsStore';

interface ListingType {
  id: string;
  title: string;
  icon: React.ReactNode;
  route: string;
}

export default function NewListing() {
  const { appearanceSettings } = useSettingsStore();
  const router = useRouter();
  const brandColor = appearanceSettings?.brandColor;

  const listingTypes: ListingType[] = [
    {
      id: 'job',
      title: 'Job Listing',
      icon: <BagIcon storke={brandColor} height={48} width={48} />,
      route: '/dashboard/jobs-and-training/posting-a-new-listing/job',
    },
    {
      id: 'apprenticeship',
      title: 'Apprenticeship Listing',
      icon: <WrenchScrewDriverIcon stroke={brandColor} />,
      route:
        '/dashboard/jobs-and-training/posting-a-new-listing/apprenticeships',
    },
  ];

  return (
    <div className="p-6 w-full mx-auto space-y-4 mt-10">
      <div className="mb-32">
        <h1 className="text-[28px] font-semibold leading-[36px] text-neutral-900">
          {' '}
          Add a New Listing
        </h1>
      </div>

      <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
        <p className="text-neutral-700 text-left py-4 text-[18px] font-normal leading-[28px] px-6">
          What type of listing would you like to post?
        </p>
        <div className="grid md:grid-cols-2 gap-6 p-6">
          {listingTypes.map((type) => (
            <Card
              onClick={() => router.push(type.route)}
              key={type.id}
              className="p-6 cursor-pointer border border-gray-10 rounded-md"
            >
              <div className="flex flex-col items-center text-center space-y-4">
                {type.icon}
                <h2 className="text-[18px] font-medium leading-[28px] text-center text-neutral-700">
                  {type.title}
                </h2>
              </div>
            </Card>
          ))}
        </div>
      </AssistantTooltip>
    </div>
  );
}
