'use client';

import type { ReactNode } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lock } from 'lucide-react';

interface PermissionCardProps {
  permission: Permission | Permission[];
  children: ReactNode;
  options?: PermissionCheckOptions;
  title?: string;
  description?: string;
  fallback?: ReactNode;
  showAccessDenied?: boolean;
  className?: string;
  cardClassName?: string;
}

export function PermissionCard({
  permission,
  children,
  options,
  title,
  description,
  fallback,
  showAccessDenied = true,
  className = '',
  cardClassName = '',
}: PermissionCardProps) {
  const { checkPermission, isLoading } = usePermission();

  // Show loading state
  if (isLoading) {
    return (
      <Card className={`${cardClassName} ${className}`}>
        <CardHeader>
          {title && (
            <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
          )}
          {description && (
            <div className="h-4 bg-gray-200 rounded animate-pulse mt-2"></div>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const hasPermission = checkPermission(permission, options);

  // Show access denied card if no permission
  if (!hasPermission) {
    if (fallback) {
      return <div className={className}>{fallback}</div>;
    }

    if (showAccessDenied) {
      return (
        <Card className={`${cardClassName} ${className}`}>
          <CardContent className="pt-6">
            <Alert>
              <Lock className="h-4 w-4" />
              <AlertDescription>
                {title
                  ? `Access to "${title}" is restricted.`
                  : 'Access to this content is restricted.'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      );
    }

    return null;
  }

  // Render the card with permission
  return (
    <Card className={`${cardClassName} ${className}`}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent className={title || description ? '' : 'pt-6'}>
        {children}
      </CardContent>
    </Card>
  );
}
