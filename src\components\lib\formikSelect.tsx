import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useField } from 'formik';
import React from 'react';

interface FormikSelectProps {
  name: string;
  options: string[];
  placeholder: string;
}

type SelectProps = React.ComponentProps<typeof Select>;

const FormikSelect: React.FC<FormikSelectProps & SelectProps> = ({
  name,
  options,
  placeholder,
  ...selectProps
}) => {
  const [field, meta, helpers] = useField<string>(name);

  const handleValueChange = (value: string) => {
    helpers.setValue(value);
  };

  const handleBlur = () => {
    helpers.setTouched(true);
  };

  return (
    <div>
      <Select
        {...selectProps}
        value={field.value}
        onValueChange={handleValueChange}
        onOpenChange={(open) => !open && handleBlur()}
      >
        <SelectTrigger
          className={meta.error && meta.touched ? 'border-destructive-500' : ''}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error && (
        <div className="text-destructive-500 text-[16px] mt-1">
          {meta.error}
        </div>
      )}
    </div>
  );
};

export default FormikSelect;
