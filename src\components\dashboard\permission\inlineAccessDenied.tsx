'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Shield, Mail, RefreshCw, Info } from 'lucide-react';
import { usePermission } from '@/hooks/usePermission';

interface InlineAccessDeniedProps {
  message?: string;
  requiredPermission?: string | string[];
  showRefreshButton?: boolean;
  showContactSupport?: boolean;
  variant?: 'default' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Inline component for showing access denied messages within existing layouts
 */
export function InlineAccessDenied({
  message = "You don't have permission to access this content.",
  requiredPermission,
  showRefreshButton = false,
  showContactSupport = false,
  variant = 'default',
  size = 'md',
  className = '',
}: InlineAccessDeniedProps) {
  const { refreshPermissions, isLoading } = usePermission();

  const handleRefreshPermissions = async () => {
    await refreshPermissions();
  };

  const handleContactSupport = () => {
    const subject = encodeURIComponent('Permission Request');
    const body = encodeURIComponent(
      `Hello,\n\nI need access to content that requires the following permission(s):\n${
        Array.isArray(requiredPermission)
          ? requiredPermission.join(', ')
          : requiredPermission || 'Not specified'
      }\n\nPlease review my access level.\n\nThank you.`
    );
    window.open(
      `mailto:<EMAIL>?subject=${subject}&body=${body}`,
      '_blank'
    );
  };

  const getIcon = () => {
    switch (variant) {
      case 'warning':
        return <Shield className="h-4 w-4" />;
      case 'error':
        return <Shield className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'p-3 text-sm';
      case 'lg':
        return 'p-6 text-base';
      default:
        return 'p-4';
    }
  };

  const getVariantProps = () => {
    switch (variant) {
      case 'warning':
        return { variant: 'default' as const };
      case 'error':
        return { variant: 'destructive' as const };
      case 'info':
        return { variant: 'default' as const };
      default:
        return { variant: 'default' as const };
    }
  };

  return (
    <Alert
      {...getVariantProps()}
      className={`${getSizeClasses()} ${className}`}
    >
      {getIcon()}
      <AlertDescription className="space-y-3">
        <div>
          <p className="font-medium">{message}</p>
          {requiredPermission && (
            <p className="text-xs mt-1 opacity-75">
              Required:{' '}
              <code>
                {Array.isArray(requiredPermission)
                  ? requiredPermission.join(', ')
                  : requiredPermission}
              </code>
            </p>
          )}
        </div>

        {(showRefreshButton || showContactSupport) && (
          <div className="flex flex-wrap gap-2">
            {showRefreshButton && (
              <Button
                onClick={handleRefreshPermissions}
                variant="outline"
                size="sm"
                disabled={isLoading}
                className="h-8 text-xs"
              >
                <RefreshCw
                  className={`h-3 w-3 mr-1 ${isLoading ? 'animate-spin' : ''}`}
                />
                {isLoading ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}

            {showContactSupport && (
              <Button
                onClick={handleContactSupport}
                variant="outline"
                size="sm"
                className="h-8 text-xs"
              >
                <Mail className="h-3 w-3 mr-1" />
                Contact Support
              </Button>
            )}
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}
