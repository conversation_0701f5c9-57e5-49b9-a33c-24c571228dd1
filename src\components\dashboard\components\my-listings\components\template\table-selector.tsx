'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StatusCountBadge } from '@/utils';

type TabSelectorProps = {
  activeTab: 'active' | 'closed';
  setActiveTab: (_tab: 'active' | 'closed') => void;
  countActive: number;
  countClosed: number;
};

export function TabSelector({
  activeTab,
  setActiveTab,
  countActive,
  countClosed,
}: TabSelectorProps) {
  return (
    <Tabs
      value={activeTab}
      onValueChange={(value) => setActiveTab(value as 'active' | 'closed')}
      className="w-full"
    >
      <TabsList className="grid w-[372px] grid-cols-2">
        <TabsTrigger value="active">
          Active
          <StatusCountBadge
            count={countActive}
            isActive={activeTab === 'active'}
          />
        </TabsTrigger>
        <TabsTrigger value="closed">
          Closed
          <StatusCountBadge
            count={countClosed}
            isActive={activeTab === 'closed'}
          />
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
