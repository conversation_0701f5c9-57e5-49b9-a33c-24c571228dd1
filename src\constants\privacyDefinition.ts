interface Definition {
  term: string;
  definition: string;
  link?: string;
}

interface CookiesTech {
  definition?: string;
  bulletPoints?: {
    title: string;
    description: string;
  }[];
  description?: string;
  cookiesSessionPurpose?: {
    title: string;
    type: string;
    AdministeredBy: string;
    purpose: string;
  }[];
}

interface PersonalData {
  term: string;
  definition: string;
}

interface BulletPoint {
  title?: string;
  def?: string;
  par?: string;
  par1?: string;
  par2?: string;
  par3?: string;
}

interface Data {
  par1?: string;
  par2?: string;
  par3?: string;
  par4?: string;
}

export const privacyDefinitions: Definition[] = [
  {
    term: 'Account',
    definition:
      'means a unique account created for You to access our Service or parts of our Service.',
  },
  {
    term: 'Affiliate',
    definition:
      'means an entity that controls, is controlled by or is under common control with a party, where "control" means ownership of 50% or more of the shares, equity interest or other securities entitled to vote for election of directors or other managing authority.',
  },
  {
    term: 'Company',
    definition:
      '(referred to as either "the Company", "We", "Us" or "Our" in this Agreement) refers to Whiteshield Partners Limited, ICD Brookfield Place Level 27, L27-02 312 Al Mustaqbal St. DIFC Dubai, UAE. For the purpose of the GDPR, the Company is the Data Controller.',
  },
  {
    term: 'Cookies',
    definition:
      'are small files that are placed on Your computer, mobile device or any other device by a website, containing the details of Your browsing history on that website among its many uses.',
  },
  {
    term: 'Country',
    definition: 'refers to: United Arab Emirates.',
  },
  {
    term: 'Data Controller',
    definition:
      'For the purposes of the GDPR (General Data Protection Regulation), refers to the Company as the legal person which alone or jointly with others determines the purposes and means of the processing of Personal Data.',
  },
  {
    term: 'Device',
    definition:
      'means any device that can access the Service such as a computer, a cellphone, or a digital tablet.',
  },
  {
    term: 'GDPR',
    definition: 'refers to EU General Data Protection Regulation.',
  },
  {
    term: 'Personal Data',
    definition:
      'is any information that relates to an identified or identifiable individual.',
  },
  {
    term: 'Service',
    definition: 'refers to the Website.',
  },
  {
    term: 'Service Provider',
    definition:
      'means any natural or legal person who processes the data on behalf of the Company. It refers to third-party companies or individuals employed by the Company to facilitate the Service, to provide the Service on behalf of the Company, to perform services related to the Service or to assist the Company in analyzing how the Service is used. For the purpose of the GDPR, Service Providers are considered Data Processors.',
  },
  {
    term: 'Usage Data',
    definition:
      'refers to data collected automatically, either generated by the use of the Service or from the Service infrastructure itself (for example, the duration of a page visit).',
  },
  {
    term: 'Website',
    definition: 'refers to Career Navigator, accessible from',
    link: 'https://career.whiteshield.com',
  },
  {
    term: 'You',
    definition:
      'means the individual accessing or using the Service, or the company, or other legal entity on behalf of which such individual is accessing or using the Service, as applicable. Under GDPR, You can be referred to as the Data Subject or as the User as you are the individual using the Service.',
  },
];

export const TrackingTechnologiesCookies: CookiesTech[] = [
  {
    definition:
      'We use Cookies and similar tracking technologies to track the activity on Our Service and store certain information. Tracking technologies used are beacons, tags, and scripts to collect and track information and to improve and analyze Our Service. The technologies We use may include:',
    bulletPoints: [
      {
        title: 'Cookies or Browser Cookies',
        description:
          'A cookie is a small file placed on Your Device. You can instruct Your browser to refuse all Cookies or to indicate when a Cookie is being sent. However, if You do not accept Cookies, You may not be able to use some parts of our Service. Unless you have adjusted Your browser setting so that it will refuse Cookies, our Service may use Cookies.',
      },
      {
        title: 'Web Beacons',
        description:
          'Certain sections of our Service and our emails may contain small electronic files known as web beacons (also referred to as clear gifs, pixel tags, and single-pixel gifs) that permit the Company, for example, to count users who have visited those pages or opened an email and for other related website statistics (for example, recording the popularity of a certain section and verifying system and server integrity).',
      },
    ],
    description:
      'Cookies can be "Persistent" or "Session" Cookies. Persistent Cookies remain on Your personal computer or mobile device when You go offline, while Session Cookies are deleted as soon as You close Your web browser. Learn more about cookies on the Privacy Policies website article.',
    cookiesSessionPurpose: [
      {
        title: 'Necessary / Essential Cookies',
        type: 'Session Cookies',
        AdministeredBy: 'Us',
        purpose:
          'These Cookies are essential to provide You with services available through the Website and to enable You to use some of its features. They help to authenticate users and prevent fraudulent use of user accounts. Without these Cookies, the services that You have asked for cannot be provided, and We only use these Cookies to provide You with those services.',
      },
      {
        title: 'Cookies Policy / Notice Acceptance Cookies',
        type: 'Persistent Cookies',
        AdministeredBy: 'Us',
        purpose:
          'These Cookies identify if users have accepted the use of cookies on the Website.',
      },
      {
        title: 'Functionality Cookies',
        type: 'Persistent Cookies',
        AdministeredBy: 'Us',
        purpose:
          'These Cookies allow us to remember choices You make when You use the Website, such as remembering your login details or language preference. The purpose of these Cookies is to provide You with a more personal experience and to avoid You having to re-enter your preferences every time You use the Website.',
      },
      {
        title: 'Tracking and Performance Cookies',
        type: 'Persistent Cookies',
        AdministeredBy: 'Third-Parties',
        purpose:
          'These Cookies are used to track information about traffic to the Website and how users use the Website. The information gathered via these Cookies may directly or indirectly identify you as an individual visitor. This is because the information collected is typically linked to a pseudonymous identifier associated with the device you use to access the Website. We may also use these Cookies to test new pages, features or new functionality of the Website to see how our users react to them.',
      },
    ],
  },
];

export const PersonalData: PersonalData[] = [
  {
    term: 'To provide and maintain our Service:',
    definition: 'including to monitor the usage of our Service.',
  },
  {
    term: 'To manage Your Account:',
    definition:
      'Your registration as a user of the Service. The Personal Data You provide can give You access to different functionalities of the Service that are available to You as a registered user',
  },
  {
    term: 'For the performance of a contract:',
    definition:
      'the development, compliance and undertaking of the purchase contract for the products, items or services You have purchased or of any other contract with Us through the Service.',
  },
  {
    term: 'To contact You: ',
    definition:
      'To contact You by email, telephone calls, SMS, or other equivalent forms of electronic communication, such as a mobile application`s push notifications regarding updates or informative communications related to the functionalities, products or contracted services, including the security updates, when necessary or reasonable for their implementation.',
  },
  {
    term: 'To provide You:',
    definition:
      'with news, special offers and general information about other goods, services and events which we offer that are similar to those that you have already purchased or enquired about unless You have opted not to receive such information.',
  },
  {
    term: 'To manage Your requests: ',
    definition: 'To attend and manage Your requests to Us.',
  },
  {
    term: 'For business transfers:',
    definition:
      'We may use Your information to evaluate or conduct a merger, divestiture, restructuring, reorganization, dissolution, or other sale or transfer of some or all of Our assets, whether as a going concern or as part of bankruptcy, liquidation, or similar proceeding, in which Personal Data held by Us about our Service users is among the assets transferred.',
  },
  {
    term: 'For other purposes:',
    definition:
      'We may use Your information for other purposes, such as data analysis, identifying usage trends, determining the effectiveness of our promotional campaigns and to evaluate and improve our Service, products, services, marketing and your experience. ',
  },
];

export const BulletPoints: BulletPoint[] = [
  {
    title: 'With Service Providers',
    def: 'We may share Your personal information with Service Providers to monitor and analyze the use of our Service, to contact You.',
  },
  {
    title: 'For business transfers',
    def: 'We may share or transfer Your personal information in connection with, or during negotiations of, any merger, sale of Company assets, financing, or acquisition of all or a portion of Our business to another company.',
  },
  {
    title: 'With Affiliates',
    def: 'We may share Your information with Our affiliates, in which case we will require those affiliates to honor this Privacy Policy. Affiliates include Our parent company and any other subsidiaries, joint venture partners or other companies that We control or that are under common control with Us.',
  },
  {
    title: 'With business partners',
    def: 'We may share Your information with Our business partners to offer You certain products, services or promotions.',
  },
  {
    title: 'With other users',
    def: 'when You share personal information or otherwise interact in the public areas with other users, such information may be viewed by all users and may be publicly distributed outside.',
  },
  {
    title: 'With Your consent',
    def: 'We may disclose Your personal information for any other purpose with Your consent.',
  },
];

export const RetentionPersonalData: Data[] = [
  {
    par1: '    The Company will retain Your Personal Data only for as long as is necessary for the purposes set out in this Privacy Policy. We will retain and use Your Personal Data to the extent necessary to comply with our legal obligations (for example, if we are required to retain your data to comply with applicable laws), resolve disputes, and enforce our legal agreements and policies.',
    par2: 'The Company will also retain Usage Data for internal analysis purposes. Usage Data is generally retained for a shorter period of time, except when this data is used to strengthen the security or to improve the functionality of Our Service, or We are legally obligated to retain this data for longer time periods.',
  },
];

export const TransferPersonalData: Data[] = [
  {
    par1: 'Your information, including Personal Data, is processed at the Company`s operating offices and in any other places where the parties involved in the processing are located. It means that this information may be transferred to — and maintained on — computers located outside of Your state, province, country or other governmental jurisdiction where the data protection laws may differ than those from Your jurisdiction.',
    par2: 'Your consent to this Privacy Policy followed by Your submission of such information represents Your agreement to that transfer. The Company will take all steps reasonably necessary to ensure that Your data is treated securely and in accordance with this Privacy Policy and no transfer of Your Personal Data will take place to an organization or a country unless there are adequate controls in place including the security of Your data and other personal information.',
  },
];

export const DeletePersonalData: Data[] = [
  {
    par1: 'You have the right to delete or request that We assist in deleting the Personal Data that We have collected about You.',
    par2: 'Our Service may give You the ability to delete certain information about You from within the Service.',
    par3: 'You may update, amend, or delete Your information at any time by signing in to Your Account, if you have one, and visiting the account settings section that allows you to manage Your personal information. You may also contact Us to request access to, correct, or delete any personal information that You have provided to Us.',
    par4: 'Please note, however, that We may need to retain certain information when we have a legal obligation or lawful basis to do so.',
  },
];

export const BusinessTransactions: Data[] = [
  {
    par1: 'If the Company is involved in a merger, acquisition or asset sale, Your Personal Data may be transferred. We will provide notice before Your Personal Data is transferred and becomes subject to a different Privacy Policy.',
  },
];

export const LawEnforcement: Data[] = [
  {
    par1: 'Under certain circumstances, the Company may be required to disclose Your Personal Data if required to do so by law or in response to valid requests by public authorities (e.g. a court or a government agency).',
  },
];

export const OtherLegalRequirements: Data[] = [
  {
    par1: 'The Company may disclose Your Personal Data in the good faith belief that such action is necessary to:',
  },
];

export const OtherLegalRequirementsBulletPoints: BulletPoint[] = [
  { def: 'Comply with a legal obligation' },
  { def: 'Protect and defend the rights or property of the Company' },
  {
    def: 'Prevent or investigate possible wrongdoing in connection with the Service',
  },
  { def: 'Protect the personal safety of Users of the Service or the public' },
];

export const SecurityPersonalData: Data[] = [
  {
    par1: 'The security of Your Personal Data is important to Us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure. While We strive to use commercially acceptable means to protect Your Personal Data, We cannot guarantee its absolute security.',
  },
];

export const DetailedInfoPersonalData: Data[] = [
  {
    par1: 'The Service Providers We use may have access to Your Personal Data. These third-party vendors collect, store, use, process and transfer information about Your activity on Our Service in accordance with their Privacy Policies.',
  },
];

export const AnalyticsData: Data[] = [
  {
    par1: 'We may use third-party Service providers to monitor and analyze the use of our Service.',
    par2: 'Google Analytics is a web analytics service offered by Google that tracks and reports website traffic. Google uses the data collected to track and monitor the use of our Service. This data is shared with other Google services. Google may use the collected data to contextualize and personalize the ads of its own advertising network. You can opt-out of having made your activity on the Service available to Google Analytics by installing the Google Analytics opt-out browser add-on. The add-on prevents the Google Analytics JavaScript (ga.js, analytics.js and dc.js) from sharing information with Google Analytics about visits activity. For more information on the privacy practices of Google, please visit the Google Privacy & Terms web page:',
    par3: 'https://policies.google.com/privacy',
  },
];

export const EmailMarketing: Data[] = [
  {
    par1: 'We may use Your Personal Data to contact You with newsletters, marketing or promotional materials and other information that may be of interest to You. You may opt-out of receiving any, or all, of these communications from Us by following the unsubscribe link or instructions provided in any email We send or by contacting Us.',
    par2: 'We may use Email Marketing Service Providers to manage and send emails to You.',
    par3: 'Their Privacy Policy can be viewed at ',
    par4: 'https://learn.microsoft.com/en-us/azure/communication-services/concepts/privacy',
  },
];

export const LegalsGDPR: BulletPoint[] = [
  {
    par: 'We may process Personal Data under the following conditions:',
  },
  {
    title: 'Consent',
    def: 'Processing Personal Data is necessary for compliance with a legal obligation to which the Company is subject.',
  },
  {
    title: 'Legal obligations',
    def: 'We may share or transfer Your personal information in connection with, or during negotiations of, any merger, sale of Company assets, financing, or acquisition of all or a portion of Our business to another company.',
  },
  {
    title: 'Vital interests',
    def: 'Processing Personal Data is necessary in order to protect Your vital interests or of another natural person.',
  },
  {
    title: 'Public interests',
    def: 'Processing Personal Data is related to a task that is carried out in the public interest or in the exercise of official authority vested in the Company.',
  },
  {
    title: 'Legitimate interests',
    def: 'Processing Personal Data is necessary for the purposes of the legitimate interests pursued by the Company.',
  },
  {
    par1: 'In any case, the Company will gladly help to clarify the specific legal basis that applies to the processing, and in particular whether the provision of Personal Data is a statutory or contractual requirement, or a requirement necessary to enter into a contract.',
  },
];

export const RightGDPR: BulletPoint[] = [
  {
    par: 'The Company undertakes to respect the confidentiality of Your Personal Data and to guarantee You can exercise Your rights.',
  },
  {
    par1: 'You have the right under this Privacy Policy, and by law if You are within the EU, to:',
  },
  {
    title: 'Request access to Your Personal Data.',
    def: 'The right to access, update or delete the information We have on You. Whenever made possible, you can access, update or request deletion of Your Personal Data directly within Your account settings section. If you are unable to perform these actions yourself, please contact Us to assist You. This also enables You to receive a copy of the Personal Data We hold about You.',
  },
  {
    title: 'Request correction of the Personal Data that We hold about You. ',
    def: 'You have the right to have any incomplete or inaccurate information We hold about You corrected.',
  },
  {
    title: 'Object to processing of Your Personal Data.',
    def: 'This right exists where We are relying on a legitimate interest as the legal basis for Our processing and there is something about Your particular situation, which makes You want to object to our processing of Your Personal Data on this ground. You also have the right to object where We are processing Your Personal Data for direct marketing purposes.',
  },
  {
    title: 'Request erasure of Your Personal Data',
    def: 'You have the right to ask Us to delete or remove Personal Data when there is no good reason for Us to continue processing it.',
  },
  {
    title: 'Request the transfer of Your Personal Data.',
    def: 'We will provide to You, or to a third-party You have chosen, Your Personal Data in a structured, commonly used, machine-readable format. Please note that this right only applies to automated information which You initially provided consent for Us to use or where We used the information to perform a contract with You.',
  },
  {
    title: 'Withdraw Your consent.',
    def: 'You have the right to withdraw Your consent on using your Personal Data. If You withdraw Your consent, We may not be able to provide You with access to certain specific functionalities of the Service.',
  },
];

export const ProtectionRights: Data[] = [
  {
    par1: 'You may exercise Your rights of access, rectification, cancellation and opposition by contacting Us. Please note that we may ask You to verify Your identity before responding to such requests. If You make a request, We will try our best to respond to You as soon as possible.',
    par2: 'You have the right to complain to a Data Protection Authority about Our collection and use of Your Personal Data. For more information, if You are in the European Economic Area (EEA), please contact Your local data protection authority in the EEA.',
  },
];

export const ChildrenPrivacy: Data[] = [
  {
    par1: 'Our Service does not address anyone under the age of 13. We do not knowingly collect personally identifiable information from anyone under the age of 13. If You are a parent or guardian and You are aware that Your child has provided Us with Personal Data, please contact Us. If We become aware that We have collected Personal Data from anyone under the age of 13 without verification of parental consent, We take steps to remove that information from Our servers.',
    par2: 'If We need to rely on consent as a legal basis for processing Your information and Your country requires consent from a parent, We may require Your parent`s consent before We collect and use that information.',
  },
];

export const LinkOtherWebsites: Data[] = [
  {
    par1: 'Our Service may contain links to other websites that are not operated by Us. If You click on a third party link, You will be directed to that third party`s site. We strongly advise You to review the Privacy Policy of every site You visit.',
    par2: 'We have no control over and assume no responsibility for the content, privacy policies or practices of any third party sites or services.',
  },
];

export const ChangePrivacyPolicy: Data[] = [
  {
    par1: 'We may update Our Privacy Policy from time to time. We will notify You of any changes by posting the new Privacy Policy on this page.',
    par2: 'We will let You know via email and/or a prominent notice on Our Service, prior to the change becoming effective and update the "Last updated" date at the top of this Privacy Policy.',
    par3: 'You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.',
  },
];

export const Contact: BulletPoint[] = [
  {
    par1: 'If you have any questions about this Privacy Policy, You can contact us:',
  },
  { title: 'By email:', def: '<EMAIL>' },
];
