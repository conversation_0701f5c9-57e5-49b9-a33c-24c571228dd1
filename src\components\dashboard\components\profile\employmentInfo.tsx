import React from 'react';

const mockData = {
  Status: 'Full time',
  Current_job_start_date: '32 October 2021',
  Profession: 'Project Manager',
  Category: 'Private',
  Salary: 'KWD 380',
  Pensionable_monthly_salary: '<EMAIL>',
};

interface UserInfo {
  label: string;
  value: string;
}

function InfoSection({
  title,
  data,
  isLast = false,
}: {
  title: string;
  data: UserInfo[];
  isLast?: boolean;
}) {
  return (
    <div className={`my-4 py-4 ${!isLast ? 'border-b border-[#D7DAED]' : ''}`}>
      <h3 className="text-[#111827] text-[18px] font-semibold leading-[28px] mb-2">
        {title}
      </h3>
      {data.map((item) => (
        <div key={item.label} className="flex justify-between mb-2">
          <p className="text-[16px] font-semibold leading-[24px] text-[#646A77]">
            {item.label}
          </p>
          <p className="text-[16px] font-semibold leading-[24px] text-[#646A77]">
            {item.value}
          </p>
        </div>
      ))}
    </div>
  );
}

function EmploymentInformation({
  user,
}: {
  user: {
    Status: string;
    Current_job_start_date: string;
    Profession: string;
    Category: string;
    Salary: string;
    Pensionable_monthly_salary: string;
  };
}) {
  return (
    <div className="py-4 px-4 mt-4 border border-[#D7DAED] rounded-md">
      <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-[#111827]">
        Personal Information
      </h4>

      <InfoSection
        title="Employment"
        data={[
          { label: 'Status:', value: user.Status },
          {
            label: 'Current job start date:',
            value: user.Current_job_start_date,
          },
          { label: 'Profession:', value: user.Profession },
          { label: 'Category:', value: user.Category },
        ]}
      />
      <InfoSection
        title="Salary"
        data={[
          { label: 'Monthly salary:', value: user.Salary },
          {
            label: 'Pensionable monthly salary:',
            value: user.Pensionable_monthly_salary,
          },
        ]}
        isLast={true}
      />
    </div>
  );
}

export default function EmploymentInformationWrapper() {
  return <EmploymentInformation user={mockData} />;
}
