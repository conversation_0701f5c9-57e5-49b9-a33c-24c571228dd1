import { create } from 'zustand';
import jobService from '@/zustand/services/jobServices';
import type { Job, JobState, StatusCounts } from '@/type/jobType';
import { useNotificationStore } from '../user/notificationStore';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

const { user } = useAuthStore.getState();

const initialState: Omit<JobState, 'jobs' | 'currentJob' | 'total'> = {
  id: '',
  userId: user?.id || '',
  jobId: '',
  appliedDate: '',
  status: 'Active',
  isLoading: false,
  page: 1,
  size: 10,
  searchTerm: '',
  ascending: true,
  sortField: '',
  IncludeStatusCounts: true,
  statusCounts: {
    Active: 0,
    Closed: 0,
    Draft: 0,
    Archived: 0,
  },
  createdOnFrom: '',
  createdOnTo: '',
};

export const useJobStore = create<
  Omit<JobState, 'jobs' | 'allJobs' | 'currentJob' | 'total'> & {
    setPage: (_page: number) => void;
    setSearchTerm: (_search: string) => void;
    setStatus: (_status: string) => void;
    setUserId: (_id: string | null) => void;
    setAscending: (_updater: boolean | ((_prev: boolean) => boolean)) => void;
    setSortField: (_sortField: string) => void;
    setIncludeStatusCounts: (_IncludeStatusCounts: boolean) => void;
    setStatusCounts: (_statusCounts: StatusCounts) => void;
  }
>()((set) => ({
  ...initialState,

  setPage: (page: number) => {
    set({ page });
  },

  setSearchTerm: (search: string) => {
    set({ searchTerm: search, page: 1 });
  },

  setStatus: (status: string) => {
    set({ status });
  },

  setUserId: (userId: string | null) => {
    set({ userId, page: 1 });
  },

  setAscending: (updater: boolean | ((_prev: boolean) => boolean)) => {
    set((state) => ({
      ascending:
        typeof updater === 'function' ? updater(state.ascending) : updater,
    }));
  },

  setSortField: (sortField: string) => {
    set({ sortField, page: 1 });
  },

  setIncludeStatusCounts: (IncludeStatusCounts: boolean) => {
    set({ IncludeStatusCounts });
  },

  setStatusCounts: (statusCounts: StatusCounts) => {
    set({ statusCounts });
  },
}));

export const useJobs = async (status: 'Active' | 'Closed') => {
  const { page, size, searchTerm } = useJobStore.getState();
  return await jobService.getJobs(page, size, searchTerm, undefined, status);
};

export const useAllJobs = async () => {
  const { searchTerm } = useJobStore.getState();
  return await jobService.getJobs(1, 1000, undefined, searchTerm);
};

export const useJobById = async (id: string) => {
  return await jobService.getJobById(id);
};

export const useJobsByUserId = async (userId: string) => {
  return await jobService.getJobsByUserId(userId);
};

export const useAddJob = () => {
  const { showNotification } = useNotificationStore.getState();

  const addJob = async (job: Job) => {
    try {
      await jobService.addJob(job);
      showNotification('Job added successfully!', 'success');
    } catch (error) {
      console.error('Failed to add job', error);
      showNotification('Error adding job', 'error');
    }
  };

  return { addJob };
};

export const useUpdateJob = () => {
  const { showNotification } = useNotificationStore.getState();

  const updateJob = async ({ id, job }: { id: string; job: Partial<Job> }) => {
    try {
      await jobService.updateJob(id, job);
      showNotification('Job updated successfully!', 'success');
    } catch (error) {
      console.error('Failed to update job', error);
      showNotification('Error updating job', 'error');
    }
  };

  return { updateJob };
};

export const useUpdateJobStatus = () => {
  const { showNotification } = useNotificationStore.getState();

  const updateJobStatus = async ({
    id,
    status,
  }: {
    id: string;
    status: string;
  }) => {
    try {
      await jobService.updateJobStatus(id, status);
      showNotification('Job status updated successfully!', 'success');
    } catch (error) {
      console.error('Failed to update job status', error);
      showNotification('Error updating job status', 'error');
    }
  };

  return { updateJobStatus };
};

export const useDeleteJob = () => {
  const { showNotification } = useNotificationStore.getState();

  const deleteJob = async (id: string) => {
    try {
      await jobService.deleteJob(id);
      showNotification('Job deleted successfully!', 'success');
    } catch (error) {
      console.error('Failed to delete job', error);
      showNotification('Error deleting job', 'error');
    }
  };

  return { deleteJob };
};
