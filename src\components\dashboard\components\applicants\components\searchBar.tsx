'use client';

import type React from 'react';

import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface SearchBarProps {
  search: string;
  onSearchChange: (_value: string) => void;
  onSearchSubmit: () => void;
}

export default function SearchBar({
  search,
  onSearchChange,
  onSearchSubmit,
}: SearchBarProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearchSubmit();
    }
  };

  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
      <Input
        type="text"
        placeholder="Search applicants..."
        value={search}
        onChange={(e) => onSearchChange(e.target.value)}
        onKeyDown={handleKeyDown}
        className="pl-10 w-full"
      />
    </div>
  );
}
