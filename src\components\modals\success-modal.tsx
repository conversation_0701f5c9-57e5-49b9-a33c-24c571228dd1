'use client';

import React from 'react';
import Confetti from 'react-confetti';
import * as Dialog from '@radix-ui/react-dialog';
import AssistantTooltip from '../dashboard/common/AssistantTooltip';
import { Button } from '../ui/button';
import { useRouter } from 'next/navigation';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';

interface SuccessModalProps {
  open: boolean;
  setOpen: (_open: boolean) => void;
  onPostAnotherListing: () => void;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  open,
  setOpen,
  onPostAnotherListing,
}) => {
  const router = useRouter();

  const handleHome = () => {
    router.push('/dashboard');
  };

  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 flex justify-center items-center bg-[#0000]/40" />
        {open && (
          <div className="absolute top-0 left-0 w-[50vw] h-full">
            <Confetti
              width={window.innerWidth / 2}
              height={window.innerHeight}
              numberOfPieces={150}
              gravity={0.1}
              confettiSource={{ x: 0, y: 0, w: 0, h: window.innerHeight }}
            />
          </div>
        )}

        {open && (
          <div className="absolute top-0 right-0 w-[50vw] h-full">
            <Confetti
              width={window.innerWidth / 2}
              height={window.innerHeight}
              numberOfPieces={150}
              gravity={0.1}
              confettiSource={{
                x: window.innerWidth / 2,
                y: 0,
                w: 0,
                h: window.innerHeight,
              }}
            />
          </div>
        )}

        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-4 pb-4 rounded-lg w-[600px]">
          <Dialog.Title asChild>
            <VisuallyHidden>Success</VisuallyHidden>
          </Dialog.Title>

          <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
            <div className="text-left space-y-2">
              <h2 className="text-[28px] font-semibold text-neutral-900 leading-[36px]">
                🎉 Success!
              </h2>
              <p className="text-neutral-500 font-normal text-[18px] mt-2 w-full ">
                Top talents can now apply, and applications will start coming in
                soon.
              </p>
              <p className="text-neutral-500 font-normal text-[18px] py-4 leading-[28px]">
                What would you like to do next?
              </p>

              <div className="mt-4 space-y-4">
                <Dialog.Close asChild>
                  <Button
                    onClick={onPostAnotherListing}
                    variant={'default'}
                    className="w-full px-[28px] py-[14px] h-[48px]"
                  >
                    Post Another Listing
                  </Button>
                </Dialog.Close>

                <Button
                  onClick={handleHome}
                  variant="outline"
                  className="w-full px-[28px] py-[14px] h-[48px]"
                >
                  Return Home
                </Button>
              </div>
            </div>
          </AssistantTooltip>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default SuccessModal;
