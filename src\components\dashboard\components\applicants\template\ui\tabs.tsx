// components/Tabs.tsx
import React from 'react';

interface TabProps {
  activeTab: string;
  setActiveTab: (_tab: string) => void;
  countNew: number;
  countShortlist: number;
  countContacted: number;
  countRejected: number;
  countHired: number;
}

export const Tabs: React.FC<TabProps> = ({
  activeTab,
  setActiveTab,
  countNew,
  countShortlist,
  countContacted,
  countRejected,
  countHired,
}) => {
  return (
    <div className="flex space-x-4 mb-4">
      {[
        { label: 'New', status: 'Pending', count: countNew },
        { label: 'Shortlist', status: 'Shortlisted', count: countShortlist },
        { label: 'Contacted', status: 'Contacted', count: countContacted },
        { label: 'Rejected', status: 'Rejected', count: countRejected },
        { label: 'Hired', status: 'Hired', count: countHired },
      ].map((tab) => (
        <button
          key={tab.status}
          onClick={() => setActiveTab(tab.status)}
          className={`px-4 py-2 rounded-md ${
            activeTab === tab.status
              ? 'border-2 border-blue-500 text-blue-500'
              : 'text-neutral-500'
          }`}
        >
          {tab.label}
          <span
            className={`ml-2 px-2 py-1 text-[16px] font-medium rounded-full ${
              activeTab === tab.status
                ? 'bg-primary-500 text-white'
                : 'bg-neutral-300 text-neutral-700'
            }`}
          >
            {tab.count}
          </span>
        </button>
      ))}
    </div>
  );
};
