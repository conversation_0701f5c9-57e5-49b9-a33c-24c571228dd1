'use client';
import { useEffect } from 'react';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { Toaster, toast } from 'react-hot-toast';

const Notification = () => {
  const { message, type, clearNotification } = useNotificationStore();

  useEffect(() => {
    if (message) {
      if (type === 'success') {
        toast.success(message);
      } else if (type === 'error') {
        toast.error(message);
      }

      setTimeout(clearNotification, 3000);
    }
  }, [message, type, clearNotification]);

  return <Toaster position="top-center" />;
};

export default Notification;
