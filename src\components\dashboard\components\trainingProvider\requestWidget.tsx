'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { checkTrainingProviderAccess } from '@/zustand/services/trainingProviderServices';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { TrainingProviderRequestModal } from './trainingProviderModal';

export const RequestAccessWidget: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuthStore();

  const checkAccess = async () => {
    if (user) {
      setIsLoading(true);
      try {
        const access = await checkTrainingProviderAccess();
        setHasAccess(access);
      } catch (error) {
        console.error('Error checking access:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    checkAccess();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const handleSuccess = () => {
    const url = new URL(window.location.href);
    url.searchParams.set('refresh', Date.now().toString());
    window.location.href = url.toString();
  };

  if (isLoading) {
    return null;
  }

  if (hasAccess) {
    return null;
  }

  if (isLoading) {
    return null;
  }

  if (hasAccess) {
    return null;
  }

  return (
    <div className="bg-[#FFFFFF]/10 shadow-md rounded-lg relative bottom-2 px-5 py-1 text-white-900">
      <h3 className="text-[16px] font-medium mb-3 !text-white">
        Want to become a training provider?
      </h3>
      <Button
        variant="default"
        className="w-full bg-white text-[16px] font-medium text-primary-500  hover:bg-neutral-100"
        onClick={() => setIsModalOpen(true)}
      >
        Request Access
      </Button>

      <TrainingProviderRequestModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleSuccess}
      />
    </div>
  );
};
