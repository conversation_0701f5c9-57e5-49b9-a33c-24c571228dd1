import React from 'react';

interface CashAdminProps {
  width?: number;
  height?: number;
  stroke?: string;
}

const CashAdmin: React.FC<CashAdminProps> = ({
  width = 32,
  height = 32,
  stroke = '#043C5B',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_5037_130732)">
        <path
          d="M6.6665 11.9999V9.06661C6.6665 8.47994 7.1465 7.99994 7.73317 7.99994H29.5998C30.1865 7.99994 30.6665 8.47994 30.6665 9.06661V20.2666C30.6665 20.8533 30.1865 21.3333 29.5998 21.3333H26.6665M3.73317 11.9999H25.5998C25.8827 11.9999 26.154 12.1123 26.3541 12.3124C26.5541 12.5124 26.6665 12.7837 26.6665 13.0666V24.2666C26.6665 24.5495 26.5541 24.8208 26.3541 25.0209C26.154 25.2209 25.8827 25.3333 25.5998 25.3333H3.73317C3.45027 25.3333 3.17896 25.2209 2.97892 25.0209C2.77888 24.8208 2.6665 24.5495 2.6665 24.2666V13.0666C2.6665 12.7837 2.77888 12.5124 2.97892 12.3124C3.17896 12.1123 3.45027 11.9999 3.73317 11.9999ZM15.9998 18.6666C15.9998 19.0202 15.8594 19.3594 15.6093 19.6094C15.3593 19.8595 15.0201 19.9999 14.6665 19.9999C14.3129 19.9999 13.9737 19.8595 13.7237 19.6094C13.4736 19.3594 13.3332 19.0202 13.3332 18.6666C13.3332 18.313 13.4736 17.9738 13.7237 17.7238C13.9737 17.4737 14.3129 17.3333 14.6665 17.3333C15.0201 17.3333 15.3593 17.4737 15.6093 17.7238C15.8594 17.9738 15.9998 18.313 15.9998 18.6666Z"
          stroke={stroke}
          strokeWidth="2"
          strokeMiterlimit="10"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_5037_130732">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CashAdmin;
