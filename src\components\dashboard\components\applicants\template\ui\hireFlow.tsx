'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface HireFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (_closeJob?: boolean) => Promise<void>;
  applicantName: string;
  positionName?: string;
  jobId?: string;
}

export function HireFlowModal({
  isOpen,
  onClose,
  onConfirm,
  applicantName,
  positionName,
}: HireFlowModalProps) {
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const handleClose = () => {
    setStep(0);
    onClose();
  };

  const handleProceedToListingOptions = () => setStep(1);

  const handleKeepListingOpen = async () => {
    setIsLoading(true);
    try {
      await onConfirm(false);
      handleClose();
    } catch (error) {
      console.error('Error marking as hired:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseListing = () => setStep(2);

  const handleConfirmCloseListing = async () => {
    setIsLoading(true);
    try {
      await onConfirm(true);
      handleClose();
    } catch (error) {
      console.error('Failed to close listing:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      {step === 0 && (
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-[24px] text-neutral-900 font-semibold leading-[32px]">
              Mark as Hired
            </DialogTitle>
          </DialogHeader>
          <DialogDescription className="text-[18px] text-neutral-500 font-normal leading-[28px]">
            Are you sure you want to mark this applicant as hired?
          </DialogDescription>
          <div className="py-2">
            <ul className="list-disc pl-8 space-y-1 text-[18px] font-semibold leading-[28px] text-neutral-900">
              <li>
                {applicantName}
                {positionName && ` for the position ${positionName}`}
              </li>
            </ul>
          </div>
          <DialogFooter className="flex sm:justify-end gap-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleProceedToListingOptions}>
              Mark as Hired
            </Button>
          </DialogFooter>
        </DialogContent>
      )}

      {step === 1 && (
        <DialogContent className="w-[440px]">
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
              Close Listing?
            </DialogTitle>
          </DialogHeader>
          <DialogDescription className="text-[18px] font-normal text-neutral-500 leading-[28px]">
            Would you also like to close this listing?
          </DialogDescription>
          <div className="py-2">
            <ul className="list-disc pl-5 space-y-1 text-[18px] font-semibold leading-[28px] text-neutral-700">
              <li>{positionName || 'Unknown Position'}</li>
            </ul>
          </div>
          <p className="text-neutral-500 text-[18px] font-normal leading-[28px]">
            Keep this listing open if you would like to continue hiring for this
            role.
          </p>
          <div className="flex flex-col gap-4">
            <Button
              onClick={handleCloseListing}
              className="w-full h-[48px] py-[14px] px-[28px] text-[18px]"
            >
              Close Listing
            </Button>
            <Button
              className="w-full h-[48px] py-[14px] px-[28px] text-[18px]"
              variant="outline"
              onClick={handleKeepListingOpen}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Keep Listing Open'
              )}
            </Button>
          </div>
        </DialogContent>
      )}

      {step === 2 && (
        <DialogContent className="w-[440px]">
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
              Confirm Close
            </DialogTitle>
          </DialogHeader>
          <DialogDescription className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Are you sure you want to close this listing?
          </DialogDescription>
          <DialogFooter className="flex sm:justify-end ">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleConfirmCloseListing} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Confirm Close Listing'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
}
