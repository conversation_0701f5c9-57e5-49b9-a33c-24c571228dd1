'use client';

import React from 'react';
import Confetti from 'react-confetti';
import * as Dialog from '@radix-ui/react-dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { Button } from '../ui/button';
import Image from 'next/image';
import DashboardSuccessImage from '@/assets/images/dashboard/registration-success.svg';
import { Check } from 'lucide-react';

interface SuccessModalProps {
  open: boolean;
  setOpen: (_open: boolean) => void;
}

const DashboardSuccessModal: React.FC<SuccessModalProps> = ({
  open,
  setOpen,
}) => {
  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-10" />
        {open && (
          <div className="absolute top-0 left-0 w-[50vw] h-full">
            <Confetti
              width={window.innerWidth / 2}
              height={window.innerHeight}
              numberOfPieces={100}
              gravity={0.1}
              confettiSource={{ x: 0, y: 0, w: 0, h: window.innerHeight }}
            />
          </div>
        )}

        {open && (
          <div className="absolute top-0 right-0 w-[50vw] h-full">
            <Confetti
              width={window.innerWidth / 2}
              height={window.innerHeight}
              numberOfPieces={100}
              gravity={0.1}
              confettiSource={{
                x: window.innerWidth / 2,
                y: 0,
                w: 0,
                h: window.innerHeight,
              }}
            />
          </div>
        )}

        {/* Modal Content */}
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 rounded-lg -translate-y-1/2 bg-white p-6 w-[500px] z-10">
          <Dialog.Title asChild>
            <VisuallyHidden>Success</VisuallyHidden>
          </Dialog.Title>

          <Image src={DashboardSuccessImage} alt="Success" />
          <div className="text-left px-2 py-4">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              🎉 Congratulations!
            </h2>

            {/* Description */}
            <p className="text-neutral-600 mt-2">
              Your company is now a valued partner, and you have access to
              exclusive benefits and opportunities. Here’s what you can do next:
            </p>

            {/* Checklist */}
            <ul className="mt-4 space-y-2">
              <li className="flex items-center gap-2 text-neutral-700">
                <Check className="text-black-100 w-5 h-5" />
                Access exclusive financial benefits.
              </li>
              <li className="flex items-center gap-2 text-neutral-700">
                <Check className="text-black-100 w-5 h-5" />
                List jobs, apprenticeships, and training opportunities.
              </li>
              <li className="flex items-center gap-2 text-neutral-700">
                <Check className="text-black-100 w-5 h-5" />
                Attract the right talent for your company.
              </li>
            </ul>

            <div className="mt-4 space-y-4">
              <Dialog.Close asChild>
                <Button
                  variant="default"
                  className="cursor-pointer w-full px-4 py-2"
                >
                  Let’s get started!
                </Button>
              </Dialog.Close>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default DashboardSuccessModal;
