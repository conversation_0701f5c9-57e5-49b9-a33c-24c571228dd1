/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PlusIcon, Upload, XIcon } from 'lucide-react';
import type { Course } from '@/types/courseType';
import { Label } from '@/components/ui/label';
import Image from 'next/image';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

const DELIVERY_MODES = ['In Person', 'Online', 'Hybrid'];
const DURATION_UNITS = ['Hours', 'Days', 'Weeks', 'Months', 'Years'];
const EXPERIENCE_LEVELS = ['Beginner', 'Intermediate', 'Advanced'];

interface Cities {
  id: string;
  value: string;
  label: string;
}

interface CourseDetailsTabProps {
  course: Course;
  updateCourse: <K extends keyof Course>(_field: K, _value: Course[K]) => void;
  onImageUpload: (_file: File) => Promise<string>;
}

export const CourseDetailsTab: React.FC<CourseDetailsTabProps> = ({
  course,
  updateCourse,
  onImageUpload,
}) => {
  const [additionalStartDates, setAdditionalStartDates] = useState<string[]>(
    []
  );
  const [showDateInput, setShowDateInput] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const allCities = useAdminValues({
    category: AdminValuesCategories?.cities?.category,
    subcategory: AdminValuesCategories?.cities.subcategories.Level_2,
  });

  const cities: Cities[] = allCities.data?.data?.data?.customValues || [];

  const validateField = (field: string, value: string) => {
    if (!value.trim()) {
      setErrors((prev) => ({
        ...prev,
        [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }

    if (field === 'courseLink') {
      const urlPattern = new RegExp(
        '^(https?:\\/\\/)?' +
          '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,}|' +
          '((\\d{1,3}\\.){3}\\d{1,3}))' +
          '(\\:\\d+)?(\\/[-a-zA-Z\\d%_.~+]*)*' +
          '(\\?[;&a-zA-Z\\d%_.~+=-]*)?' +
          '(\\#[-a-zA-Z\\d_]*)?$',
        'i'
      );
      if (!urlPattern.test(value)) {
        setErrors((prev) => ({
          ...prev,
          [field]: 'Please enter a valid URL',
        }));
        return false;
      }
    }

    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const handleImageChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.match(/image\/(png|jpeg|jpg)/)) {
      alert('Only PNG or JPG files are allowed');
      return;
    }
    if (file.size > 2.5 * 1024 * 1024) {
      alert('File size must be less than 2.5MB');
      return;
    }

    try {
      setIsUploading(true);
      const imageUrl = await onImageUpload(file);
      updateCourse('coverImage', imageUrl);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const validateNumberField = (field: string, value: string | number) => {
    if (value === '' || value === null || value === undefined) {
      setErrors((prev) => ({
        ...prev,
        [field]: `${field === 'courseFee' ? 'Course fee' : field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }
    if (typeof value === 'string' && isNaN(Number(value))) {
      setErrors((prev) => ({
        ...prev,
        [field]: `Please enter a valid number`,
      }));
      return false;
    }
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const handleDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    updateCourse('duration', value);
  };

  const handleCourseFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    // Allow only empty string or valid decimal numbers
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      updateCourse('courseFee', Number.parseFloat(value) || 0);
      validateNumberField('courseFee', value);
    }
  };

  // const handleCourseFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const value = e.target.value.replace(/[^0-9.]/g, '')
  //   updateCourse('courseFee', Number.parseFloat(value) || 0)
  // }

  const handleCourseFeeBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { value } = e.target;
    // On blur, if empty, keep it as empty (don't convert to 0)
    validateNumberField('courseFee', value === '' ? '0' : value);
  };

  const handleFlexibleChange = (checked: boolean) => {
    updateCourse('isFlexible', checked);
    if (checked) {
      updateCourse('startDate', '');
    }
  };

  const handleSelfPacedChange = (checked: boolean) => {
    updateCourse('isSelfPaced', checked);
    if (checked) {
      updateCourse('duration', '');
    }
  };

  const handleStartDateChange = (date: Date | undefined) => {
    if (date) {
      updateCourse('startDate', date.toISOString());
    } else {
      updateCourse('startDate', '');
    }
  };

  const handleAddAnotherStartDate = () => {
    setShowDateInput(true);
  };

  const addStartDate = (date: Date | undefined) => {
    if (date) {
      setAdditionalStartDates([...additionalStartDates, date.toISOString()]);
      setShowDateInput(false);
    }
  };

  const removeStartDate = (index: number) => {
    const newDates = [...additionalStartDates];
    newDates.splice(index, 1);
    setAdditionalStartDates(newDates);
  };

  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Course Image Upload Section (unchanged) */}
      <div className="mb-6">
        <Label
          htmlFor="coverImage"
          className="text-neutral-900 text-[16px] font-medium"
        >
          Course Image:
        </Label>
        <div className="flex flex-col gap-4 max-w-72 border rounded-md border-neutral-200 px-6 py-2">
          {course.coverImage ? (
            <div className="relative w-36 h-32">
              <Image
                src={
                  course.coverImage ||
                  '/placeholder.svg?height=200&width=300&query=course'
                }
                alt="Course Cover"
                fill
                className="object-cover rounded-none"
                unoptimized
              />
            </div>
          ) : (
            <p>Browse files to upload</p>
          )}

          <p className="text-[14px] text-neutral-500 relative bottom-3">
            {course.coverImage ? '' : 'PNG or JPG up to 2.5MB'}
          </p>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/png, image/jpeg"
            onChange={handleImageChange}
            className="hidden"
            disabled={isUploading}
          />
          <Button
            type="button"
            onClick={triggerFileInput}
            className="text-[16px] w-fit"
            variant={course.coverImage ? 'outline' : 'default'}
            disabled={isUploading}
          >
            {isUploading ? (
              'Uploading...'
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                {course.coverImage ? 'Choose Another Image' : 'Browse files'}
              </>
            )}
          </Button>
        </div>
      </div>

      <div>
        <label
          htmlFor="courseLink"
          className="block text-[16px] font-medium mb-1"
        >
          Course Link: <span className="text-destructive-500">*</span>
        </label>
        <Input
          id="courseLink"
          value={course.courseLink}
          onChange={(e) => {
            updateCourse('courseLink', e.target.value);
            validateField('courseLink', e.target.value);
          }}
          onBlur={() => validateField('courseLink', course.courseLink)}
          placeholder="Paste the url to the page where citizens can find more information about the course"
          className={cn(errors.courseLink && 'border-destructive-500')}
          required
        />
        {errors.courseLink && (
          <p className="text-destructive-500 text-[16px] mt-1">
            {errors.courseLink}
          </p>
        )}
      </div>

      {/* Delivery Type (unchanged) */}
      <div className="max-w-96">
        <label
          htmlFor="deliveryMode"
          className="block text-[16px] font-medium mb-1"
        >
          Delivery Type: <span className="text-destructive-500">*</span>
        </label>
        {errors.deliveryMode && (
          <p className="text-destructive-500 text-[16px] mb-1">
            {errors.deliveryMode}
          </p>
        )}
        <div className="flex space-x-2">
          {DELIVERY_MODES.map((mode) => (
            <Button
              key={mode}
              type="button"
              variant={course.deliveryMode === mode ? 'default' : 'outline'}
              className="flex-1 rounded-md"
              onClick={() => updateCourse('deliveryMode', mode as any)}
            >
              {mode}
            </Button>
          ))}
        </div>
      </div>

      {/* City Selector (unchanged) */}
      {(course.deliveryMode === 'In Person' ||
        course.deliveryMode === 'Hybrid') && (
        <div>
          <label htmlFor="city" className="block text-[16px] font-medium mb-1">
            City: <span className="text-destructive-500">*</span>
          </label>
          <Select
            value={course.city}
            onValueChange={(value) => updateCourse('city', value)}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Please select" />
            </SelectTrigger>
            <SelectContent>
              {cities.map((city: Cities) => (
                <SelectItem key={city.id} value={city?.value}>
                  {city?.label}
                </SelectItem>
              ))}
              {errors.city && (
                <p className="text-destructive-500 text-[16px] mt-1">
                  {errors.city}
                </p>
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Start Date Section with ShadCN Calendar */}
      <div className="max-w-96">
        <label
          htmlFor="startDate"
          className="block text-[16px] font-medium mb-1"
        >
          Start Date: <span className="text-destructive-500">*</span>
        </label>
        {errors.startDate && (
          <p className="text-destructive-500 text-[16px] mb-1">
            {errors.startDate}
          </p>
        )}
        <div className="flex space-x-2 mb-2">
          <Button
            type="button"
            variant={!course.isFlexible ? 'default' : 'outline'}
            className="flex-1 rounded-md"
            onClick={() => handleFlexibleChange(false)}
          >
            Session(s)
          </Button>
          <Button
            type="button"
            variant={course.isFlexible ? 'default' : 'outline'}
            className="flex-1 rounded-md"
            onClick={() => handleFlexibleChange(true)}
          >
            Flexible
          </Button>
        </div>

        {!course.isFlexible && (
          <>
            {/* Main date picker field */}
            <div className="mb-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-between text-left font-normal rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor]',
                      !course.startDate && 'text-muted-foreground'
                    )}
                  >
                    {course.startDate ? (
                      format(new Date(course.startDate), 'PPP')
                    ) : (
                      <span>DD/MM/YYYY</span>
                    )}
                    <CalendarIcon className="mr-2 h-4 w-4 !text-[--bodyTextColor]" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={
                      course.startDate ? new Date(course.startDate) : undefined
                    }
                    onSelect={handleStartDateChange}
                    initialFocus
                    fromDate={new Date()}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Display additional start dates if any */}
            {additionalStartDates.length > 0 && (
              <div className="mt-4 space-y-2">
                <p className="text-[18px] font-semibold leading-[28px] text-neutral-900">
                  Additional Start Dates:
                </p>
                <div className="space-y-2">
                  {additionalStartDates.map((date, index) => (
                    <div
                      key={date}
                      className="flex items-center justify-between p-2 border rounded-md"
                    >
                      <span>{formatDateForDisplay(date)}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeStartDate(index)}
                        className="h-6 w-6 p-0"
                      >
                        <XIcon className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Add another start date button */}
            {additionalStartDates.length < 10 && (
              <div className="mt-2">
                {showDateInput && (
                  <div className="mt-4">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-between text-left font-normal rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor]',
                            'text-muted-foreground'
                          )}
                        >
                          <span>DD/MM/YYYY</span>
                          <CalendarIcon className="mr-2 h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          onSelect={addStartDate}
                          initialFocus
                          fromDate={new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                )}
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddAnotherStartDate}
              className="flex items-center  h-10 py-2.5 px-5 justify-center mt-2"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              <p className="font-medium text-[16px] leading-[24px] !text-[--buttonColor]">
                {' '}
                Add another start date
              </p>
            </Button>
          </>
        )}
      </div>

      {/* Duration Section (unchanged) */}
      <div className="max-w-52">
        <label
          htmlFor="duration"
          className="block text-[16px] font-medium mb-1"
        >
          Duration: <span className="text-destructive-500">*</span>
        </label>

        {errors.duration && (
          <p className="text-destructive-500 text-[16px] mb-1">
            {errors.duration}
          </p>
        )}
        <div className="flex items-center space-x-2 mb-2">
          <Input
            type="text"
            value={course.duration}
            onChange={handleDurationChange}
            className="w-24"
            placeholder="0"
            disabled={course.isSelfPaced}
            required={!course.isSelfPaced}
          />
          <Select
            value={course.durationUnit}
            onValueChange={(value) =>
              updateCourse('durationUnit', value as any)
            }
            disabled={course.isSelfPaced}
          >
            <SelectTrigger
              className={cn(errors.durationUnit && 'border-destructive-500')}
            >
              <SelectValue placeholder="Unit" />
            </SelectTrigger>
            <SelectContent>
              {DURATION_UNITS.map((unit) => (
                <SelectItem key={unit} value={unit}>
                  {unit}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {errors.durationUnit && (
          <p className="text-destructive-500 text-[16px] mt-1">
            {errors.durationUnit}
          </p>
        )}

        <div className="flex items-center space-x-2">
          <Checkbox
            id="isSelfPaced"
            checked={course.isSelfPaced}
            onCheckedChange={(checked) =>
              handleSelfPacedChange(checked === true)
            }
          />
          <label
            htmlFor="isSelfPaced"
            className="text-[16px] font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Self-paced
          </label>
        </div>
      </div>

      <div>
        <label
          htmlFor="experienceLevel"
          className="block text-[16px] font-medium mb-1"
        >
          Experience Level: <span className="text-destructive-500">*</span>
        </label>
        <Select
          value={course.experienceLevel}
          onValueChange={(value) =>
            updateCourse('experienceLevel', value as any)
          }
          required
        >
          <SelectTrigger
            className={cn(errors.experienceLevel && 'border-destructive-500')}
          >
            <SelectValue placeholder="Please select" />
          </SelectTrigger>
          <SelectContent>
            {EXPERIENCE_LEVELS.map((level) => (
              <SelectItem key={level} value={level}>
                {level}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.experienceLevel && (
          <p className="text-destructive-500 text-[16px] mt-1">
            {errors.experienceLevel}
          </p>
        )}
      </div>

      {/* Course Fee (unchanged) */}
      <div className="">
        <label
          htmlFor="courseFee"
          className="block text-[16px] font-medium mb-1"
        >
          Course Fee: <span className="text-destructive-500">*</span>
        </label>
        <div className="relative rounded-lg">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none bg-neutral-50 border border-neutral-200 px-2  rounded-l-md">
            <span className="text-neutral-500">$</span>
          </div>
          <Input
            id="courseFee"
            type="text"
            value={
              typeof course.courseFee === 'string'
                ? ''
                : course.courseFee?.toString()
            }
            onChange={handleCourseFeeChange}
            onBlur={handleCourseFeeBlur}
            className={cn(
              'pl-10',
              errors.courseFee && 'border-destructive-500'
            )}
            placeholder="Enter the cost of the course or 0 if free"
            required
          />
        </div>
        {errors.courseFee && (
          <p className="text-destructive-500 text-[16px] mt-1">
            {errors.courseFee}
          </p>
        )}
      </div>
    </div>
  );
};
