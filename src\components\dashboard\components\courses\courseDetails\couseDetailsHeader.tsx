/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import type { Course } from '@/types/courseType';
import { updateCourse } from '@/zustand/services/courseServices';
import Image from 'next/image';
import EditICon from '@/assets/images/EditIcon.svg';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

interface CourseDetailsHeaderProps {
  course: Course;
  onCourseUpdate?: (_updatedCourse: Course) => void;
}

interface CourseValidation {
  isValid: boolean;
  missingFields: string[];
}

export default function CourseDetailsHeader({
  course,
  onCourseUpdate,
}: CourseDetailsHeaderProps) {
  const router = useRouter();
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [courseValidation, setCourseValidation] = useState<CourseValidation>({
    isValid: false,
    missingFields: [],
  });
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const userId = useAuthStore((state) => state.user?.id);

  useEffect(() => {
    validateCourse(course);
  }, [course]);

  const validateCourse = (courseToValidate: Course) => {
    const requiredFields: (keyof Course)[] = [
      'name',
      'description',
      'coverImage',
      'mainTopic',
      'learningObjective',
      'courseFee',
      'programOutlines',
      'coverImage',
      'courseLink',
      'experienceLevel',
      'courseFee',
    ];

    const missingFields = requiredFields.filter((field) => {
      const value = courseToValidate[field];
      if (Array.isArray(value)) return value.length === 0;
      if (typeof value === 'string') return value.trim() === '';
      return value === null || value === undefined;
    });

    setCourseValidation({
      isValid: missingFields.length === 0,
      missingFields,
    });
  };

  const getStatusStyles = () => {
    switch (course.status) {
      case 'Active':
        return {
          bgColor: 'bg-transparent',
          textColor: 'text-primary-500',
          borderColor: 'border-primary-500',
          dotColor: 'bg-primary-500',
        };
      case 'Pending':
        return {
          bgColor: 'bg-transparent',
          textColor: 'text-warning-800',
          borderColor: 'border-warning-900',
          dotColor: 'bg-warning-900',
        };
      case 'Draft':
        return {
          bgColor: 'bg-transparent',
          textColor: 'text-neutral-500',
          borderColor: 'border-neutral-500',
          dotColor: 'bg-neutral-600',
        };
      case 'Inactive':
        return {
          bgColor: 'bg-transparent',
          textColor: 'text-destructive-500',
          borderColor: 'border-destructive-500',
          dotColor: 'bg-destructive-600',
        };
      default:
        return {
          bgColor: 'bg-neutral-50',
          textColor: 'text-neutral-700',
          borderColor: 'border-neutral-200',
          dotColor: 'bg-neutral-500',
        };
    }
  };

  const statusStyles = getStatusStyles();

  const handleEdit = () => {
    router.push(`/dashboard/courses/${course.id}/edit`);
  };

  const handleStatusChange = async () => {
    try {
      setIsUpdating(true);

      if (!course?.id || !userId) {
        throw new Error('Course ID and user ID are required for editing');
      }

      const newStatus =
        course.status === 'Draft'
          ? 'Active'
          : course.status === 'Pending'
            ? 'Active'
            : course.status === 'Active'
              ? 'Inactive'
              : 'Active';

      const updatedCourseData: Partial<Course> = {
        ...course,
        status: newStatus as 'Active' | 'Pending' | 'Draft' | 'Inactive',
        lastModifiedById: userId,
      };

      // eslint-disable-next-line no-unused-vars
      const { id, ...dataToSend } = updatedCourseData;

      const updatedCourse = await updateCourse(course.id, dataToSend, userId);

      if (onCourseUpdate && updatedCourse) {
        onCourseUpdate(updatedCourse);
      }

      router.push(`/dashboard/courses`);
    } catch (error) {
      console.error('Error saving course:', error);
    } finally {
      setIsUpdating(false);
      setIsStatusDialogOpen(false);
    }
  };

  const getStatusActionButton = () => {
    switch (course.status) {
      case 'Active':
        return (
          <Button
            className="text-destructive-500 border border-destructive-500 bg-white hover:bg-destructive-50 hover:text-destructive-500"
            variant="outline"
            onClick={() => setIsStatusDialogOpen(true)}
          >
            Deactivate
          </Button>
        );
      case 'Pending':
        return (
          <Button
            variant="outline"
            disabled
            className="opacity-70 cursor-not-allowed text-warning-900 bg-transparent border border-warning-900"
          >
            Pending Approval
          </Button>
        );
      case 'Draft':
        return (
          <Button
            variant="default"
            onClick={() => {
              if (courseValidation.isValid) {
                setIsStatusDialogOpen(true);
              } else {
                setShowValidationDialog(true);
              }
            }}
          >
            Submit
          </Button>
        );
      case 'Inactive':
        return (
          <Button variant="outline" onClick={() => setIsStatusDialogOpen(true)}>
            Activate
          </Button>
        );
      default:
        return null;
    }
  };

  const getDialogTitle = () => {
    switch (course.status) {
      case 'Active':
        return 'Deactivate Course';
      case 'Draft':
        return 'Submit Course';
      case 'Inactive':
        return 'Activate Course';
      default:
        return 'Update Course Status';
    }
  };

  const getDialogDescription = () => {
    switch (course.status) {
      case 'Active':
        return 'Are you sure you want to deactivate this course? It will no longer be visible to learners.';
      case 'Draft':
        return 'Are you sure you want to submit this course for approval? Once submitted, it will be reviewed by administrators.';
      case 'Inactive':
        return 'Are you sure you want to activate this course? It will be visible to learners.';
      default:
        return 'Are you sure you want to update the status of this course?';
    }
  };

  const getDialogActionText = () => {
    switch (course.status) {
      case 'Active':
        return 'Deactivate';
      case 'Draft':
        return 'Submit';
      case 'Inactive':
        return 'Activate';
      default:
        return 'Confirm';
    }
  };

  return (
    <>
      <div className="flex justify-between items-start relative z-30">
        <h1 className="flex gap-2 items-center text-[28px] text-neutral-900 font-semibold leading-[30px]">
          {course.name}
          <div
            className={`flex gap-2 items-center border ${statusStyles.borderColor} ${statusStyles.bgColor} rounded-full py-1.5 pl-3 pr-2`}
          >
            <div className={`w-3 h-3 ${statusStyles.dotColor} rounded-full`} />
            <span className={`text-sm ${statusStyles.textColor}`}>
              {course.status}
            </span>
          </div>
        </h1>
        <div className="flex space-x-3">
          {getStatusActionButton()}
          <Button className="px-4 " onClick={handleEdit}>
            <Image src={EditICon} alt="Edit" width={20} height={20} />
            Edit
          </Button>
        </div>

        {/* Status Change Confirmation Dialog */}
        <AlertDialog
          open={isStatusDialogOpen}
          onOpenChange={setIsStatusDialogOpen}
        >
          <AlertDialogContent className="z-50">
            <AlertDialogHeader>
              <AlertDialogTitle>{getDialogTitle()}</AlertDialogTitle>
              <AlertDialogDescription>
                {getDialogDescription()}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="rounded-full" disabled={isUpdating}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleStatusChange}
                disabled={isUpdating}
                className={
                  course.status === 'Active'
                    ? 'bg-destructive-500 hover:bg-destructive-500 rounded-full'
                    : 'rounded-full'
                }
              >
                {isUpdating ? 'Processing...' : getDialogActionText()}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Incomplete Course Warning Dialog */}
        <Dialog
          open={showValidationDialog}
          onOpenChange={setShowValidationDialog}
        >
          <DialogContent className="z-50 w-[500px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-semibold text-destructive-500">
                Incomplete Course
              </DialogTitle>
              <DialogDescription className="text-[16px] font-medium text-neutral-500">
                Please complete all required fields before you can submit this
                course
              </DialogDescription>
            </DialogHeader>
            <div className="mt-6 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowValidationDialog(false)}
                className="py-[14px] px-[28px] border border-destructive-500 !text-destructive-500 hover:bg-destructive-50"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setShowValidationDialog(false);
                  router.push(`/dashboard/courses/${course.id}/edit`);
                }}
                className="py-[14px] px-[28px]"
              >
                Edit Course
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
