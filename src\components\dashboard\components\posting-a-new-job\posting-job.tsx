'use client';

import AssistantTooltip from '@/components/dashboard/common/AssistantTooltip';
import { Card, CardContent } from '@/components/ui/card';
import type React from 'react';
import { ChevronLeft, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useGenerateJobMutation } from '@/mutations';
import useAiJobStore from '@/zustand/store/aiJobStore';
import type { IJob } from '@/type';
import { SparkleIcons } from '@/assets';
import Image from 'next/image';

export default function PostingJob() {
  const [content, setContent] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const maxWords = 1200;

  useEffect(() => {
    const words = content
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    setWordCount(words.length);
  }, [content]);

  const { setJob } = useAiJobStore((state) => state);
  const handleSuccess = (data: { data: IJob }) => {
    setJob(data?.data);
    setContent('');
    handleManualDetails();
  };
  const { generateJob, isLoading, isError, error } =
    useGenerateJobMutation(handleSuccess);

  const handleGenerate = async () => {
    if (!content.trim()) return;
    generateJob(content);
  };

  const router = useRouter();

  const handleManualDetails = () => {
    router.push('/dashboard/jobs-and-training/posting-a-new-listing/detail');
  };

  return (
    <div className="p-4 mx-auto space-y-4">
      <div className="mb-28">
        <Button
          onClick={() => router.back()}
          variant="outline"
          className="py-3.5 px-7  mb-10 h-[48px] text-[16px] font-medium"
        >
          <ChevronLeft className="w-20 h-20" />
          Back
        </Button>
        <h1 className="text-[28px] font-semibold leading-[36px] text-neutral-900">
          Add a New Job Listing
        </h1>
      </div>
      <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
        <Card className="border-none shadow-none">
          <CardContent className="space-y-6">
            <div className="space-y-4 pt-6">
              <p className="text-[18px] text-muted-foreground text-left">
                Please provide the job title, include key responsibilities,
                required qualifications, work mode (on-site, remote, or hybrid),
                and compensation. I&apos;ll generate a compelling job listing
                for you.
              </p>
            </div>

            <div className="space-y-4">
              <Textarea
                placeholder={`Example: 
  Job Title: Senior Backend Developer
  
  Responsibilities:
  - Develop backend systems
  - Optimize APIs
  - Lead data integration projects
  
  Qualifications:
  - 5+ years in .NET and Azure
  - Bachelor's degree in Computer Science

  Work Mode: 
  - Hybrid
  
  Compensation:
  - Salary range: 12,000 - 15,000 AED`}
                className="min-h-[150px] resize-none"
                value={content}
                onChange={(e) => setContent(e.target.value)}
              />

              <div className="text-[16px] text-muted-foreground text-right">
                {wordCount}/{maxWords} words
              </div>
            </div>
            {isError && (
              <p className="text-[16px] text-destructive-500">
                {(error as { response?: { data?: { message?: string } } })
                  ?.response?.data?.message ||
                  error?.message ||
                  'An unexpected error occurred. Please try again.'}
              </p>
            )}
            <div className="space-y-4">
              <Button
                className="w-full px-[14px] py-[28px] h-[48px] text-[18px] font-medium leading-[27px] cursor-pointer"
                onClick={handleGenerate}
                disabled={!content.trim() || isLoading || wordCount > maxWords}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Image src={SparkleIcons} alt="" />
                Generate Listing
              </Button>

              <div className="relative py-2">
                <div className="absolute inset-0 flex items-center">
                  {/* <Separator /> */}
                </div>
                <div className="relative flex justify-center text-[16px] uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    Or
                  </span>
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full px-[14px] py-[28px] h-[48px] text-[18px] font-medium leading-[27px] cursor-pointer"
                onClick={handleManualDetails}
              >
                Manually Enter Details
              </Button>
            </div>
          </CardContent>
        </Card>
      </AssistantTooltip>
    </div>
  );
}
