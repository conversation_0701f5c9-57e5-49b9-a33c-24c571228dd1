version: '3.8'

services:
  career-navigator-ui:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: career-navigator-pro-partner-ui
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-https://career-navigator-pro-api-dot-careernavigator-430608.el.r.appspot.com/api/}
      - NEXT_PUBLIC_PARTNER_BASE_URL=${NEXT_PUBLIC_PARTNER_BASE_URL:-https://career-navigator-pro-ui-dot-careernavigator-430608.el.r.appspot.com}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "/app/scripts/healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - career-navigator-network

networks:
  career-navigator-network:
    driver: bridge
