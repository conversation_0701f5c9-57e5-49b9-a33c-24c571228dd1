'use client';

import type React from 'react';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PlusIcon, Upload, XIcon } from 'lucide-react';
import {
  DELIVERY_MODES,
  DURATION_UNITS,
  EXPERIENCE_LEVELS,
} from '@/types/courseType';
import { Label } from '@/components/ui/label';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import { cn } from '@/lib/utils';

import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
// import { format } from 'date-fns'
import { CalendarIcon } from 'lucide-react';
import { formatDateDDMonthYYYY } from '@/utils';

interface Cities {
  id: string;
  value: string;
  label: string;
}

export const CourseDetailsStep: React.FC = () => {
  const { course, updateCourse, startDates, addStartDate, removeStartDate } =
    useCourseFormStore();
  const [imageUrl, setImageUrl] = useState<string>(course.coverImage || '');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showNotification } = useNotificationStore.getState();
  const [showAdditionalDateInput, setShowAdditionalDateInput] = useState(false);
  const [newStartDate, setNewStartDate] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validation functions
  const validateField = (field: string, value: string) => {
    if (!value.trim()) {
      setErrors((prev) => ({
        ...prev,
        [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }

    if (field === 'courseLink') {
      const urlPattern = new RegExp(
        '^(https?:\\/\\/)?' +
          '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,}|' +
          '((\\d{1,3}\\.){3}\\d{1,3}))' +
          '(\\:\\d+)?(\\/[-a-zA-Z\\d%_.~+]*)*' +
          '(\\?[;&a-zA-Z\\d%_.~+=-]*)?' +
          '(\\#[-a-zA-Z\\d_]*)?$',
        'i'
      );
      if (!urlPattern.test(value)) {
        setErrors((prev) => ({
          ...prev,
          [field]: 'Please enter a valid URL',
        }));
        return false;
      }
    }

    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const validateSelectField = (field: string, value: string) => {
    if (!value) {
      setErrors((prev) => ({
        ...prev,
        [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const validateNumberField = (field: string, value: string | number) => {
    if (value === '' || value === null || value === undefined) {
      setErrors((prev) => ({
        ...prev,
        [field]: `${field === 'courseFee' ? 'Course fee' : field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }
    if (typeof value === 'string' && isNaN(Number(value))) {
      setErrors((prev) => ({
        ...prev,
        [field]: `Please enter a valid number`,
      }));
      return false;
    }
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const handleDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    updateCourse({ duration: value });
    if (!course.isSelfPaced) {
      validateNumberField('duration', value);
    }
  };

  const handleCourseFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    // Allow only empty string or valid decimal numbers
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      const numericValue = value === '' ? undefined : parseFloat(value);
      updateCourse({ courseFee: numericValue });
      validateNumberField('courseFee', value);
    }
  };

  const handleCourseFeeBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { value } = e.target;
    // On blur, if empty, keep it as empty (don't convert to 0)
    validateNumberField('courseFee', value === '' ? '0' : value);
  };

  const handleFlexibleChange = (checked: boolean) => {
    updateCourse({
      isFlexible: checked,
      startDate: checked ? '' : course.startDate,
    });
    if (!checked) {
      validateField('startDate', course.startDate);
    } else {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors['startDate'];
        return newErrors;
      });
    }
  };

  const handleSelfPacedChange = (checked: boolean) => {
    updateCourse({
      isSelfPaced: checked,
      duration: checked ? '' : course.duration,
    });
    if (!checked) {
      validateNumberField('duration', course.duration);
    } else {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors['duration'];
        return newErrors;
      });
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2.5 * 1024 * 1024) {
        showNotification('File size must be less than 2.5MB', 'error');
        setErrors((prev) => ({
          ...prev,
          coverImage: 'File size must be less than 2.5MB',
        }));
        return;
      }

      if (!file.type.match(/image\/(png|jpeg|jpg)/)) {
        showNotification('Only PNG or JPG files are allowed', 'error');
        setErrors((prev) => ({
          ...prev,
          coverImage: 'Only PNG or JPG files are allowed',
        }));
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setImageUrl(base64String);
        updateCourse({ coverImage: base64String });
        // Clear any existing error
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.coverImage;
          return newErrors;
        });
      };
      reader.readAsDataURL(file);
    }
  };

  useEffect(() => {
    if (course.coverImage) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.coverImage;
        return newErrors;
      });
    }
  }, [course.coverImage]);

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const allCities = useAdminValues({
    category: AdminValuesCategories?.cities?.category,
    subcategory: AdminValuesCategories?.cities.subcategories.Level_2,
  });

  const cities: Cities[] = allCities.data?.data?.data?.customValues || [];

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">
        Step 2: Enrolment & Course Details
      </h2>

      <div className="space-y-6">
        <div className="mb-6">
          <Label
            htmlFor="coverImage"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Course Image:
            {/* <span className="text-destructive-500">*</span> */}
          </Label>
          <div className="flex flex-col gap-4 max-w-72 border rounded-md border-neutral-200 px-6 py-2 mt-2">
            {imageUrl ? (
              <div className="relative w-36 h-32">
                <Image
                  src={imageUrl || '/placeholder.svg'}
                  alt="Course Cover"
                  fill
                  className="object-cover rounded-none"
                />
              </div>
            ) : (
              <p> Browse files to upload</p>
            )}

            <p className="text-[14px] text-neutral-500 relative bottom-3">
              {imageUrl ? '' : 'PNG or JPG up to 2.5MB'}
            </p>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/png, image/jpeg"
              onChange={handleImageUpload}
              className="hidden"
            />
            <Button
              type="button"
              onClick={triggerFileInput}
              className="text-[16px] w-fit rounded-[--buttonStyle] mt-2"
              variant={imageUrl ? 'outline' : 'default'}
            >
              <Upload />
              {imageUrl ? 'Change Image' : 'Browse files'}
            </Button>
          </div>
          {errors.coverImage && (
            <p className="text-destructive-500 text-[16px] mt-1">
              {errors.coverImage}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="courseLink"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Course Link:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          <Input
            id="courseLink"
            value={course.courseLink}
            onChange={(e) => {
              updateCourse({ courseLink: e.target.value });
              validateField('courseLink', e.target.value);
            }}
            onBlur={() => validateField('courseLink', course.courseLink)}
            placeholder="Paste the url to the page where citizens can find more information about the course"
            className={cn(
              errors.courseLink && 'border-destructive-500',
              'mt-2'
            )}
            required
          />
          {errors.courseLink && (
            <p className="text-destructive-500 text-[16px] mt-1">
              {errors.courseLink}
            </p>
          )}
        </div>

        <div className="max-w-80">
          <label className="text-[18px] font-semibold leading-[28px] text-neutral-900">
            Delivery:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          {errors.deliveryMode && (
            <p className="text-destructive-500 text-[16px] mb-1 mt-2">
              {errors.deliveryMode}
            </p>
          )}
          <div className="flex space-x-2 mt-3">
            {DELIVERY_MODES.map((mode) => (
              <Button
                key={mode}
                type="button"
                variant={course.deliveryMode === mode ? 'default' : 'outline'}
                className={cn(
                  'flex-1 rounded-md text-[16px]',
                  errors.deliveryMode && 'border-destructive-500'
                )}
                onClick={() => {
                  updateCourse({ deliveryMode: mode });
                  validateSelectField('deliveryMode', mode);
                }}
              >
                {mode}
              </Button>
            ))}
          </div>
        </div>

        {(course.deliveryMode === 'In Person' ||
          course.deliveryMode === 'Hybrid') && (
          <div>
            <label
              htmlFor="city"
              className="text-[18px] font-semibold leading-[28px] text-neutral-900"
            >
              City:
              {/* <span className="text-destructive-500">*</span> */}
            </label>
            <Select
              value={course.city}
              onValueChange={(value) => {
                updateCourse({ city: value });
                validateSelectField('city', value);
              }}
              onOpenChange={(open) =>
                !open && validateSelectField('city', course.city)
              }
            >
              <SelectTrigger
                className={cn(errors.city && 'border-destructive-500', 'mt-2')}
              >
                <SelectValue placeholder="Please select" />
              </SelectTrigger>
              <SelectContent>
                {cities.map((city: Cities) => (
                  <SelectItem key={city.id} value={city?.value}>
                    {city?.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.city && (
              <p className="text-destructive-500 text-[16px] mt-1">
                {errors.city}
              </p>
            )}
          </div>
        )}

        <div className="max-w-80">
          <label className="text-[18px] font-semibold leading-[28px] text-neutral-900">
            Start Date:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          {errors.startDate && (
            <p className="text-destructive-500 text-[16px] mb-1 mt-2">
              {errors.startDate}
            </p>
          )}
          <div className="flex space-x-2 mb-2 mt-2">
            <Button
              type="button"
              variant={!course.isFlexible ? 'default' : 'outline'}
              className="flex-1 rounded-md text-[16px]"
              onClick={() => handleFlexibleChange(false)}
            >
              Session(s)
            </Button>
            <Button
              type="button"
              variant={course.isFlexible ? 'default' : 'outline'}
              className="flex-1 rounded-md text-[16px]"
              onClick={() => handleFlexibleChange(true)}
            >
              Flexible
            </Button>
          </div>

          {!course.isFlexible && (
            <>
              {/* Main date input field */}
              <div className="relative mt-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={'outline'}
                      className={cn(
                        'w-full pl-3 text-left font-normal text-[16px] rounded-md border border-neutral-200 hover:bg-transparent',
                        !course.startDate && 'text-muted-foreground',
                        errors.startDate && 'border-destructive-500'
                      )}
                    >
                      {course.startDate ? (
                        formatDateDDMonthYYYY(course.startDate)
                      ) : (
                        <span className="!text-[--bodyTextColor]">
                          DD/MM/YYYY
                        </span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50 !text-[--bodyTextColor]" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <div className="flex flex-col">
                      <Calendar
                        mode="single"
                        selected={
                          course.startDate
                            ? new Date(course.startDate)
                            : undefined
                        }
                        onSelect={(date) => {
                          if (date) {
                            const localDateString =
                              date.toLocaleDateString('en-CA'); // 'YYYY-MM-DD'
                            updateCourse({ startDate: localDateString });
                            validateField('startDate', localDateString);
                          }
                        }}
                        disabled={(date) =>
                          date < new Date() || date < new Date('1900-01-01')
                        }
                        initialFocus
                      />

                      <Button
                        variant="ghost"
                        onClick={() => {
                          updateCourse({ startDate: '' });
                          setErrors((prev) => {
                            const newErrors = { ...prev };
                            delete newErrors['startDate'];
                            return newErrors;
                          });
                        }}
                        className="text-destructive-500 hover:text-destructive-600  mt-2 text-center justify-center items-center"
                      >
                        Clear
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Additional dates section */}
              {startDates.length > 0 && (
                <div className="mt-4 space-y-2">
                  <p className="text-[18px] font-semibold leading-[28px] text-neutral-900">
                    Additional Start Dates:
                  </p>
                  <div className="space-y-2">
                    {startDates.map((date, index) => (
                      <div
                        key={date}
                        className="flex items-center justify-between p-2 border rounded-md"
                      >
                        <span>{formatDateDDMonthYYYY(date)}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeStartDate(index)}
                          className="h-6 w-6 p-0 hover:bg-[--buttonColorLight]"
                        >
                          <XIcon className="h-4 w-4" />
                          <span className="sr-only">Remove</span>
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Add another start date button */}
              {startDates.length < 10 && (
                <div className="mt-2">
                  {!showAdditionalDateInput ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAdditionalDateInput(true)}
                      className="flex items-center text-[16px] rounded-[--buttonStyle] mt-3 py-[10px] px-[20px] h-[40px]"
                    >
                      <PlusIcon className="h-5 w-5" />
                      Add another start date
                    </Button>
                  ) : (
                    <div className="flex space-x-2 items-center mt-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-[240px] pl-3 text-left font-normal hover:bg-transparent rounded-md border border-secondary-500',
                              !newStartDate && 'text-muted-foreground'
                            )}
                          >
                            {newStartDate ? (
                              formatDateDDMonthYYYY(newStartDate)
                            ) : (
                              <span>DD/MM/YYYY</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50 !text-[--bodyTextColor]" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <div className="flex flex-col">
                            <Calendar
                              mode="single"
                              selected={
                                newStartDate
                                  ? new Date(newStartDate)
                                  : undefined
                              }
                              onSelect={(date) => {
                                if (date) {
                                  const localDateStringDate =
                                    date.toLocaleDateString('en-CA');
                                  setNewStartDate(localDateStringDate);
                                  addStartDate(localDateStringDate);
                                  setNewStartDate('');
                                  setShowAdditionalDateInput(false);
                                }
                              }}
                              disabled={(date) =>
                                date < new Date() ||
                                date < new Date('1900-01-01')
                              }
                              initialFocus
                            />
                            <Button
                              variant="ghost"
                              onClick={() => {
                                setNewStartDate('');
                                setShowAdditionalDateInput(false);
                              }}
                              className="text-destructive-500 hover:text-destructive-600"
                            >
                              Clear
                            </Button>
                          </div>
                        </PopoverContent>
                      </Popover>
                      <Button
                        variant="ghost"
                        onClick={() => setShowAdditionalDateInput(false)}
                      >
                        <XIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        <div className="max-w-52">
          <label className="text-[18px] font-semibold leading-[28px] text-neutral-900">
            Duration:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          {errors.duration && (
            <p className="text-destructive-500 text-[16px] mb-1 mt-2">
              {errors.duration}
            </p>
          )}
          <div className="flex items-center space-x-2 mb-2 mt-2">
            <Input
              type="text"
              value={course.duration}
              onChange={handleDurationChange}
              onBlur={() => {
                if (!course.isSelfPaced) {
                  validateNumberField('duration', course.duration);
                }
              }}
              className={cn(
                'w-24',
                errors.duration && 'border-destructive-500'
              )}
              placeholder="0"
              disabled={course.isSelfPaced}
            />
            <Select
              value={course.durationUnit}
              onValueChange={(value) => {
                updateCourse({ durationUnit: value });
                validateSelectField('durationUnit', value);
              }}
              onOpenChange={(open) =>
                !open &&
                validateSelectField('durationUnit', course.durationUnit)
              }
              disabled={course.isSelfPaced}
            >
              <SelectTrigger
                className={cn(errors.durationUnit && 'border-destructive-500')}
              >
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                {DURATION_UNITS.map((unit) => (
                  <SelectItem key={unit} value={unit}>
                    {unit}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {errors.durationUnit && (
            <p className="text-destructive-500 text-[16px] mt-1">
              {errors.durationUnit}
            </p>
          )}

          <div className="flex items-center space-x-2 mt-2">
            <Checkbox
              id="isSelfPaced"
              checked={course.isSelfPaced}
              onCheckedChange={(checked) =>
                handleSelfPacedChange(checked === true)
              }
            />
            <label
              htmlFor="isSelfPaced"
              className="text-[16px] font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Self-paced
            </label>
          </div>
        </div>

        <div>
          <label
            htmlFor="experienceLevel"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Experience Level:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          <Select
            value={course.experienceLevel}
            onValueChange={(value) => {
              updateCourse({ experienceLevel: value });
              validateSelectField('experienceLevel', value);
            }}
            onOpenChange={(open) =>
              !open &&
              validateSelectField('experienceLevel', course.experienceLevel)
            }
          >
            <SelectTrigger
              className={cn(
                errors.experienceLevel && 'border-destructive-500',
                'mt-2'
              )}
            >
              <SelectValue placeholder="Please select" />
            </SelectTrigger>
            <SelectContent>
              {EXPERIENCE_LEVELS.map((level) => (
                <SelectItem key={level} value={level}>
                  {level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.experienceLevel && (
            <p className="text-destructive-500 text-[16px] mt-1">
              {errors.experienceLevel}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="courseFee"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Course Fee:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          <div className="relative rounded-lg mt-2">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none bg-neutral-50 border border-neutral-200 px-2 rounded-l-md">
              <span className="text-neutral-500">$</span>
            </div>
            <Input
              id="courseFee"
              type="text"
              value={
                typeof course.courseFee === 'string'
                  ? ''
                  : course.courseFee?.toString()
              }
              onChange={handleCourseFeeChange}
              onBlur={handleCourseFeeBlur}
              className={cn(
                'pl-10',
                errors.courseFee && 'border-destructive-500'
              )}
              placeholder="Enter the cost of the course or 0 if free"
              required
            />
          </div>
          {errors.courseFee && (
            <p className="text-destructive-500 text-[16px] mt-1">
              {errors.courseFee}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
