'use client';

import type { Route } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
// import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import CareerNavigatorLogo from '@/assets/CareerNavigatorLogo.svg';
import { citizenBaseUrl } from '@/constants/globalBaseUrl';
import { GovernmentEmblem } from '../../governmentEmblem';
import useSettingsStore from '@/zustand/store/settingsStore';
import { validationMessages } from '@/constants/validationMessages';
import InputField from '@/components/common/InputField';

const { emailInvalid, emailRequired, passwordRequired } = validationMessages;

// Define validation schema using Yup
const validationSchema = Yup.object().shape({
  email: Yup.string().email(emailInvalid).required(emailRequired),
  password: Yup.string().required(passwordRequired),
  rememberMe: Yup.boolean(),
});

function LoginFormTemplate() {
  const { push } = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const { login, isloading } = useAuthStore();
  const [showPassword, setShowPassword] = useState(false);

  // Redirect helper
  const redirectToPath = (path: Route) => {
    push(path);
  };

  return (
    <>
      <div className="flex flex-col gap-8">
        {/* Header */}
        <div className="flex flex-col items-start font-semibold text-neutral-900 dark:text-neutral-100">
          <Image
            alt="Logo"
            width={300}
            height={40}
            className="w-auto h-[40px] cursor-pointer"
            src={appearanceSettings?.platformLogoDark || CareerNavigatorLogo}
            onClick={() => redirectToPath('/')}
          />
          <h2 className="text-neutral-900 text-[28px] font-semibold leading-[36px] mt-8">
            Login as a Partner Company
          </h2>
        </div>

        <div className="w-full mx-auto">
          <Formik
            initialValues={{ email: '', password: '', rememberMe: false }}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                await login({
                  email: values.email,
                  password: values.password,
                  role: 'partner',
                });
                redirectToPath('/dashboard');
              } catch (error) {
                console.error('login failed:', error);
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ handleChange, handleBlur, values, isSubmitting, errors }) => (
              <Form className="grid grid-cols-1">
                <div className="mb-3 space-y-1">
                  <InputField
                    label="Email Address"
                    name={'email'}
                    type={'email'}
                    error={errors.email}
                    placeholder="Enter your work email"
                    value={values.email}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                {/* Password Field */}
                <div className="mb-3 space-y-1 relative">
                  <div className="relative">
                    <button
                      type="button"
                      className="absolute right-3 top-[48px] transform -translate-y-1/2 text-neutral-500 hover:text-neutral-700"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-primary-500" />
                      ) : (
                        <Eye className="h-5 w-5 text-primary-500" />
                      )}
                    </button>
                    <InputField
                      label="Password"
                      name={'password'}
                      type={showPassword ? 'text' : 'password'}
                      placeholder="*******************"
                      error={errors.password}
                      value={values.password}
                      handleChange={handleChange}
                      handleBlur={handleBlur}
                    />
                  </div>
                </div>

                <p className="mt-1 text-primary-500 text-[16px] font-semibold leading-[16px] cursor-pointer">
                  Forgot Password?
                </p>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isloading || isSubmitting}
                  variant="default"
                  className="justify-center mt-6 mb-8 flex items-center gap-2 text-[16px] py-[14px] px-[28px] h-[48px]"
                >
                  {isloading || isSubmitting ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    'Log in as a Partner Company'
                  )}
                </Button>

                <Link
                  href={'/signup' as Route}
                  className="text-[18px] font-normal text-center text-neutral-500"
                >
                  Don&apos;t have an account?{' '}
                  <span className="font-semibold ml-1 text-primary-500 hover:underline">
                    Sign Up
                  </span>
                </Link>
              </Form>
            )}
          </Formik>
        </div>

        <div className="flex items-center w-full gap-10 mt-4">
          <div className="flex-1 h-px bg-neutral-200" />
          <p className="text-[16px] text-neutral-500 font-normal leading-[27px]">
            Or
          </p>
          <div className="flex-1 h-px bg-neutral-200" />
        </div>

        <Button
          variant="outline"
          onClick={() => redirectToPath(citizenBaseUrl)}
          className="justify-center flex items-center gap-2 text-[18px] py-[14px] px-[28px] h-[48px] font-medium"
        >
          Continue as a Citizen
        </Button>
      </div>

      <GovernmentEmblem />
    </>
  );
}

export default LoginFormTemplate;
