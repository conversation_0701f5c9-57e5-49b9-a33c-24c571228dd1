'use client';

import React from 'react';
// import Image from 'next/image'
import { navConfig } from '@/constants/navConfig';
import Logo from '@/constants/logo';
// import { searchIcon } from '@/assets'
import NavigationItem from '@/utils/navigationItem';
import { Button } from '../ui/button';
import SearchIcon from '../icons/search-icon';
import Link from 'next/link';

function Header() {
  return (
    <div
      className={`bg-[--topBarBgColor] items-center justify-center relative z-10 px-20`}
    >
      <div className={`h-20  relative flex justify-between`}>
        <div className="flex justify-start gap-10 items-center ">
          <Logo
            logoColor="black"
            className="self-center w-68"
            defaultRoute={''}
          />

          <ul className="flex mx-auto justify-center text-center md:flex-wrap lg:space-x-1 relative text-[16px] font-bold gap-8">
            {navConfig?.map((item) => (
              <div key={item.id} className="relative group flex items-center">
                <NavigationItem menuItem={item} />
              </div>
            ))}
          </ul>
        </div>
        <div className="flex items-center space-x-6">
          <SearchIcon strokeColor={'var(--topBarBtnColor)'} />
          <Link href={'/login'}>
            <Button className="!self-center !px-4 !text-[--topBarBgColor] bg-[--topBarBtnColor] hover:bg-[--topBarBtnColor]">
              Login via Mobile ID
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default Header;
