// src/zustand/hooks/useApplicantQueries.ts
import { useQuery } from '@tanstack/react-query';
import applicantService from '@/zustand/services/applicantServices';
import type { Applicant, ApplicantResponse } from '@/type';

export const useApplicantQueries = (
  page: number,
  size: number,
  sortField: string | null,
  ascending: boolean,
  status: string | null,
  createdById: string | null,
  position: string,
  appliedOnFrom: string | null = null,
  appliedOnTo: string | null = null
) => {
  return useQuery<ApplicantResponse>({
    queryKey: [
      'applicants',
      page,
      size,
      sortField,
      ascending,
      status,
      createdById,
      position,
      appliedOnFrom,
      appliedOnTo,
    ],
    queryFn: () =>
      applicantService.getApplicants(
        page,
        size,
        sortField,
        ascending,
        status,
        createdById,
        null,
        true,
        position,
        appliedOnFrom,
        appliedOnTo
      ),
  });
};

export const useApplicantById = (id: string) => {
  return useQuery<Applicant>({
    queryKey: ['applicant', id],
    queryFn: () => applicantService.getApplicantById(id),
    enabled: !!id,
  });
};
