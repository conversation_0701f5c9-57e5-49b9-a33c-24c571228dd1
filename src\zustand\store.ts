/* eslint-disable no-unused-vars */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface PartnerState {
  name: string;
  partnerId?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  industry?: string;
  size?: string;
  type: PartnerTypeEnums;
  status?: string;
  logo?: string;
  cover?: string;
  socialMediaLinks?: string;
  country?: string;
  state?: string;
  addressLine1?: string;
  city?: string;
  postCode?: string;
  mailAddressLine1?: string;
  mailCity?: string;
  mailPostCode?: string;
  updatePartner: (_partial: Partial<PartnerState>) => void;
  clearPartner: () => void;
}

enum PartnerTypeEnums {
  Company = 'Company',
  Individual = 'Individual',
}

const useStore = create(
  persist<PartnerState>(
    (set) => ({
      name: '',
      partnerId: undefined,
      description: undefined,
      website: undefined,
      email: undefined,
      phone: undefined,
      industry: undefined,
      size: undefined,
      type: PartnerTypeEnums.Company,
      status: undefined,
      logo: undefined,
      cover: undefined,
      socialMediaLinks: undefined,
      country: undefined,
      state: undefined,
      addressLine1: undefined,
      city: undefined,
      postCode: undefined,
      mailAddressLine1: undefined,
      mailCity: undefined,
      mailPostCode: undefined,
      updatePartner: (partial) => set((state) => ({ ...state, ...partial })),
      clearPartner: () =>
        set({
          name: '',
          partnerId: undefined,
          description: undefined,
          website: undefined,
          email: undefined,
          phone: undefined,
          industry: undefined,
          size: undefined,
          type: PartnerTypeEnums.Company,
          status: undefined,
          logo: undefined,
          cover: undefined,
          socialMediaLinks: undefined,
          country: undefined,
          state: undefined,
          addressLine1: undefined,
          city: undefined,
          postCode: undefined,
          mailAddressLine1: undefined,
          mailCity: undefined,
          mailPostCode: undefined,
        }),
    }),
    {
      name: 'partner-store', // Key for localStorage
    }
  )
);

export default useStore;
export { PartnerTypeEnums };
