import { create } from 'zustand';
import axiosClient from '@/utils/axiosClient';
import { persist, createJSONStorage } from 'zustand/middleware';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { authReducer } from './authReducer';
import type { IRegisterProps, ILoginProps, AuthState, IUser } from '@/type';

const initialState: Omit<
  AuthState,
  'register' | 'login' | 'logout' | 'setUser'
> = {
  user: null,
  token:
    typeof window !== 'undefined'
      ? localStorage.getItem('auth_token') || null
      : null,
  isloading: false,
  showLoader: false,
  role:
    typeof window !== 'undefined'
      ? localStorage.getItem('auth_role') || 'partner'
      : 'partner',
};

export const useAuthStore = create<
  AuthState & {
    login: (_credentials: ILoginProps) => Promise<void>;
    logout: () => void;
    register: (_credentials: IRegisterProps) => Promise<void>;
    setUser: (_user: IUser) => void;
    setToken: (_token: string) => void;
    setRole: (_role: string) => void;
  }
>()(
  persist(
    (set) => ({
      ...initialState,

      setToken: (token: string) => {
        set((state) =>
          authReducer(state, {
            type: 'SET_AUTH',
            payload: { user: state.user ?? ({} as IUser), token },
          })
        );
      },

      setRole: (role: string) => {
        localStorage.setItem('auth_role', role);
        set(() => ({ role }));
      },

      register: async ({ email, password, fullName }) => {
        const { showNotification } = useNotificationStore.getState();
        try {
          set((state) => authReducer(state, { type: 'START_LOADING' }));
          set((state) => authReducer(state, { type: 'SHOW_LOADER' }));

          const role = 'partner';

          const res = await axiosClient.post(`/auth/signup`, {
            email,
            password,
            fullName,
            role,
          });
          const { user, token, description } = res.data;

          localStorage.setItem('auth_token', token);
          localStorage.setItem('auth_role', role);

          set((state) =>
            authReducer(state, {
              type: 'SET_AUTH',
              payload: { user, token },
            })
          );
          set(() => ({ role }));

          showNotification(
            description || 'Registration successful!',
            'success'
          );
        } catch (error) {
          console.error('Registration failed', error);
          set((state) => authReducer(state, { type: 'STOP_LOADING' }));
          set((state) => authReducer(state, { type: 'HIDE_LOADER' }));

          showNotification('Registration failed. Please try again.', 'error');
          throw error;
        }
      },

      login: async ({ email, password }) => {
        const { showNotification } = useNotificationStore.getState();
        try {
          set((state) => authReducer(state, { type: 'START_LOADING' }));
          set((state) => authReducer(state, { type: 'SHOW_LOADER' }));

          const role = 'partner';

          const res = await axiosClient.post(`/auth/login`, {
            email,
            password,
            role,
          });
          const { user, token, title } = res.data;

          localStorage.setItem('auth_token', token);
          localStorage.setItem('auth_role', role);

          setTimeout(() => {
            set((state) =>
              authReducer(state, {
                type: 'SET_AUTH',
                payload: { user, token },
              })
            );
            set(() => ({ role }));
          }, 2000);

          showNotification(
            title || `Welcome back, ${user.fullName || 'User'}!`,
            'success'
          );
        } catch (error) {
          set((state) => authReducer(state, { type: 'STOP_LOADING' }));
          set((state) => authReducer(state, { type: 'HIDE_LOADER' }));

          showNotification('Invalid credentials. Please try again.', 'error');
          throw error;
        }
      },

      logout: () => {
        const storageKeysToRemove = [
          'auth_token',
          'auth_role',
          'auth-storage',
          'job-storage',
          'application-storage',
          'course-enrollments-storage',
          'course-form-storage',
          'partner-storage',
          'applied-job-storage',
        ];

        storageKeysToRemove.forEach((key) => localStorage.removeItem(key));
        set((state) => authReducer(state, { type: 'LOGOUT' }));
      },

      setUser: (user) => {
        set((state) =>
          authReducer(state, {
            type: 'SET_AUTH',
            payload: { user, token: '' },
          })
        );
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
