// hooks/useApplicants.ts
import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Applicant, ApplicantFilters } from '@/type';
import applicantService from '@/zustand/services/applicantServices';

export const useApplicants = (jobId: string) => {
  const queryClient = useQueryClient();

  const initialState = {
    search: '',
    selectedIds: [] as string[],
    showFilterModal: false,
    activeTab: 'Pending' as Applicant['status'],
    filters: {
      name: '',
      position: '',
      appliedOnFrom: '',
      appliedOnTo: '',
    },
    page: 1,
    size: 10,
    sortField: null as string | null,
    ascending: true,
  };

  const [state, setState] = useState(initialState);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const updateState = (updates: Partial<typeof initialState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  const resetState = () => {
    setState(initialState);
    queryClient.removeQueries({ queryKey: ['applicants'] });
    queryClient.removeQueries({ queryKey: ['allApplicantIds'] });
  };

  // Main applicants query
  const {
    data: applicantsData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [
      'applicants',
      jobId,
      state.page,
      state.size,
      state.activeTab,
      state.search,
      state.filters,
      state.sortField,
      state.ascending,
    ],
    queryFn: async () => {
      const result = await applicantService.getApplicantsbyApplicantId(
        jobId,
        state.page,
        state.size,
        state.sortField,
        state.ascending,
        state.activeTab,
        state.search || null,
        true,
        state.filters.appliedOnFrom,
        state.filters.appliedOnTo
      );
      return {
        data: result.data,
        total: result.total,
        statusCounts: { ...result.statusCounts },
      };
    },
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const applicants = applicantsData?.data ?? [];
  const total = applicantsData?.total ?? 0;

  useEffect(() => {
    const currentApplicantIds = new Set(applicants.map((a) => a.id));
    setState((prev) => ({
      ...prev,
      selectedIds: prev.selectedIds.filter((id) => currentApplicantIds.has(id)),
    }));
  }, [applicants]);

  // All applicant IDs query
  const { data: allApplicantIdsData } = useQuery({
    queryKey: [
      'allApplicantIds',
      jobId,
      state.activeTab,
      state.search,
      state.filters.name,
      state.filters.position,
      state.filters.appliedOnFrom,
      state.filters.appliedOnTo,
    ],
    queryFn: async () => {
      const result = await applicantService.getApplicantsbyApplicantId(
        jobId,
        1,
        total,
        state.sortField,
        state.ascending,
        state.activeTab,
        state.search || null,
        true,
        state.filters.appliedOnFrom,
        state.filters.appliedOnTo
      );
      return result.data.map((applicant) => applicant.id);
    },
    enabled: total > 0,
    refetchOnWindowFocus: false,
  });

  // Status update mutation
  const { mutateAsync: updateStatus } = useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: Applicant['status'];
    }) => {
      await applicantService.updateApplicant(id, { status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['applicants'] });
      queryClient.invalidateQueries({ queryKey: ['allApplicantIds'] });
    },
  });

  // Handlers
  const handleSort = (field: string) => {
    if (state.sortField === field) {
      updateState({ ascending: !state.ascending });
    } else {
      const isDateField = ['startDate', 'expiryDate'].includes(field);
      updateState({ sortField: field, ascending: isDateField ? false : true });
    }
  };

  const handleApplyFilters = useCallback(
    (newFilters: ApplicantFilters) => {
      updateState({
        filters: {
          name: newFilters.name || '',
          position: newFilters.position || '',
          appliedOnFrom: newFilters.appliedOnFrom || '',
          appliedOnTo: newFilters.appliedOnTo || '',
        },
        page: 1,
        selectedIds: [],
      });
    },
    [updateState]
  );

  const handleResetFilters = useCallback(() => {
    updateState({
      filters: {
        name: '',
        position: '',
        appliedOnFrom: '',
        appliedOnTo: '',
      },
      page: 1,
      selectedIds: [],
    });
  }, [updateState]);

  const getStatusCounts = () => {
    return {
      Pending: applicantsData?.statusCounts?.Pending || 0,
      Shortlisted: applicantsData?.statusCounts?.Shortlisted || 0,
      Contacted: applicantsData?.statusCounts?.Contacted || 0,
      Hired: applicantsData?.statusCounts?.Hired || 0,
      Rejected: applicantsData?.statusCounts?.Rejected || 0,
    };
  };

  const handleTabChange = (status: Applicant['status']) => {
    queryClient.removeQueries({ queryKey: ['applicants'] });
    updateState({ activeTab: status, page: 1, selectedIds: [] });
  };

  const totalPages = Math.ceil(total / state.size);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      updateState({ page: newPage });
    }
  };

  const handleBulkAction = async (action: string, ids: string[]) => {
    if (!ids.length) return;

    switch (action) {
      case 'Shortlisted':
      case 'Contacted':
      case 'Hired':
      case 'Rejected':
        try {
          const batchSize = 50;
          for (let i = 0; i < ids.length; i += batchSize) {
            const batch = ids.slice(i, i + batchSize);
            await Promise.all(
              batch.map((id) =>
                updateStatus({ id, status: action as Applicant['status'] })
              )
            );
          }
          updateState({ selectedIds: [] });
        } catch (error) {
          console.error('Error updating statuses:', error);
        }
        break;

      case 'downloadCVs':
        console.warn('Downloading CVs for IDs:', ids);
        break;

      default:
        break;
    }
  };

  const handleRemoveFromSelection = (idsToRemove: string[]) => {
    updateState({
      selectedIds: state.selectedIds.filter((id) => !idsToRemove.includes(id)),
    });
  };

  return {
    state,
    applicants,
    total,
    allApplicantIdsData,
    isLoading,
    isError,
    totalPages,
    getStatusCounts,
    updateState,
    resetState,
    handleSort,
    handleApplyFilters,
    handleResetFilters,
    handleTabChange,
    handlePageChange,
    handleBulkAction,
    handleRemoveFromSelection,
    updateStatus,
  };
};
