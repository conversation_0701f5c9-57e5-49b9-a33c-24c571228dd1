const host = process.env.NEXT_PUBLIC_API_BASE_URL;

export const customPortalServiceMetaTags = {
  title: {
    default: 'Custom Portal',
    template: '%s | Career Navigator',
  },
  description:
    'Custom Portal in this field may offer a wide range of services, including college admissions consulting, career counseling, academic planning, test preparation guidance, and assistance with choosing the right educational path based on individual interests, strengths, and goals.',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.CONSULTANTSERVICE}`,
    title: 'Custom Portal',
    description:
      'Custom Portal in this field may offer a wide range of services, including college admissions consulting, career counseling, academic planning, test preparation guidance, and assistance with choosing the right educational path based on individual interests, strengths, and goals.',
    images: [
      {
        url: `${host}/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fconsultingpage-header-3.871829de.png&w=1080&q=75`,
        alt: 'Custom Portal',
      },
    ],
    siteName: 'Career Navigator',
  },
};

export const careerChartMetaTags = {
  title: {
    default: 'Work Force Developement and Education Patnership',
    template: '%s | Career Navigator',
  },
  description:
    'A Workforce Development and Education Partnership is a collaboration between educational institutions (such as schools, colleges, or universities) and employers or industry stakeholders to address workforce needs and skill gaps.',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.CAREERCHART}`,
    title: 'Work Force Developement and Education Patnership',
    description:
      'A Workforce Development and Education Partnership is a collaboration between educational institutions (such as schools, colleges, or universities) and employers or industry stakeholders to address workforce needs and skill gaps.',
    images: [
      {
        url: `https://images.unsplash.com/photo-1541746972996-4e0b0f43e02a?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=400&ixid=MnwxfDB8MXxyYW5kb218MHx8bWVldGluZ3x8fHx8fDE2ODk2NTk5MzU&ixlib=rb-4.0.3&q=80&w=600`,
        alt: 'Work Force Developement and Education Patnership',
      },
    ],
    siteName: 'Career Navigator',
  },
};

export const programsMetaTags = {
  title: {
    default: 'Program And Courses',
    template: '%s | Career Navigator',
  },
  description:
    'we expand our efforts to foster global connections through education, we are thrilled to introduce our taxonomy update to Open Programs and Courses, which now includes program categories! Program categories serve as a means to logically group the extensive range of 10,000+ programs in our repository.',
  openGraph: {
    // url: `${host}${UNIVERSITIES_ROUTES_NAMES?.PROGRAMS}`,
    title: 'Program And Courses',
    description:
      'we expand our efforts to foster global connections through education, we are thrilled to introduce our taxonomy update to Open Programs and Courses, which now includes program categories! Program categories serve as a means to logically group the extensive range of 10,000+ programs in our repository.',
    images: [
      {
        url: `${host}/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FBecomeAnAuthorImg.fe618d37.png&w=1080&q=75`,
        alt: 'Program And Courses',
      },
    ],
    siteName: 'Career Navigator',
  },
};

export const skillsExtractorMetaTags = {
  title: {
    default: 'Skill Extractor',
    template: '%s | Career Navigator',
  },
  description:
    'A skill extractor is a tool or software that automatically identifies and extracts specific skills and competencies from a given text or dataset. It is commonly used in the context of analyzing resumes, job descriptions, or other text-based sources to identify and categorize skills possessed by individuals or required by employers',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.SKILLSEXTRACTOR}`,
    title: 'Skill Extractor',
    description:
      'A skill extractor is a tool or software that automatically identifies and extracts specific skills and competencies from a given text or dataset. It is commonly used in the context of analyzing resumes, job descriptions, or other text-based sources to identify and categorize skills possessed by individuals or required by employers',
    siteName: 'Career Navigator',
  },
};

export const aboutMetaTags = {
  title: {
    default: 'About Us',
    template: '%s | Career Navigator',
  },
  description:
    'Career Navigator is a career guidance and advisory service that offers personalized support to individuals in making informed decisions about their professional journey. The service provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals achieve their career goals and thrive in their chosen professions.',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.ABOUT}`,
    title: 'About Us',
    description:
      'Career Navigator is a career guidance and advisory service that offers personalized support to individuals in making informed decisions about their professional journey. The service provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals achieve their career goals and thrive in their chosen professions.',
    images: [
      {
        url: `${host}/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout-hero-right.d78fd3ca.png&w=1920&q=75`,
        alt: 'About Us',
      },
    ],
    siteName: 'Career Navigator',
  },
};

export const contactUsMetaTags = {
  title: {
    default: 'Contact Us',
    template: '%s | Career Navigator',
  },
  description:
    'A dedicated section or page on the Career Navigator website or platform that provides users with various ways to get in touch with our career guidance and advisory service.',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.CONTACTUS}`,
    title: 'Contact Us',
    description:
      'A dedicated section or page on the Career Navigator website or platform that provides users with various ways to get in touch with our career guidance and advisory service.',
    siteName: 'Career Navigator',
  },
};

export const homeMetaTags = {
  title: {
    default:
      'Career Navigator Homepage | Navigating your way through the job market',
    template: '%s | Navigating your way through the job market',
  },
  description:
    'Career Navigator is a comprehensive career guidance and advisory service that offers personalized support to individuals in navigating their professional journey. It provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals make informed decisions and achieve their career goals successfully.',
  openGraph: {
    url: `${host}`,
    title: 'Career Navigator - Navigating your way through the job market',
    description:
      'Career Navigator is a comprehensive career guidance and advisory service that offers personalized support to individuals in navigating their professional journey. It provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals make informed decisions and achieve their career goals successfully.',
    images: [
      {
        url: `${host}/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Four-benefits.a8732966.jpg&w=640&q=75`,
        alt: 'Career Navigator',
      },
    ],
    siteName: 'Career Navigator',
  },
};

export const blogMetaTags = {
  title: {
    default: 'Blog',
    template: '%s | Career Navigator',
  },
  description:
    'A dedicated section or page on the Career Navigator website or platform that provides users with various blog news',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.BLOG}`,
    title: 'Blog',
    description:
      'A dedicated section or page on the Career Navigator website or platform that provides users with various blog news',
    siteName: 'Career Navigator',
  },
};

export const blogArticleMetaTags = {
  title: {
    default: 'Blog Article',
    template: '%s | Career Navigator',
  },
  description:
    'A dedicated section or page on the Career Navigator website or platform that provides users with various blog news',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.BLOG}`,
    title: 'Blog Article',
    description:
      'A dedicated section or page on the Career Navigator website or platform that provides users with various blog news',
    siteName: 'Career Navigator',
  },
};

export const loginMetaTags = {
  title: {
    default: 'Login',
    template: '%s | Career Navigator',
  },
  description:
    'Career Navigator is a comprehensive career guidance and advisory service that offers personalized support to individuals in navigating their professional journey. It provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals make informed decisions and achieve their career goals successfully.',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.LOGIN}`,
    title: 'Login',
    description:
      'Career Navigator is a comprehensive career guidance and advisory service that offers personalized support to individuals in navigating their professional journey. It provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals make informed decisions and achieve their career goals successfully.',
    siteName: 'Career Navigator',
  },
};

export const rankingsMetaTags = {
  title: {
    default: 'Rankings',
    template: '%s | Career Navigator',
  },
  description:
    'A dedicated section or page on the Career Navigator website or platform that provides users with various occupation data',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.RANKINGS}`,
    title: 'Rankings',
    description:
      'A dedicated section or page on the Career Navigator website or platform that provides users with various occupation data',
    siteName: 'Career Navigator',
  },
};

export const registerMetaTags = {
  title: {
    default: 'Register',
    template: '%s | Career Navigator',
  },
  description:
    'Register to Career Navigator which provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals make informed decisions and achieve their career goals successfully.',
  openGraph: {
    // url: `${host}${INFORMATION_ROUTES_NAMES?.REGISTER}`,
    title: 'Register',
    description:
      'Register to Career Navigator which provides assistance in identifying suitable career options, educational choices, and job opportunities, helping individuals make informed decisions and achieve their career goals successfully.',
    siteName: 'Career Navigator',
  },
};

export const cvBuilderMetaTags = {
  title: {
    default: 'CV Builder',
    template: '%s | Career Navigator',
  },
  description:
    'Career Navigator provides assistance in helping individuals to build their CVs',
  openGraph: {
    // url: `${host}${STUDENTS_ROUTES_NAMES?.CVBUILDER}`,
    title: 'CV Builder',
    description:
      'Career Navigator provides assistance in helping individuals to build their CVs',
    siteName: 'Career Navigator',
  },
};
