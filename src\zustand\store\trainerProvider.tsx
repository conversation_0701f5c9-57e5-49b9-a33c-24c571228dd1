import { create } from 'zustand';
import { checkUserIsTrainer } from '@/zustand/services/trainingProviderServices';

interface TrainingProviderState {
  isTrainer: boolean | null;
  isLoading: boolean;
  error: Error | null;

  // Actions
  checkTrainerStatus: (_userId: string) => Promise<void>;
  reset: () => void;
}

export const useTrainingProviderStore = create<TrainingProviderState>(
  (set) => ({
    // Initial state
    isTrainer: null,
    isLoading: false,
    error: null,

    // Actions
    checkTrainerStatus: async (userId: string) => {
      if (!userId) {
        set({ isTrainer: false, isLoading: false, error: null });
        return;
      }

      try {
        set({ isLoading: true, error: null });
        const isTrainer = await checkUserIsTrainer(userId);
        set({ isTrainer, isLoading: false });
      } catch (error) {
        console.error('Error in checkTrainerStatus:', error);
        set({
          isTrainer: false,
          isLoading: false,
          error: error instanceof Error ? error : new Error('Unknown error'),
        });
      }
    },

    reset: () => {
      set({ isTrainer: null, isLoading: false, error: null });
    },
  })
);
