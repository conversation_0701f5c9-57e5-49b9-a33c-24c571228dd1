'use client';

import { XIcon, Clock5Icon } from 'lucide-react';

interface InactiveModulesProps {
  onClose: () => void;
}

export default function Inactive({ onClose }: InactiveModulesProps) {
  return (
    <div className="fixed inset-0 flex justify-center items-center bg-[#0000]/40 backdrop-blur-sm">
      <div className="bg-white px-4 py-6 rounded-lg shadow-lg w-96 text-center">
        <div className="flex justify-end">
          <XIcon
            onClick={onClose}
            className="text-neutral-400 h-[18px] w-[18px] cursor-pointer"
          />
        </div>

        <div>
          <Clock5Icon className="mx-auto mb-6 w-[24px] h-[24px] text-primary-500" />
          <h2 className="text-[18px] font-bold mb-2">Module Inactive</h2>
          <p className="text-neutral-600 mb-4">
            Please contact the administrator to request the module to be
            activated.
          </p>
          <button onClick={onClose} className="w-full py-2">
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
