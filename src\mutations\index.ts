import companyService from '@/services/company';
import jobService from '@/services/jobs/job.service';
import userService from '@/services/user/user.service';
import type { IGetPartners, IJob } from '@/type';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

const useGenerateJobMutation = (
  onSuccessCallback?: (_data: { data: IJob }) => void
) => {
  const [isLoading, setIsLoading] = useState(false);
  const mutation = useMutation({
    mutationFn: jobService.generateJob,
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: (data) => {
      if (onSuccessCallback) onSuccessCallback(data);
    },
    onError: (error) => {
      console.error({ useCreateProductMutation: error });
      // toast({
      //     variant: "destructive",
      //     description:
      //         (error as any)?.response?.data?.message ||
      //         error.message ||
      //         "There was an error creating the product. Please try again",
      // });
    },
  });

  return { generateJob: mutation.mutate, isLoading, ...mutation };
};
const useUpdatePartnerProfile = (onSuccessCallback?: () => void) => {
  const [isLoading, setIsLoading] = useState(false);
  const mutation = useMutation({
    mutationFn: userService.updatePartnerProfile,
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: () => {
      if (onSuccessCallback) onSuccessCallback();
    },
    onError: (error) => {
      console.error({ useCreateProductMutation: error });
    },
  });

  return { updatePartnerProfile: mutation.mutate, isLoading, ...mutation };
};

const useCreatePartnerProfile = (onSuccessCallback?: () => void) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotificationStore.getState();

  const mutation = useMutation({
    mutationFn: userService.createPartnerProfile,
    onMutate: () => {
      setIsLoading(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
    onSuccess: () => {
      showNotification('Partner Account created successfully!', 'success');
      if (onSuccessCallback) onSuccessCallback();
    },
    onError: (error) => {
      console.error({ useCreatePartnerProfile: error });
      showNotification(
        `Error creating partner profile: ${(error as unknown as { data?: { message?: string } })?.data?.message || error.message}`,
        'error'
      );
    },
  });

  return { createPartnerProfile: mutation.mutate, isLoading, ...mutation };
};

const useUpdatePartnersById = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotificationStore.getState();

  return useMutation({
    mutationFn: ({
      id,
      updateData,
    }: {
      id: string;
      updateData: Partial<IGetPartners>;
    }) => companyService.updatePartnerById(id, updateData),

    onSuccess: (updatedPartner, variables) => {
      queryClient.setQueryData(['partner', variables.id], updatedPartner);
      showNotification('Company Details updated successfully!', 'success');
    },

    onError: (error) => {
      console.error('Failed to update partner', error);
      showNotification('Error updating Company Details', 'error');
    },
  });
};

export {
  useGenerateJobMutation,
  useUpdatePartnerProfile,
  useCreatePartnerProfile,
  useUpdatePartnersById,
};
