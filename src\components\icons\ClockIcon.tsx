import React from 'react';

interface ClockIconPorps {
  height?: number;
  width?: number;
  stopColor1?: string;
  stopColor2?: string;
}

function ClockIcon({
  height = 48,
  width = 48,
  stopColor1,
  stopColor2,
}: ClockIconPorps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.84961 9.60604V15.6666H14.9102M7.33398 23.9999C7.33398 33.2047 14.7959 40.6666 24.0007 40.6666C33.2054 40.6666 40.6673 33.2047 40.6673 23.9999C40.6673 14.7952 33.2054 7.33325 24.0007 7.33325C17.8323 7.33325 12.4466 10.6842 9.56463 15.665M24.0043 13.9999L24.0033 24.0073L31.0694 31.0734"
        stroke="url(#paint0_linear_4117_12332)"
        strokeWidth={3}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4117_12332"
          x1="7.33398"
          y1="7.33325"
          x2="40.6673"
          y2="40.6666"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={stopColor1} />
          <stop offset="1" stopColor={stopColor2} />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default ClockIcon;
