// components/OverViewCard.tsx
import React from 'react';
import { Card, CardTitle } from '@/components/ui/card';
import { Button } from '@headlessui/react';
import { useRouter } from 'next/navigation';

interface OverCardProps {
  icon: React.ReactNode;
  title: string | number;
  description: string;
  arrowIcon?: React.ReactNode;
  route?: string;
}

export const OverViewCard: React.FC<OverCardProps> = ({
  icon,
  title,
  description,
  arrowIcon,
  route,
}) => {
  const router = useRouter();

  const handleNavigation = () => {
    if (route) {
      router.push(route);
    }
  };

  return (
    <Card
      className="space-y-2 bg-white border border-neutral-200 rounded-lg px-[32px] py-[24px] flex justify-between items-center h-[140px]"
      onClick={handleNavigation}
    >
      {/* Description + Arrow */}
      <div className="flex items-center gap-6">
        {/* Icon */}
        <div className=" w-[48px] h-[48px]">{icon}</div>
        <div className="flex flex-col">
          {/* Title */}
          <CardTitle className=" text-[36px] font-semibold leading-[44px] text-neutral-900 text-justify">
            {title}
          </CardTitle>
          <p className="text-[16px] font-normal text-neutral-500">
            {description}
          </p>
        </div>
      </div>

      <Button className="w-[32px] h-[32px] !text-[--buttonColor]">
        {arrowIcon}
      </Button>
    </Card>
  );
};
