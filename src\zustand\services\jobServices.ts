import axiosClient from '@/utils/axiosClient';
import type { Job } from '@/type/jobType';

const API_URL = '/jobs';

const jobService = {
  getJobs: async (
    page = 1,
    size = 10,
    search?: string,
    userId?: string,
    status?: string,
    ascending = true,
    sortField = 'createdAt',
    IncludeStatusCounts = true,
    listingType?: string,
    createdOnFrom?: string,
    createdOnTo?: string,
    ClosedDateFrom?: string,
    ClosedDateTo?: string
  ): Promise<{ data: Job[]; page: number; size: number; total: number }> => {
    const params = new URLSearchParams();

    // Append required parameters
    params.append('page', page.toString());
    params.append('size', size.toString());
    params.append('ascending', ascending.toString());
    params.append('sortField', sortField);
    params.append('IncludeStatusCounts', IncludeStatusCounts.toString());

    // Append optional parameters if they are provided
    if (status) {
      params.append('status', status);
    }
    if (search && search.trim() !== '') {
      params.append('search', search.trim());
    }
    if (userId) {
      params.append('userId', userId);
    }
    if (listingType) {
      params.append('listingType', listingType);
    }
    if (createdOnFrom) {
      params.append('createdOnFrom', createdOnFrom);
    }
    if (createdOnTo) {
      params.append('createdOnTo', createdOnTo);
    }
    if (ClosedDateFrom) {
      params.append('ClosedDateFrom', ClosedDateFrom);
    }
    if (ClosedDateTo) {
      params.append('ClosedDateTo', ClosedDateTo);
    }

    try {
      const response = await axiosClient.get<{
        data: Job[];
        page: number;
        size: number;
        total: number;
      }>(`${API_URL}?${params.toString()}`);

      return {
        ...response.data,
        data: response.data.data.map((job) => ({
          ...job,
          companyName: job.companyName ?? 'Unknown Company', // Default value for companyName
        })),
      };
    } catch (error) {
      console.error('Error fetching jobs:', error);
      throw new Error('Failed to fetch jobs');
    }
  },

  getJobsByUserId: async (userId: string): Promise<Job[]> => {
    try {
      const response = await axiosClient.get<{ data: Job[] }>(
        `${API_URL}/my/${userId}`
      );
      return response.data.data.map((job) => ({
        ...job,
        companyName: job.companyName ?? 'Unknown Company', // Default value for companyName
      }));
    } catch (error) {
      console.error(`Error fetching jobs for user ${userId}:`, error);
      throw new Error('Failed to fetch user jobs');
    }
  },

  getJobById: async (id: string): Promise<Job> => {
    try {
      const response = await axiosClient.get<{ success: boolean; data: Job }>(
        `${API_URL}/${id}`
      );

      // Check if the response has the expected structure
      if (response.data && response.data.success && response.data.data) {
        return response.data.data;
      } else {
        console.error('Unexpected API response structure:', response.data);
        throw new Error('Unexpected API response structure');
      }
    } catch (error) {
      console.error(`Error fetching job with id ${id}:`, error);
      throw new Error('Failed to fetch job');
    }
  },

  addJob: async (job: Job): Promise<Job> => {
    try {
      const response = await axiosClient.post<Job>(API_URL, job);
      return response.data;
    } catch (error) {
      console.error('Error adding job:', error);
      throw new Error('Failed to add job');
    }
  },

  updateJob: async (id: string, updatedJob: Partial<Job>): Promise<Job> => {
    try {
      const response = await axiosClient.put<Job>(
        `${API_URL}/${id}`,
        updatedJob
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating job with id ${id}:`, error);
      throw new Error('Failed to update job');
    }
  },

  updateJobStatus: async (id: string, status: string): Promise<Job> => {
    try {
      const response = await axiosClient.put<Job>(`${API_URL}/${id}/status`, {
        status,
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating job status for id ${id}:`, error);
      throw new Error('Failed to update job status');
    }
  },

  // In your jobService.ts
  updateJobsClose: async (
    jobId: string,
    applicantIds: string[]
  ): Promise<void> => {
    try {
      // Send the array directly as the request body
      await axiosClient.patch(`${API_URL}/${jobId}/close`, applicantIds);
    } catch (error) {
      console.error(`Error closing applicants for job ${jobId}:`, error);
      throw new Error('Failed to close applicants');
    }
  },

  deleteJob: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`${API_URL}/${id}`);
    } catch (error) {
      console.error(`Error deleting job with id ${id}:`, error);
      throw new Error('Failed to delete job');
    }
  },
};

export default jobService;
