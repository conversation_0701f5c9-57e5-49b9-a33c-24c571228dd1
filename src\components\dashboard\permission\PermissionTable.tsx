/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-unused-vars */
'use client';

import type { ReactNode } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { usePermission } from '@/hooks/usePermission';

interface PermissionColumn {
  id: string;
  label: string;
  permission?: Permission | Permission[];
  permissionOptions?: PermissionCheckOptions;
  render: (item: any, index: number) => ReactNode;
  className?: string;
}

interface PermissionAction {
  id: string;
  label: string;
  permission: Permission | Permission[];
  permissionOptions?: PermissionCheckOptions;
  onClick: (item: any, index: number) => void;
  variant?: 'default' | 'destructive' | 'outline';
  icon?: ReactNode;
}

interface PermissionTableProps {
  data: any[];
  columns: PermissionColumn[];
  actions?: PermissionAction[];
  className?: string;
  emptyMessage?: string;
  showActionsColumn?: boolean;
}

/**
 * Table component that filters columns and actions based on user permissions
 */
export function PermissionTable({
  data,
  columns,
  actions = [],
  className = '',
  emptyMessage = 'No data available',
  showActionsColumn = true,
}: PermissionTableProps) {
  const { checkPermission, isLoading } = usePermission();

  // Filter columns based on permissions
  const visibleColumns = columns.filter((column) => {
    if (!column.permission) return true;
    return checkPermission(column.permission, column.permissionOptions);
  });

  // Filter actions based on permissions
  const getVisibleActions = (item: any, index: number) => {
    return actions.filter((action) =>
      checkPermission(action.permission, action.permissionOptions)
    );
  };

  // Check if any actions are visible for any row
  const hasAnyVisibleActions = data.some(
    (item, index) => getVisibleActions(item, index).length > 0
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className={className}>
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id} className={column.className}>
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableHead>
              ))}
              {showActionsColumn && actions.length > 0 && (
                <TableHead>
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(3)].map((_, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={column.id}>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </TableCell>
                ))}
                {showActionsColumn && actions.length > 0 && (
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={`${className} text-center py-8`}>
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <Table>
        <TableHeader>
          <TableRow>
            {visibleColumns.map((column) => (
              <TableHead key={column.id} className={column.className}>
                {column.label}
              </TableHead>
            ))}
            {showActionsColumn && hasAnyVisibleActions && (
              <TableHead>Actions</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item, index) => {
            const visibleActions = getVisibleActions(item, index);

            return (
              <TableRow key={index}>
                {visibleColumns.map((column) => (
                  <TableCell key={column.id} className={column.className}>
                    {column.render(item, index)}
                  </TableCell>
                ))}
                {showActionsColumn && hasAnyVisibleActions && (
                  <TableCell>
                    <div className="flex space-x-2">
                      {visibleActions.map((action) => (
                        <button
                          key={action.id}
                          onClick={() => action.onClick(item, index)}
                          className={`px-3 py-1 rounded text-sm ${
                            action.variant === 'destructive'
                              ? 'bg-red-500 text-white hover:bg-red-600'
                              : action.variant === 'outline'
                                ? 'border border-gray-300 hover:bg-gray-50'
                                : 'bg-blue-500 text-white hover:bg-blue-600'
                          }`}
                        >
                          {action.icon && (
                            <span className="mr-1">{action.icon}</span>
                          )}
                          {action.label}
                        </button>
                      ))}
                    </div>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
