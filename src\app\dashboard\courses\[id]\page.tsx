'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getCourseById } from '@/zustand/services/courseServices';
import { ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import type { CourseRespones } from '@/types/courseType';
import CourseDetailsHeader from '@/components/dashboard/components/courses/courseDetails/couseDetailsHeader';
import CourseDetailsInfo from '@/components/dashboard/components/courses/courseDetails/courseDetailsInfo';
import CourseDetailsContent from '@/components/dashboard/components/courses/courseDetails/courseDetailsContent';
import { Card } from '@/components/ui/card';
import CourseLearnersTable from '@/components/dashboard/components/courses/courseDetails/courseLearner';
import { Loader } from '@/components/dashboard/common/Loader';

export default function CourseDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [course, setCourse] = useState<CourseRespones | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCourse = async () => {
    try {
      setIsLoading(true);
      const data = await getCourseById(id);
      setCourse(data);
    } catch (err) {
      console.error('Error fetching course:', err);
      setError('Failed to load course details. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    fetchCourse();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handleBack = () => {
    router.push('/dashboard/courses');
  };

  if (isLoading || !id) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
        <Loader />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error || 'Course not found'}</p>
        <Button onClick={handleBack} variant="outline" className="mt-4">
          Back to Courses
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="mb-4">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'My Courses',
              href: '/dashboard/courses',
            },
            {
              label: course.data.name,
              href: `/dashboard/courses/${course.data.id}`,
            },
          ]}
        />
      </div>

      <Button variant="outline" className="py-6 px-6 my-6" onClick={handleBack}>
        <ChevronLeft className="h-4 w-4 mr-1" />
        Back
      </Button>

      <Card className="p-6 rounded-lg bg-white">
        <CourseDetailsHeader course={course.data} />
        <CourseDetailsInfo course={course.data} />
        <CourseDetailsContent course={course.data} />
      </Card>

      {course.data.id && <CourseLearnersTable courseId={course.data.id} />}
    </div>
  );
}
