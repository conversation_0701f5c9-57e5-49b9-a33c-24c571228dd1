type ColorConfig = Record<string, string>;
type CssVar = { varName: string; opacity?: number; darkenFactor?: number };
type CssVarType = Record<string, CssVar[]>;

export const updateAppColors = (colorConfigs: ColorConfig[]) => {
  const rootStyle = document.documentElement.style;

  // Internal map of logical color keys to CSS variable names
  const variableMap: CssVarType = {
    brandColor: [
      { varName: '--primary_400', opacity: 0.2 },
      { varName: '--primary_500Main' },
      { varName: '--primary_600', opacity: 0.2 },
    ],
    topBarBgColor: [{ varName: '--topBarBgColor' }],
    topBarButtonColor: [{ varName: '--topBarBtnColor' }],
    navMenuColor: [
      { varName: '--navMenuColor' },
      { varName: '--navMenuColorOpacity10', opacity: 1, darkenFactor: 0.1 },
    ],
    buttonColor: [
      { varName: '--buttonColor' },
      { varName: '--buttonColorDark', darkenFactor: 0.1 },
      { varName: '--buttonColorLight', opacity: 0.2 },
    ],
    buttonStyle: [{ varName: '--buttonStyle' }],
    headingFont: [{ varName: '--headingFont' }],
    bodyTextFont: [{ varName: '--bodyTextFont' }],
    headingTextColor: [{ varName: '--headingTextColor' }],
    bodyTextColor: [{ varName: '--bodyTextColor' }],
  };

  colorConfigs.forEach((config) => {
    const [colorKey, colorValue] = Object.entries(config)[0];

    const cssVars = variableMap[colorKey];
    if (!cssVars) {
      console.warn(`No CSS variables defined for color key: ${colorKey}`);
      return;
    }

    cssVars.forEach((cssVar: CssVar) => {
      if (cssVar?.opacity || cssVar?.darkenFactor) {
        rootStyle.setProperty(
          cssVar.varName,
          addOpacityToDarkerHex(colorValue, cssVar.opacity, cssVar.darkenFactor)
        );
      } else {
        rootStyle.setProperty(cssVar.varName, colorValue);
      }
    });
  });
};

// const addOpacityToHex = (hex: string, opacity: number): string => {
//   hex = hex.replace('#', '')

//   // Parse the hex color into RGB components
//   const r = parseInt(hex.substring(0, 2), 16)
//   const g = parseInt(hex.substring(2, 4), 16)
//   const b = parseInt(hex.substring(4, 6), 16)

//   // Return the RGBA color string
//   return `rgba(${r}, ${g}, ${b}, ${opacity})`
// }

const addOpacityToDarkerHex = (
  hex: string,
  opacity = 1,
  darkenFactor = 0.1
): string => {
  hex = hex.replace('#', '');

  if (hex.length === 3) {
    hex = hex
      .split('')
      .map((char) => char + char)
      .join('');
  }

  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);

  // Apply darkening: reduce RGB values by the darken factor
  r = Math.round(r * (1 - darkenFactor));
  g = Math.round(g * (1 - darkenFactor));
  b = Math.round(b * (1 - darkenFactor));

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
