'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { dashboardSideBarRoutes } from '@/constants/navConfig';
import Image from 'next/image';
import { RequestAccessWidget } from './trainingProvider/requestWidget';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { useTrainingProviderStore } from '@/zustand/store/trainerProvider';
import GovernmentEmblem from '@/assets/DashboardEmblem.svg';
import useSettingsStore from '@/zustand/store/settingsStore';

const SideNavigation: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { appearanceSettings } = useSettingsStore();
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const { user } = useAuthStore();
  const { isTrainer, isLoading, checkTrainerStatus } =
    useTrainingProviderStore();

  useEffect(() => {
    if (user?.id) {
      checkTrainerStatus(user.id);
    } else {
      useTrainingProviderStore.getState().reset();
    }
  }, [user?.id, checkTrainerStatus]);

  const toggleSection = (label: string) => {
    setExpandedSections((prev) =>
      prev.includes(label)
        ? prev.filter((section) => section !== label)
        : [...prev, label]
    );
  };

  const isActive = (route: string) => pathname === route;

  const filteredRoutes = dashboardSideBarRoutes.filter((route) => {
    if (route.label === 'Courses') {
      return isTrainer === true;
    }
    return true;
  });

  const isBgWhite = ['#ffffff', '#fff', '#FFF', '#FFFFFF'].includes(
    appearanceSettings.navMenuColor
  );

  return (
    <div className="bg-[--navMenuColor] text-white w-full h-full flex flex-col overflow-y-auto relative z-10">
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="px-4 py-6">
          <div className="flex flex-col space-y-2">
            {filteredRoutes.map((item) => (
              <div key={item.label}>
                <div
                  role="button"
                  tabIndex={0}
                  aria-expanded={
                    !!item.children && expandedSections.includes(item.label)
                  }
                  aria-controls={
                    item.children ? `section-${item.label}` : undefined
                  }
                  className={`flex items-center justify-between cursor-pointer px-3 py-6 ${
                    isActive(item.route || '')
                      ? `${isBgWhite ? 'bg-[#0000001a]' : 'bg-[#ffffff1a]'} border-l-4 ${isBgWhite ? 'border-primary-500' : 'border-white'} rounded-r`
                      : `${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'}`
                  }`}
                  onClick={(e) => {
                    e.preventDefault();
                    if (item.children) {
                      toggleSection(item.label);
                    } else if (item.route) {
                      router.push(item.route);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      if (item.children) {
                        toggleSection(item.label);
                      } else if (item.route) {
                        router.push(item.route);
                      }
                    }
                  }}
                >
                  <div className="flex items-center space-x-4">
                    {item?.icon}
                    <span
                      className={`text-[16px] font-medium ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
                    >
                      {item.label}
                    </span>
                  </div>
                  {item.children && (
                    <ChevronDown
                      className={`w-4 h-4 transition-transform ${isBgWhite ? 'text-primary-500' : 'text-white'} ${
                        expandedSections.includes(item.label)
                          ? 'rotate-180'
                          : ''
                      }`}
                    />
                  )}
                </div>
                {item.children && expandedSections.includes(item.label) && (
                  <div className="ml-7 mt-2 space-y-2 text-[16px] font-medium">
                    {item.children.map((child) => (
                      <div
                        key={child.route}
                        role="button"
                        tabIndex={0}
                        aria-current={
                          isActive(child.route) ? 'page' : undefined
                        }
                        onClick={() => router.push(child.route)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            router.push(child.route);
                          }
                        }}
                        className={`cursor-pointer px-4 py-3 outline-none focus:ring-2 focus:ring-primary-500 ${
                          isActive(child.route)
                            ? `${isBgWhite ? 'bg-[#0000001a]' : 'bg-[#ffffff1a]'} border-l-4 ${isBgWhite ? 'border-primary-500' : 'border-white'} rounded-r`
                            : `${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'}`
                        }`}
                      >
                        <div>
                          <span
                            className={`text-[16px] font-medium ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
                          >
                            {child.label}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="px-5 py-6">
        {!isLoading && !isTrainer && <RequestAccessWidget />}
        <div
          role="button"
          tabIndex={0}
          className="flex items-center justify-between cursor-pointer mt-4"
          onClick={() => router.push('/dashboard')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              router.push('/dashboard');
            }
          }}
        >
          <div className="flex items-center space-x-4 relative">
            <Image
              src={appearanceSettings?.governmentEmblem || GovernmentEmblem}
              width={100}
              height={100}
              className="w-auto h-[64px] cursor-pointer"
              alt="bubbleIcon"
              onClick={() => router.push('/dashboard/contact-support')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideNavigation;
