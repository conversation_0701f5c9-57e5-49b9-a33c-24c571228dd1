@echo off
REM Docker build script for Windows
REM Career Navigator Pro Partner UI

echo Building Career Navigator Pro Partner UI Docker image...

REM Set variables
set IMAGE_NAME=career-navigator-ui
set TAG=latest

REM Build the Docker image
echo Building production image...
docker build -t %IMAGE_NAME%:%TAG% .

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Docker image built successfully!
    echo Image: %IMAGE_NAME%:%TAG%
    echo.
    echo To run the container:
    echo docker run -p 3000:3000 %IMAGE_NAME%:%TAG%
    echo.
    echo Or use docker-compose:
    echo docker-compose up
) else (
    echo.
    echo Docker build failed!
    exit /b 1
)

pause
