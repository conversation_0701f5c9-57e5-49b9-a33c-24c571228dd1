import type React from 'react';
import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';

interface ApplicantProgressTrackerProps {
  applicant: {
    status: 'Pending' | 'Shortlisted' | 'Contacted' | 'Hired' | 'Rejected';
  };
}

const ApplicantProgressTracker: React.FC<ApplicantProgressTrackerProps> = ({
  applicant,
}) => {
  return (
    <div className="">
      {applicant.status === 'Rejected' && (
        <div className="bg-destructive-50 text-destructive-500 py-2 px-4 flex items-center justify-center gap-4 font-medium">
          <div className="bg-destructive-500 text-white rounded-full p-1 w-8 h-8 flex items-center justify-center">
            <CheckIcon className="h-[21.333px] w-[21.333px] text-white" />
          </div>
          <span>Applicant Rejected</span>
        </div>
      )}
      <div className="relative h-1 bg-neutral-100">
        <div
          className={cn(
            'absolute top-0 left-0 h-1',
            applicant.status === 'Rejected'
              ? 'bg-destructive-500'
              : 'bg-primary-500'
          )}
          style={{
            width:
              applicant.status === 'Rejected'
                ? '100%'
                : applicant.status === 'Pending'
                  ? '25%'
                  : applicant.status === 'Shortlisted'
                    ? '50%'
                    : applicant.status === 'Contacted'
                      ? '75%'
                      : applicant.status === 'Hired'
                        ? '100%'
                        : '25%',
          }}
        ></div>
      </div>
      <div className="relative grid grid-cols-4 text-[16px] p-4">
        <div className="relative">
          <div
            className={cn(
              'flex items-center gap-4',
              applicant.status === 'Rejected'
                ? 'text-neutral-300'
                : applicant.status === 'Pending' ||
                    applicant.status === 'Shortlisted' ||
                    applicant.status === 'Contacted' ||
                    applicant.status === 'Hired'
                  ? ''
                  : 'text-neutral-900'
            )}
          >
            {applicant.status === 'Rejected' ? (
              <div className="border-2 border-neutral-300 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block"></span>
              </div>
            ) : applicant.status === 'Pending' ||
              applicant.status === 'Shortlisted' ||
              applicant.status === 'Contacted' ||
              applicant.status === 'Hired' ? (
              <div className="bg-primary-500 text-white rounded-full w-8 h-8 flex items-center justify-center">
                <CheckIcon className="h-[21.333px] w-[21.333px] text-white" />
              </div>
            ) : (
              <div className="border-2 border-neutral-100 rounded-full w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block bg-neutral-100 rounded-full" />
              </div>
            )}
            <span
              className={cn(
                'text-[18px] font-medium leading-[27px]',
                applicant.status === 'Rejected'
                  ? 'text-neutral-300'
                  : 'text-primary-500'
              )}
            >
              Applied
            </span>
          </div>
        </div>

        <div className="relative">
          <div
            className={cn(
              'flex items-center gap-4',
              applicant.status === 'Rejected'
                ? 'text-neutral-300'
                : applicant.status === 'Shortlisted' ||
                    applicant.status === 'Contacted' ||
                    applicant.status === 'Hired'
                  ? ''
                  : 'text-neutral-500'
            )}
          >
            {applicant.status === 'Rejected' ? (
              <div className="border-2 border-neutral-300 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block bg-neutral-100 rounded-full"></span>
              </div>
            ) : applicant.status === 'Shortlisted' ||
              applicant.status === 'Contacted' ||
              applicant.status === 'Hired' ? (
              <div className="bg-primary-500 text-white rounded-full w-8 h-8 flex items-center justify-center">
                <CheckIcon className="h-[21.333px] w-[21.333px] text-white" />
              </div>
            ) : (
              <div className="border-2 border-neutral-100 rounded-full w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block bg-neutral-100 rounded-full" />
              </div>
            )}

            <span
              className={cn(
                'text-[18px] font-medium leading-[27px]',
                applicant.status === 'Rejected'
                  ? 'text-neutral-300'
                  : applicant.status === 'Shortlisted'
                    ? 'text-primary-500'
                    : 'text-neutral-900'
              )}
            >
              Shortlisted
            </span>
          </div>
        </div>

        <div className="relative">
          <div
            className={cn(
              'flex items-center gap-4',
              applicant.status === 'Rejected'
                ? 'text-neutral-300'
                : applicant.status === 'Contacted' ||
                    applicant.status === 'Hired'
                  ? ''
                  : 'text-neutral-500'
            )}
          >
            {applicant.status === 'Rejected' ? (
              <div className="border-2 border-neutral-300 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block"></span>
              </div>
            ) : applicant.status === 'Contacted' ||
              applicant.status === 'Hired' ? (
              <div className="bg-primary-500 text-white rounded-full w-8 h-8 flex items-center justify-center">
                <CheckIcon className="h-[21.333px] w-[21.333px] text-white" />
              </div>
            ) : (
              <div className="border-2 border-neutral-100 rounded-full w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block bg-neutral-100 rounded-full" />
              </div>
            )}
            <span
              className={cn(
                'text-[18px] font-medium leading-[27px]',
                applicant.status === 'Rejected'
                  ? 'text-neutral-300'
                  : applicant.status === 'Contacted'
                    ? 'text-primary-500'
                    : 'text-neutral-900'
              )}
            >
              Contacted
            </span>
          </div>
        </div>

        <div className="relative">
          <div
            className={cn(
              'flex items-center gap-4',
              applicant.status === 'Rejected'
                ? 'text-neutral-300'
                : applicant.status === 'Hired'
                  ? ''
                  : 'text-neutral-500'
            )}
          >
            {applicant.status === 'Rejected' ? (
              <div className="border-2 border-neutral-300 rounded-full p-1 w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block"></span>
              </div>
            ) : applicant.status === 'Hired' ? (
              <div className="bg-primary-500 text-white rounded-full w-8 h-8 flex items-center justify-center">
                <CheckIcon className="h-[21.333px] w-[21.333px] text-white" />
              </div>
            ) : (
              <div className="border-2 border-neutral-100 rounded-full w-8 h-8 flex items-center justify-center">
                <span className="w-[10px] h-[10px] block bg-neutral-100 rounded-full" />
              </div>
            )}
            <span
              className={cn(
                'text-[18px] font-medium leading-[27px]',
                applicant.status === 'Rejected'
                  ? 'text-neutral-300'
                  : applicant.status === 'Hired'
                    ? 'text-primary-500'
                    : 'text-neutral-900'
              )}
            >
              Hired
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicantProgressTracker;
