'use client';

import { useState, useRef, useEffect } from 'react';
import type React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, XIcon, ChevronUp, ChevronDown } from 'lucide-react';
import type { SkillsCovered } from '@/types/courseType';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
} from '@/components/ui/dialog';

// Mock API call or data fetch would go here
const suggestions: SkillsCovered[] = [
  { key: 'angular-web-framework', value: 'Angular (Web Framework)' },
  { key: 'blockchain', value: 'Blockchain' },
  {
    key: 'bootstrap-front-end-framework',
    value: 'Bootstrap (Front-End Framework)',
  },
  { key: 'finance', value: 'Finance' },
  { key: 'market-research', value: 'Market Research' },
  { key: 'ideation', value: 'Ideation' },
];

export function CourseSkillsTab() {
  const { course, addSkillCovered, removeSkillCovered } = useCourseFormStore();
  const [newSkill, setNewSkill] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showLastSkillWarning, setShowLastSkillWarning] = useState(false);
  const [skillToRemove, setSkillToRemove] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Initialize with empty array if no skills are selected
  const [extractedSkills, setExtractedSkills] = useState<SkillsCovered[]>([]);

  function handleClickOutside(event: MouseEvent) {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Only load suggestions when dropdown is opened and input has value
  useEffect(() => {
    if (isDropdownOpen && newSkill.trim()) {
      setExtractedSkills(suggestions);
    }
  }, [isDropdownOpen, newSkill]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleAddSkill = () => {
    if (!newSkill.trim()) return;

    const skill: SkillsCovered = {
      key: newSkill.trim().toLowerCase().replace(/\s+/g, '-'),
      value: newSkill.trim(),
    };

    addSkillCovered(skill);
    setNewSkill('');
    setIsDropdownOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  const handleRemoveSkill = (key: string) => {
    if (course.skillsCovered?.length === 1) {
      setSkillToRemove(key);
      setShowLastSkillWarning(true);
    } else {
      removeSkillCovered(key);
    }
  };

  const handleConfirmRemoveLastSkill = () => {
    if (skillToRemove) {
      removeSkillCovered(skillToRemove);
      setSkillToRemove(null);
    }
    setShowLastSkillWarning(false);
  };

  const handleCancelRemoveLastSkill = () => {
    setSkillToRemove(null);
    setShowLastSkillWarning(false);
  };

  const filteredSkills = extractedSkills.filter((skill) =>
    skill.value.toLowerCase().includes(newSkill.toLowerCase())
  );

  const handleSelectSkill = (skill: SkillsCovered) => {
    addSkillCovered(skill);
    setNewSkill('');
    setIsDropdownOpen(false);
  };

  return (
    <div className="space-y-6">
      <div className="">
        <h2 className="text-[18px] font-medium pb-4 text-neutral-900 leading-[28px]">
          {course.skillsCovered?.length || 0} Total Skills
        </h2>
        <div className="relative flex items-center w-[500px] border-2 border-neutral-300 px-4 rounded-lg">
          <Search className="text-neutral-400" />
          <Input
            id="skills"
            value={newSkill}
            onChange={(e) => setNewSkill(e.target.value)}
            onFocus={() => setIsDropdownOpen(true)}
            onKeyDown={handleKeyDown}
            placeholder="Add another skill"
            className="pr-10 border-none"
          />
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 h-8 w-8"
            onClick={toggleDropdown}
          >
            {isDropdownOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-10 mt-1 bg-white border border-gray-200 rounded shadow-lg max-h-60 w-[500px] overflow-auto"
        >
          {newSkill.trim() && (
            <div
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={handleAddSkill}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleAddSkill();
                }
              }}
              tabIndex={0}
              role="button"
            >
              Add &rdquo;{newSkill}&rdquo; as new skill
            </div>
          )}

          {filteredSkills.map((skill) => (
            <div
              key={skill.key}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSelectSkill(skill)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleSelectSkill(skill);
                }
              }}
              tabIndex={0}
              role="button"
            >
              {skill.value}
            </div>
          ))}

          {newSkill.trim() && filteredSkills.length === 0 && (
            <div className="px-4 py-2 text-gray-500">
              No matching skills found
            </div>
          )}
        </div>
      )}

      {course.skillsCovered && course.skillsCovered.length > 0 && (
        <div>
          <div className="flex flex-col gap-2">
            {course.skillsCovered.map((skill) => (
              <Badge
                key={skill.key}
                variant="outline"
                className="text-[16px] rounded-md bg-neutral-200 text-neutral-900 py-1.5 px-3 flex items-center w-fit"
              >
                {skill.value || skill.key}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveSkill(skill.key)}
                  className="h-4 w-4 p-0 ml-2"
                >
                  <XIcon className="h-3 w-3" />
                  <span className="sr-only">Remove</span>
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      <Dialog
        open={showLastSkillWarning}
        onOpenChange={setShowLastSkillWarning}
      >
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
            <DialogDescription>
              Are you sure you want to remove the last skill?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelRemoveLastSkill}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              className="bg-destructive-500"
              onClick={handleConfirmRemoveLastSkill}
            >
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
