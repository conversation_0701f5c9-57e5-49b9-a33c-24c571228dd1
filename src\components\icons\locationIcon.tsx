import React from 'react';

const LocationIcon = ({ stroke }: { stroke?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M16.9185 15.479C16.2066 16.1908 14.8161 17.5814 13.6943 18.7032C12.757 19.6405 11.2385 19.6403 10.3013 18.703C9.20019 17.602 7.83409 16.2359 7.07724 15.479C4.35967 12.7614 4.35967 8.35537 7.07724 5.63779C9.79482 2.92022 14.2009 2.92022 16.9185 5.63779C19.636 8.35537 19.636 12.7614 16.9185 15.479Z"
        stroke={stroke}
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.6074 10.5584C14.6074 11.9996 13.4391 13.1679 11.9979 13.1679C10.5566 13.1679 9.38831 11.9996 9.38831 10.5584C9.38831 9.11719 10.5566 7.94885 11.9979 7.94885C13.4391 7.94885 14.6074 9.11719 14.6074 10.5584Z"
        stroke={stroke}
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default LocationIcon;
