runtime: nodejs20

service: career-navigator-pro-partner-ui

env_variables:
  NODE_ENV: "production"  # Available during runtime

build_env_variables:
  GOOGLE_NODE_RUN_SCRIPTS: ''  # Available during the build process
  NODE_ENV: "production"  # Available during the build process

handlers:
  - url: /_next/static/
    static_dir: .next/static

  - url: /static/
    static_dir: public/static

  - url: /_next/image/
    script: auto

  - url: /.*
    script: auto