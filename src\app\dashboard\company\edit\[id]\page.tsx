'use client';

import { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { useAdminValues, usePartnersById } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/customButton';
import { useUpdatePartnersById } from '@/mutations';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import { Loader } from '@/components/dashboard/common/Loader';
import { EditCompanyRepresentatives } from '@/components/dashboard/components/company/editDetails/editcompanyRepresentative';
import { validationMessages } from '@/constants/validationMessages';
import { CompanyDetailsForm } from '@/components/dashboard/components/company/editDetails/editCompanyDetails';

const {
  companyNameRequired,
  addressRequired,
  cityRequired,
  stateRequired,
  countryRequired,
  zipCodeRequired,
  industryRequired,
  companySizeRequired,
} = validationMessages;

interface CompanyFormData {
  name: string;
  partnerId: string;
  logo: string;
  addressLine1: string;
  mailAddressLine1: string;
  city: string;
  state: string;
  country: string;
  postCode: string;
  industry: string;
  companySize: string;
}

const validationSchema = Yup.object({
  name: Yup.string().required(companyNameRequired).trim(),
  //   partnerId: Yup.string().required('Partner Id is required').trim(),
  addressLine1: Yup.string().required(addressRequired).trim(),
  city: Yup.string().required(cityRequired).trim(),
  state: Yup.string().required(stateRequired).trim(),
  country: Yup.string().required(countryRequired).trim(),
  postCode: Yup.string().required(zipCodeRequired).trim(),
  industry: Yup.string().required(industryRequired).trim(),
  companySize: Yup.string().required(companySizeRequired).trim(),
});

export default function EditCompanyPage() {
  const router = useRouter();
  const { id } = useParams<{ id: string }>();
  const updatePartner = useUpdatePartnersById();
  const { data: partnerData, isLoading: isLoadingCompany } =
    usePartnersById(id);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const getCompanySize = useAdminValues({
    category: AdminValuesCategories?.companySize?.category,
  });
  const companySizeOptions =
    getCompanySize.data?.data?.data?.customValues || [];

  const industryQuery = useAdminValues({
    subcategory: AdminValuesCategories?.industryOptions.subcategories.ISIC_1,
  });
  const industryOptions = industryQuery.data?.data?.data?.customValues || [];

  const cityQuery = useAdminValues({
    subcategory: AdminValuesCategories?.cities.subcategories.Level_2,
  });

  const citiesOptions = cityQuery.data?.data?.data?.customValues || [];

  const regionsQuery = useAdminValues({
    subcategory: AdminValuesCategories?.cities.subcategories.Level_1,
  });

  const regionsOptions = regionsQuery.data?.data?.data?.customValues || [];

  const handleCancel = () => {
    setIsModalOpen(true);
  };

  const confirmCancel = () => {
    setIsModalOpen(false);
    router.push(`/dashboard/company`);
  };

  if (isLoadingCompany) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40">
        <Loader />
      </div>
    );
  }

  if (!partnerData) {
    return (
      <div className=" flex justify-center mx-auto text-center">
        Company not found
      </div>
    );
  }

  const initialValues: CompanyFormData = {
    name: partnerData.name || '',
    partnerId: partnerData.partnerId || '',
    logo: partnerData.logo || '',
    addressLine1: partnerData.addressLine1 || '',
    mailAddressLine1: partnerData.mailAddressLine1 || '',
    city: partnerData.city || '',
    state: partnerData.state || '',
    country: partnerData.country || '',
    postCode: partnerData.postCode || '',
    industry: partnerData.industry || '',
    companySize: partnerData.size || '',
  };

  const handleSubmit = async (values: CompanyFormData) => {
    try {
      await updatePartner.mutateAsync({
        id: id as string,
        updateData: {
          name: values.name,
          partnerId: values.partnerId,
          logo: values.logo,
          addressLine1: values.addressLine1,
          mailAddressLine1: values.mailAddressLine1,
          city: values.city,
          state: values.state,
          country: values.country,
          postCode: values.postCode,
          industry: values.industry,
          size: values.companySize,
        },
      });

      router.push(`/dashboard/company`);
    } catch (error) {
      console.error('Error updating company:', error);
    }
  };

  return (
    <div className="container max-w-3xl mx-auto py-8">
      <div className="mb-6">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: `Companies`,
              href: '/dashboard/company',
            },
            {
              label: `${partnerData.name}`,
              href: `/dashboard/company/edit/${id}`,
            },
          ]}
        />
      </div>
      <Card className=" mx-auto bg-white shadow-sm rounded-lg overflow-hidden">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({
            isSubmitting,
            setFieldValue,
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
          }) => (
            <Form>
              <div className="p-6">
                <Tabs defaultValue="details" className="w-full">
                  <TabsList className="grid grid-cols-2 max-w-[400px] gap-4 mb-6 bg-transparent">
                    <TabsTrigger
                      value="details"
                      className="px-6 text-[18px] font-medium leading-[26px]"
                    >
                      Company Details
                    </TabsTrigger>
                    <TabsTrigger
                      value="representatives"
                      className="px-6 text-[18px] font-medium leading-[26px]"
                    >
                      Representatives
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="details">
                    <CompanyDetailsForm
                      values={values}
                      errors={errors}
                      touched={touched}
                      handleChange={handleChange}
                      handleBlur={handleBlur}
                      setFieldValue={setFieldValue}
                      isSubmitting={isSubmitting}
                      handleCancel={handleCancel}
                      companySizeOptions={companySizeOptions}
                      industryOptions={industryOptions}
                      citiesOptions={citiesOptions}
                      regionsOptions={regionsOptions}
                      getCompanySize={getCompanySize}
                    />
                  </TabsContent>

                  <TabsContent value="representatives">
                    <div className="space-y-4">
                      <EditCompanyRepresentatives />
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </Form>
          )}
        </Formik>
      </Card>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure?</DialogTitle>
          </DialogHeader>
          <p>All unsaved changes will be lost.</p>
          <DialogFooter>
            <Button onClick={confirmCancel}>Yes</Button>
            <Button variant={'outline'} onClick={() => setIsModalOpen(false)}>
              No
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
