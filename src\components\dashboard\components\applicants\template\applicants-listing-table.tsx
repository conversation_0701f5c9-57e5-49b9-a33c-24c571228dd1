'use client';

import { useApplicants } from '@/hooks/useApplicant';
import ApplicantListingSkeleton from '@/components/dashboard/common/applicant-listing-skeleton';
import { PaginationControls } from '../../my-listings/components/template/pagination-controller';
import BulkActionsToolbar from '../components/bulkAction';
import Tabs from '../components/tabs';
import ApplicantTableData from './applicantListingTableData';
import { useRouter } from 'next/navigation';
import FilterPanel from '../components/filterPanel';

export default function ApplicantJobListingTable({ jobId }: { jobId: string }) {
  const router = useRouter();

  const {
    state,
    applicants,
    total,
    allApplicantIdsData,
    isLoading,
    isError,
    totalPages,
    getStatusCounts,
    updateState,
    handleSort,
    handleTabChange,
    handlePageChange,
    handleBulkAction,
    handleRemoveFromSelection,
    updateStatus,
    handleApplyFilters,
    handleResetFilters,
  } = useApplicants(jobId);

  const {
    selectedIds,
    activeTab,
    sortField,
    ascending,
    page,
    search,
    filters,
  } = state;

  if (isLoading) {
    return <ApplicantListingSkeleton />;
  }

  if (isError) {
    return <div>Error fetching applicants. Please try again later.</div>;
  }

  return (
    <div className="bg-white border border-neutral-200 rounded-lg p-4 space-y-4">
      <Tabs
        activeTab={activeTab}
        onTabChange={handleTabChange}
        counts={getStatusCounts()}
      />

      <FilterPanel
        search={search}
        filters={filters}
        hideJobTitleDropdown={true}
        onSearchChange={(value) => updateState({ search: value })}
        onApplyFilters={handleApplyFilters}
        onResetFilters={handleResetFilters}
      />

      {isLoading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-neutral-500">Loading applicants...</p>
        </div>
      ) : (
        <>
          <ApplicantTableData
            key={`${activeTab}-${page}`}
            applicants={applicants}
            selectedIds={selectedIds}
            sortField={sortField}
            ascending={ascending}
            handleSort={handleSort}
            onSelect={(ids) => updateState({ selectedIds: ids })}
            onRowClick={(id) =>
              router.push(`/dashboard/jobs-and-training/applicants/${id}`)
            }
            updateApplicantStatus={updateStatus}
            status={activeTab}
            totalApplicantsCount={total}
            allApplicantIds={allApplicantIdsData}
          />

          {total > 0 && (
            <PaginationControls
              page={page}
              totalPages={totalPages}
              handlePageChange={handlePageChange}
            />
          )}
        </>
      )}

      <BulkActionsToolbar
        selectedIds={selectedIds}
        applicants={applicants}
        activeTab={activeTab}
        onBulkAction={handleBulkAction}
        onClearSelection={() => updateState({ selectedIds: [] })}
        onRemoveFromSelection={handleRemoveFromSelection}
      />
    </div>
  );
}
