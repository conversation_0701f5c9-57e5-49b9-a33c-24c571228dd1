/* eslint-disable @typescript-eslint/no-explicit-any */
import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import type { ComponentType } from 'react';

interface WithPermissionProps {
  [key: string]: any;
}

/**
 * Higher-order component that restricts access to components based on permissions
 * @param WrappedComponent - Component to wrap with permission check
 * @param requiredPermission - Permission(s) required to access the component
 * @param options - Options for permission checking
 * @param FallbackComponent - Component to render if permission check fails
 */
export function withPermission<P extends WithPermissionProps>(
  WrappedComponent: ComponentType<P>,
  requiredPermission: Permission | Permission[],
  options?: PermissionCheckOptions,
  FallbackComponent?: ComponentType<P>
) {
  return function WithPermissionComponent(props: P) {
    const { checkPermission, isLoading } = usePermission();

    if (isLoading) {
      return (
        <div className="flex justify-center p-4">Loading permissions...</div>
      );
    }

    if (checkPermission(requiredPermission, options)) {
      return <WrappedComponent {...props} />;
    }

    if (FallbackComponent) {
      return <FallbackComponent {...props} />;
    }

    return null;
  };
}
