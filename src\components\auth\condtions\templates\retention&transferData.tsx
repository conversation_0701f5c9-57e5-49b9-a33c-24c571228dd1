import {
  RetentionPersonalData,
  TransferPersonalData,
} from '@/constants/privacyDefinition';
import React from 'react';

function RetentionTransferData() {
  return (
    <div>
      <h5 className="text-blue-300 text-[16px] font-medium mt-4">
        Retention of Your Personal Data
      </h5>

      <ul className="flex flex-col gap-3 mt-1">
        {RetentionPersonalData.map((item) => (
          <li
            key={item.par1}
            className="text-[16px] font-normal leading-6 text-neutral-900"
          >
            <p className="text-[16px] text-neutral-900">{item.par1}</p>
            {item.par2 && (
              <p className="text-[16px] text-neutral-900 mt-2">{item.par2}</p>
            )}
          </li>
        ))}
      </ul>

      <h5 className="text-blue-300 text-[16px] font-medium mt-4">
        Transfer of Your Personal Data
      </h5>

      <ul className="flex flex-col gap-3 mt-1">
        {TransferPersonalData.map((item) => (
          <li
            key={item.par1}
            className="text-[16px] font-normal leading-6 text-neutral-900"
          >
            <p className="text-[16px] text-neutral-900">{item.par1}</p>
            {item.par2 && (
              <p className="text-[16px] text-neutral-900 mt-2">{item.par2}</p>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default RetentionTransferData;
