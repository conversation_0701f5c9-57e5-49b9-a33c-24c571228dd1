import { useMutation, useQueryClient } from '@tanstack/react-query';
import jobService from '@/zustand/services/jobServices';
import type { Job } from '@/type/jobType';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { useJobStore } from '@/zustand/store/jobStore';

// Mutations
export const useAddJob = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotificationStore.getState();
  const { status, ascending, sortField } = useJobStore();

  return useMutation({
    mutationFn: (job: Job) => jobService.addJob(job),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['jobs', status, ascending, sortField],
      });
      queryClient.invalidateQueries({ queryKey: ['allJobs'] });
      showNotification('Job added successfully!', 'success');
    },
    onError: (error) => {
      console.error('Failed to add job', error);
      showNotification('Error adding job', 'error');
    },
  });
};

export const useUpdateJob = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotificationStore.getState();
  const { status, ascending, sortField } = useJobStore(); // Get current state

  return useMutation({
    mutationFn: ({ id, job }: { id: string; job: Partial<Job> }) =>
      jobService.updateJob(id, job),
    onSuccess: (updatedJob) => {
      // Update the specific job in the cache
      queryClient.setQueryData(['job', updatedJob.id], updatedJob);

      // Invalidate queries with the current status, ascending, and sortField
      queryClient.invalidateQueries({
        queryKey: ['jobs', status, ascending, sortField],
      });
      queryClient.invalidateQueries({ queryKey: ['allJobs'] });
      queryClient.invalidateQueries({ queryKey: ['userJobs'] });

      showNotification('Job updated successfully!', 'success');
    },
    onError: (error) => {
      console.error('Failed to update job', error);
      showNotification('Error updating job', 'error');
    },
  });
};

export const useUpdateJobStatus = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotificationStore.getState();
  const { status, ascending, sortField } = useJobStore(); // Get current state

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      jobService.updateJobStatus(id, status),
    onSuccess: (updatedJob) => {
      // Update the specific job in the cache
      queryClient.setQueryData(['job', updatedJob.id], updatedJob);

      // Invalidate queries with the current status, ascending, and sortField
      queryClient.invalidateQueries({
        queryKey: ['jobs', status, ascending, sortField],
      });
      queryClient.invalidateQueries({ queryKey: ['allJobs'] });
      queryClient.invalidateQueries({ queryKey: ['userJobs'] });

      showNotification('Job status updated successfully!', 'success');
    },
    onError: (error) => {
      console.error('Failed to update job status', error);
      showNotification('Error updating job status', 'error');
    },
  });
};

export const useDeleteJob = () => {
  const queryClient = useQueryClient();
  const { showNotification } = useNotificationStore.getState();
  const { status, ascending, sortField } = useJobStore(); // Get current state

  return useMutation({
    mutationFn: (id: string) => jobService.deleteJob(id),
    onSuccess: (_, id) => {
      // Remove the job from the cache
      queryClient.removeQueries({ queryKey: ['job', id] });

      // Invalidate queries with the current status, ascending, and sortField
      queryClient.invalidateQueries({
        queryKey: ['jobs', status, ascending, sortField],
      });
      queryClient.invalidateQueries({ queryKey: ['allJobs'] });
      queryClient.invalidateQueries({ queryKey: ['userJobs'] });

      showNotification('Job deleted successfully!', 'success');
    },
    onError: (error) => {
      console.error('Failed to delete job', error);
      showNotification('Error deleting job', 'error');
    },
  });
};
