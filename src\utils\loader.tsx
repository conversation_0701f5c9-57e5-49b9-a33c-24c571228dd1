'use client';

import { motion } from 'framer-motion';

function Loader() {
  return (
    <div className="fixed inset-0 flex justify-center items-center bg-black bg-opacity-70 z-50">
      <div className="flex space-x-4">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-4 h-20 bg-primary-500"
            initial={{ y: 0, opacity: 0.8 }}
            animate={{ y: [-10, 10, -10] }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
    </div>
  );
}

export default Loader;
