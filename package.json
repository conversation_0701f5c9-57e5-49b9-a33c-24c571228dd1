{"name": "careernavigatorproui-beta", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier \"src/**/*.{js,jsx,ts,tsx,json,css,md}\" --write", "postinstall": "if [ \"$NODE_ENV\" != \"production\" ]; then husky install; fi || echo 'Skipping Husky'"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --cache --fix", "prettier --write"]}, "dependencies": {"@headlessui/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.67.3", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "3.6.0", "docx-preview": "^0.3.5", "dompurify": "^3.2.5", "file-saver": "^2.0.5", "formik": "^2.4.6", "framer-motion": "^12.4.7", "jszip": "^3.10.1", "lucide-react": "^0.474.0", "next": "15.1.6", "react": "18", "react-confetti": "^6.2.2", "react-day-picker": "8.10.1", "react-dom": "18", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "recharts": "^2.15.0", "tailwind-merge": "^3.0.1", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@eslint/eslintrc": "^3.3.1", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.19.0", "eslint-config-next": "^15.3.2", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "npm@10.9.2"}