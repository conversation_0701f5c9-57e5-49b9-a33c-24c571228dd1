'use client';

import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import NewListing from '@/components/dashboard/components/posting-a-new-job/new-listing';
import React from 'react';

function Page() {
  return (
    <div className="">
      <div className="">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'Post a New Listing',
              href: '/dashboard/jobs-and-training/posting-a-new-listing',
            },
          ]}
        />
      </div>
      <div className="mx-auto"></div>
      <NewListing />
    </div>
  );
}

export default Page;
