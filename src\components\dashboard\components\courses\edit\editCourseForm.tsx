'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Loader2 } from 'lucide-react';
import { updateCourse } from '@/zustand/services/courseServices';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { CourseSummaryTab } from './courseSummaryTab';
import { CourseDetailsTab } from './courseDetailTab';
import { CourseSkillsTab } from './courseSkillTab';
import type { Course } from '@/types/courseType';
import { toast } from 'react-hot-toast';
import { UnsavedChangesDialog } from '../unsavedChanges';

interface CourseFormProps {
  initialCourse?: Course;
  mode?: 'create' | 'edit';
}

export const EditCourseForm: React.FC<CourseFormProps> = ({
  initialCourse,
  mode = 'create',
}) => {
  const router = useRouter();
  const userId = useAuthStore((state) => state.user?.id);
  const [activeTab, setActiveTab] = useState('course-summary');
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [showActiveConfirmDialog, setShowActiveConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<'back' | 'save' | null>(
    null
  );
  // const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const {
    course,
    updateCourse: updateStoreCourse,
    skillsCovered,
    isDirty,
    setIsDirty,
    resetForm,
    validateStep,
  } = useCourseFormStore();

  useEffect(() => {
    if (initialCourse) {
      updateStoreCourse(initialCourse);
      setIsDirty(false);
    } else {
      resetForm();
    }
  }, [initialCourse, updateStoreCourse, resetForm, setIsDirty]);

  const validateCurrentTab = (tab: string): boolean => {
    let step: 'summary' | 'details' | 'skills' = 'summary';

    if (tab === 'course-summary') step = 'summary';
    else if (tab === 'enrolment-details') step = 'details';
    else if (tab === 'skills') step = 'skills';

    const validation = validateStep(step);
    // setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleTabChange = (newTab: string) => {
    // Remove validation when switching tabs
    setActiveTab(newTab);
    // setValidationErrors([])
  };

  const handleCancel = () => {
    if (isDirty) {
      setShowUnsavedDialog(true);
      setPendingAction('back');
    } else {
      navigateBack();
    }
  };

  const navigateBack = () => {
    if (mode === 'edit' && initialCourse?.id) {
      router.push(`/dashboard/courses/${initialCourse.id}`);
    } else {
      router.push('/dashboard/courses');
    }
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    try {
      // Only validate the current tab
      if (!validateCurrentTab(activeTab)) {
        toast.error('Please fill all required fields before saving this tab');
        setIsSaving(false);
        return;
      }

      if (
        mode === 'edit' &&
        course.status === 'Active' &&
        !showActiveConfirmDialog
      ) {
        setShowActiveConfirmDialog(true);
        setPendingAction('save');
        return;
      }

      await saveTabChanges(activeTab);
    } finally {
      setIsSaving(false);
    }
  };

  const saveTabChanges = async (tab: string) => {
    if (!userId) {
      toast.error('User ID is missing. Please log in again.');
      return;
    }

    try {
      const courseData = {
        ...course,
        skillsCovered,
        lastModifiedById: userId,
      };

      if (mode === 'edit' && initialCourse?.id) {
        await updateCourse(initialCourse.id, courseData, userId);
        // toast.success(`${getTabName(tab)} saved successfully`)
        toast.success(`saved successfully`);
        setIsDirty(false);
        router.push(`/dashboard/courses/${initialCourse.id}`);
      } else {
        toast.success(`${getTabName(tab)} saved successfully`);
        toast.success(`saved successfully`);
        setIsDirty(false);
        router.push('/dashboard/courses');
      }
    } catch (error) {
      console.error('Error saving tab:', error);
      toast.error('Failed to save changes');
    } finally {
      setShowActiveConfirmDialog(false);
    }
  };

  const getTabName = (tab: string): string => {
    switch (tab) {
      case 'course-summary':
        return 'Course Summary';
      case 'enrolment-details':
        return 'Enrolment & Course Details';
      case 'skills':
        return 'Skills';
      default:
        return 'Tab';
    }
  };

  const handleDiscardChanges = () => {
    setShowUnsavedDialog(false);
    if (pendingAction === 'back') {
      navigateBack();
    }
    setPendingAction(null);
  };

  const handleKeepEditing = () => {
    setShowUnsavedDialog(false);
    setShowActiveConfirmDialog(false);
    setPendingAction(null);
  };

  const updateCourseField = <K extends keyof Course>(
    field: K,
    value: Course[K]
  ) => {
    updateStoreCourse({ [field]: value } as Partial<Course>);
  };

  const handleImageUpload = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('Failed to read file'));
        }
      };

      reader.onerror = () => {
        reject(new Error('FileReader error'));
      };

      reader.readAsDataURL(file);
    });
  };

  return (
    <div className="bg-white border rounded-lg shadow-sm p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-[28px] font-semibold leading-[36px] text-neutral-900">
          {mode === 'create' ? 'Add a New Course' : 'Edit Course'}
        </h1>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleSaveChanges}
            disabled={isSaving || (!isDirty && mode === 'edit')}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              `Save Changes`
            )}
          </Button>
        </div>
      </div>
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="bg-transparent w-[1000px]"
      >
        <TabsList className="grid grid-cols-3 gap-6 mb-6 max-w-5xl bg-transparent text-left">
          <TabsTrigger className="text-left" value="course-summary">
            Course Summary
          </TabsTrigger>

          <TabsTrigger className="text-left" value="enrolment-details">
            Enrolment & Course Details
          </TabsTrigger>

          <TabsTrigger
            className="text-left flex items-center gap-1"
            value="skills"
          >
            Skills{' '}
            <span className="bg-neutral-100 text-black rounded-full w-5 h-5 flex items-center justify-center text-[16px]">
              {course.skillsCovered.length || 0}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="course-summary">
          <CourseSummaryTab course={course} updateCourse={updateCourseField} />
        </TabsContent>

        <TabsContent value="enrolment-details">
          <CourseDetailsTab
            course={course}
            updateCourse={updateCourseField}
            onImageUpload={handleImageUpload}
          />
        </TabsContent>

        <TabsContent value="skills">
          <CourseSkillsTab />
        </TabsContent>
      </Tabs>

      <UnsavedChangesDialog
        isOpen={showUnsavedDialog}
        onClose={() => setShowUnsavedDialog(false)}
        onDiscard={handleDiscardChanges}
        onKeepEditing={handleKeepEditing}
      />

      <AlertDialog
        open={showActiveConfirmDialog}
        onOpenChange={setShowActiveConfirmDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Confirm Changes to Active Course
            </AlertDialogTitle>
            <AlertDialogDescription>
              This is an active course. Saved changes will be visible directly
              for citizens. Please confirm if you want to save changes.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="rounded-full"
              onClick={handleKeepEditing}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                pendingAction === 'save'
                  ? saveTabChanges(activeTab)
                  : navigateBack()
              }
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
