'use client';

import type { ReactNode } from 'react';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { usePermission } from '@/hooks/usePermission';

interface PermissionWrapperProps {
  permission: Permission | Permission[];
  children: ReactNode;
  fallback?: ReactNode;
  options?: PermissionCheckOptions;
  className?: string;
  loadingComponent?: ReactNode;
  showLoader?: boolean;
}

/**
 * Basic wrapper component that conditionally renders children based on permissions
 */
export function PermissionWrapper({
  permission,
  children,
  fallback = null,
  options,
  className,
  loadingComponent,
  showLoader = true,
}: PermissionWrapperProps) {
  const { checkPermission, isLoading } = usePermission();

  // Show loading state if permissions are still being fetched
  if (isLoading && showLoader) {
    return (
      <div className={className}>
        {loadingComponent || (
          <div className="flex items-center justify-center p-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
          </div>
        )}
      </div>
    );
  }

  // Render children if user has permission, otherwise render fallback
  const hasPermission = checkPermission(permission, options);

  return <div className={className}>{hasPermission ? children : fallback}</div>;
}
