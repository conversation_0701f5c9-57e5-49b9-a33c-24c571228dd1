/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { GlobeIcon } from '@/components/icons/globeIcon';
import useSettingsStore from '@/zustand/store/settingsStore';
import { InboxIcon } from '@/components/icons/inboxIcon';
import { dashboardSideBarRoutes } from '@/constants/navConfig';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { useTrainingProviderStore } from '@/zustand/store/trainerProvider';
import MyAccountDropdown from '../template/MyAccountDropdown';
import { usePartnerProfile } from '@/queries';
import Image from 'next/image';

interface TopBarProps {
  toggleActionsAlert?: () => void;
}
const TopNavigation: React.FC<TopBarProps> = ({ toggleActionsAlert }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useAuthStore();
  const { appearanceSettings } = useSettingsStore();
  const { isTrainer, checkTrainerStatus } = useTrainingProviderStore();
  const [isAccountDropdownOpen, setIsAccountDropdownOpen] = useState(false);
  const [openDropdownMenu, setOpenDropdownMenu] = useState<string | null>(null);
  const accountDropdownRef = useRef<HTMLDivElement>(null);
  const { data: partnerData } = usePartnerProfile();
  const company = partnerData?.data?.items[0] ?? [];
  const menuDropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const toggleAccountDropdown = () => {
    setIsAccountDropdownOpen((prev) => !prev);
  };

  useEffect(() => {
    if (user?.id) {
      checkTrainerStatus(user.id);
    } else {
      useTrainingProviderStore.getState().reset();
    }
  }, [user?.id, checkTrainerStatus]);

  const toggleMenuDropdown = (label: string) => {
    setOpenDropdownMenu((prev) => (prev === label ? null : label));
  };

  const isActive = (route: string) => pathname === route;

  const hasActiveChild = (item: any) => {
    if (!item.children) return false;
    return item.children.some((child: any) => isActive(child.route));
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close account dropdown if clicked outside
      if (
        accountDropdownRef.current &&
        !accountDropdownRef.current.contains(event.target as Node)
      ) {
        setIsAccountDropdownOpen(false);
      }
      let clickedOutsideAllMenus = true;
      Object.values(menuDropdownRefs.current).forEach((ref) => {
        if (ref && ref.contains(event.target as Node)) {
          clickedOutsideAllMenus = false;
        }
      });

      if (clickedOutsideAllMenus) {
        setOpenDropdownMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close all dropdowns when route changes
  useEffect(() => {
    setIsAccountDropdownOpen(false);
    setOpenDropdownMenu(null);
  }, [pathname]);

  const isBgWhite = ['#ffffff', '#fff', '#FFF', '#FFFFFF'].includes(
    appearanceSettings.navMenuColor
  );

  const filteredRoutes = dashboardSideBarRoutes.filter((route) => {
    if (route.label === 'Courses') {
      return isTrainer === true;
    }
    return true;
  });

  return (
    <div className="relative top-0 left-0 right-0 bg-[--navMenuColor] text-white z-50 shadow-md py-2 items-center">
      <div className="mx-auto px-4 items-center">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {filteredRoutes.map((item) => (
              <div
                key={item.label}
                ref={(el) => {
                  menuDropdownRefs.current[item.label] = el;
                }}
                className="relative"
              >
                <button
                  className={`flex items-center space-x-2 px-6 py-2 ${
                    isActive(item.route || '') || hasActiveChild(item)
                      ? `border-b-4 ${isBgWhite ? 'border-primary-500' : 'border-white'}`
                      : `${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'}`
                  }`}
                  onClick={() => {
                    if (item.children) {
                      toggleMenuDropdown(item.label);
                    } else {
                      router.push(item.route);
                      setOpenDropdownMenu(null);
                    }
                  }}
                >
                  <span
                    className={`text-[16px] ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
                  >
                    {item.label}
                  </span>
                  {item.children && (
                    <ChevronDown
                      className={`w-4 h-4 transition-transform ${isBgWhite ? '!text-primary-500' : '!text-white'} ${
                        openDropdownMenu === item.label ? 'rotate-180' : ''
                      }`}
                    />
                  )}
                </button>

                {item.children && openDropdownMenu === item.label && (
                  <div className="absolute left-0 mt-1 w-[322px] bg-[--navMenuColor] rounded-md shadow-lg py-1 text-black border border-[--navMenuColor] z-10 top-14">
                    {item.children.map((child) => (
                      <button
                        key={child.route}
                        onClick={() => {
                          router.push(child.route);
                          setOpenDropdownMenu(null);
                        }}
                        className={`block w-full text-left px-6 py-4 text-[16px] text-primary-100 space-y-4 ${isBgWhite ? 'text-primary-500' : 'text-white'} ${
                          isActive(child.route)
                            ? 'bg-transparent'
                            : `${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'}`
                        }`}
                      >
                        {child.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center space-x-4">
            <button
              onClick={toggleActionsAlert}
              className={`flex items-center space-x-1 px-[28px] py-[14px] h-[48px] cursor-not-allowed text-white opacity-60 border ${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'} rounded-full`}
            >
              <GlobeIcon
                fill={isBgWhite ? appearanceSettings.brandColor : 'white'}
              />
              <span
                className={`${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              >
                EN
              </span>
              <ChevronDown
                className={`w-4 h-4 ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              />
            </button>

            <button
              onClick={toggleActionsAlert}
              className={`flex items-center space-x-4 w-[150px] h-[48px] py-3.5  cursor-not-allowed text-white opacity-60 border border-white ${isBgWhite ? 'hover:bg-[#0000000d]' : 'hover:bg-[#ffffff0d]'} rounded-full justify-center`}
            >
              <div className=" block">
                <InboxIcon
                  fill={isBgWhite ? appearanceSettings.brandColor : 'white'}
                />
              </div>
              <span
                className={`text-[18px] items-stretch ${isBgWhite ? '!text-primary-500' : '!text-white'}`}
              >
                Inbox: 4
              </span>
            </button>

            <div className="relative" ref={accountDropdownRef}>
              <button
                onClick={toggleAccountDropdown}
                className={`flex items-center space-x-2 w-[220px] h-[48px] py-3.5 px-[28px] ${isBgWhite ? 'bg-primary-500' : 'bg-white'} text-[--navMenuColor]  rounded-full`}
              >
                {company?.partner?.logo ? (
                  <Image
                    src={company?.partner?.logo}
                    width={24}
                    height={24}
                    alt="Account"
                    className="rounded-md object-contain mr-2"
                  />
                ) : (
                  <div className="w-6 h-6 rounded-md bg-primary-500 !text-white flex items-center justify-center font-semibold text-xs mr-2">
                    {company?.partner?.name.charAt(0).toUpperCase()}
                  </div>
                )}
                <span className="!text-[--navMenuColor] text-[18px] truncate">
                  {company?.partner?.name || 'My Account'}
                </span>
                <ChevronDown
                  className={`w-5 h-5 transition-transform ${
                    isAccountDropdownOpen ? 'rotate-180' : ''
                  }`}
                />
              </button>

              {isAccountDropdownOpen && <MyAccountDropdown />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopNavigation;
