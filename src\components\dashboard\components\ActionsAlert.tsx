'use client';

import React, { useState } from 'react';
import { AlertIcon } from '@/assets';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export const ActionsAlert: React.FC = () => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(true);
  const router = useRouter();

  const hideDropdown = () => setIsDropdownVisible(false);

  return (
    <div className="w-fulltransition-all duration-300">
      {isDropdownVisible && (
        <div className="bg-[#FBF398] text-black px-4 py-6 gap-3 flex items-center text-[16px] font-medium w-full absolute top-0">
          <Image src={AlertIcon} alt="alertIcon" />
          <span>You have 4 pending actions.</span>

          <Button
            className="px-6 py-0 bg-transparent text-black border border-[#000] text-[16px] font-medium hover:bg-transparent"
            onClick={() => router.push('/dashboard/pending-actions')}
          >
            View
          </Button>

          <Button
            className="px-3 py-1 bg-transparent text-black text-[16px] font-medium hover:bg-transparent"
            onClick={hideDropdown}
          >
            Hide
          </Button>
        </div>
      )}
    </div>
  );
};
