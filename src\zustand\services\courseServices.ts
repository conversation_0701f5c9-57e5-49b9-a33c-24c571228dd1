import type {
  Course,
  CourseFilters,
  CourseListResponse,
  CourseRespones,
  EnrollmentFilters,
  EnrollmentListResponse,
  StatsResponse,
} from '@/types/courseType';
import axiosClient from '@/utils/axiosClient';

export const createCourse = async (course: Course): Promise<Course> => {
  const response = await axiosClient.post<Course>('/courses', course);
  return response.data;
};

export const getCourses = async (
  filters: CourseFilters = {},
  page = 1,
  pageSize = 10
): Promise<CourseListResponse> => {
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await axiosClient.get<CourseListResponse>(
    `/courses?${params.toString()}`
  );
  return response.data;
};

export const getCourseById = async (id: string): Promise<CourseRespones> => {
  const response = await axiosClient.get<CourseRespones>(`/courses/${id}`);
  return response.data;
};

export const updateCourse = async (
  id: string,
  course: Partial<Course>,
  lastModifiedBy: string
): Promise<Course> => {
  const updatedData = {
    ...course,
    lastModifiedById: lastModifiedBy,
  };

  const response = await axiosClient.put<Course>(`/courses/${id}`, updatedData);
  return response.data;
};

export const deleteCourse = async (id: string): Promise<void> => {
  await axiosClient.delete(`/courses/${id}`);
};

export const getCourseEnrollments = async (
  courseId: string,
  filters: EnrollmentFilters = {},
  page = 1,
  pageSize = 10
): Promise<EnrollmentListResponse> => {
  // Build query parameters
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());

  // Add filters to query params
  if (filters.search) params.append('search', filters.search);
  if (filters.enrolledFrom) params.append('enrolledFrom', filters.enrolledFrom);
  if (filters.enrolledTo) params.append('enrolledTo', filters.enrolledTo);
  if (filters.status && filters.status !== 'All')
    params.append('status', filters.status);
  if (filters.sortField) params.append('sortField', filters.sortField);
  if (filters.ascending !== undefined)
    params.append('ascending', filters.ascending.toString());

  try {
    const response = await axiosClient.get<EnrollmentListResponse>(
      `/courses/${courseId}/enrollments?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching course enrollments:', error);
    // Return empty data with the expected format if API fails
    return {
      success: false,
      message: 'Failed to fetch enrollments',
      data: [],
      total: 0,
      page,
      pageSize,
      totalPages: 0,
      statusCounts: {
        totalEnrolled: 0,
        completed: 0,
        notCompleted: 0,
      },
    };
  }
};

export const getStats = async (params: {
  createdById?: string;
}): Promise<StatsResponse> => {
  const response = await axiosClient.get<StatsResponse>('/courses/stats', {
    params,
  });
  return response.data;
};
