#!/usr/bin/env node

/**
 * Health check script for Docker container
 * Career Navigator Pro Partner UI
 */

const http = require('http');

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/api/health',
  method: 'GET',
  timeout: 5000
};

const healthCheck = () => {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200 && response.status === 'healthy') {
            console.log('Health check passed');
            console.log(`Status: ${response.status}`);
            console.log(`Uptime: ${response.uptime}s`);
            resolve(0);
          } else {
            console.log('Health check failed');
            console.log(`Status Code: ${res.statusCode}`);
            console.log(`Response: ${data}`);
            reject(1);
          }
        } catch (error) {
          console.log('Health check failed - Invalid JSON response');
          console.log(`Response: ${data}`);
          reject(1);
        }
      });
    });

    req.on('error', (error) => {
      console.log('Health check failed - Connection error');
      console.log(`Error: ${error.message}`);
      reject(1);
    });

    req.on('timeout', () => {
      console.log('Health check failed - Timeout');
      req.destroy();
      reject(1);
    });

    req.setTimeout(options.timeout);
    req.end();
  });
};

// Run health check
healthCheck()
  .then((code) => process.exit(code))
  .catch((code) => process.exit(code));
