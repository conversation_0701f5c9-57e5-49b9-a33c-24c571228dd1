'use client';

import { useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Applicant } from '@/type';
import applicantService from '@/zustand/services/applicantServices';
import { useApplicantStore } from '@/zustand/store/applicantStore';

interface ApplicantFilters {
  name?: string;
  position?: string;
  appliedOnFrom?: string;
  appliedOnTo?: string;
}

interface ApplicantResponse {
  data: Applicant[];
  total: number;
  statusCounts?: {
    Pending: number;
    Shortlisted: number;
    Contacted: number;
    Hired: number;
    Rejected: number;
  };
  jobTitles: string[]; // Fixed typo from jobTilte to jobTitles
}

export const useApplicantListing = (userId: string | null) => {
  const queryClient = useQueryClient();
  const { state, updateState, resetState } = useApplicantStore();

  const memoizedFilters = useMemo(
    () => ({
      name: state.filters.name,
      position: state.filters.position,
      appliedOnFrom: state.filters.appliedOnFrom,
      appliedOnTo: state.filters.appliedOnTo,
    }),
    [
      state.filters.name,
      state.filters.position,
      state.filters.appliedOnFrom,
      state.filters.appliedOnTo,
    ]
  );

  const queryKey = useMemo(
    () => [
      'applicants',
      userId,
      state.page,
      state.size,
      state.activeTab,
      state.search,
      memoizedFilters,
      state.sortField,
      state.ascending,
    ],
    [
      userId,
      state.page,
      state.size,
      state.activeTab,
      state.search,
      memoizedFilters,
      state.sortField,
      state.ascending,
    ]
  );

  const {
    data: applicantsData,
    isLoading,
    isError,
    error,
  } = useQuery<ApplicantResponse>({
    queryKey,
    queryFn: async () => {
      if (!userId) throw new Error('User ID is required');

      const result = await applicantService.getApplicants(
        state.page,
        state.size,
        state.sortField,
        state.ascending,
        state.activeTab,
        userId,
        state.search || undefined,
        true,
        memoizedFilters.position,
        memoizedFilters.appliedOnFrom,
        memoizedFilters.appliedOnTo
      );

      return {
        data: result.data,
        total: result.total,
        statusCounts: result.statusCounts,
        jobTitles: result.jobTitles || [], // Ensure jobTitles is always an array
      };
    },
    enabled: !!userId,
    staleTime: 1000 * 60 * 5, // 5 minutes stale time
  });

  const applicants = applicantsData?.data ?? [];
  const total = applicantsData?.total ?? 0;
  const jobTitles = applicantsData?.jobTitles ?? []; // Extracted jobTitles

  const allApplicantIdsQueryKey = useMemo(
    () => [
      'allApplicantIds',
      userId,
      state.activeTab,
      state.search,
      memoizedFilters,
    ],
    [userId, state.activeTab, state.search, memoizedFilters]
  );

  const { data: allApplicantIdsData } = useQuery<string[]>({
    queryKey: allApplicantIdsQueryKey,
    queryFn: async () => {
      if (!userId || total <= 0) return [];

      const result = await applicantService.getApplicants(
        1,
        total,
        state.sortField,
        state.ascending,
        state.activeTab,
        userId,
        state.search || undefined,
        true,
        memoizedFilters.position,
        memoizedFilters.appliedOnFrom,
        memoizedFilters.appliedOnTo
      );
      return result.data.map((applicant) => applicant.id);
    },
    enabled: total > 0 && !!userId,
    staleTime: 1000 * 60, // 1 minute stale time
  });

  const { mutateAsync: updateStatus } = useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: Applicant['status'];
    }) => {
      return applicantService.updateApplicant(id, { status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['applicants'] });
      queryClient.invalidateQueries({ queryKey: ['allApplicantIds'] });
      updateState({ selectedIds: [] });
    },
    onError: (error) => {
      console.error('Error updating applicant status:', error);
    },
  });

  const handleSort = useCallback(
    (field: string) => {
      updateState({
        sortField: field,
        ascending:
          state.sortField === field
            ? !state.ascending
            : ['startDate', 'expiryDate'].includes(field)
              ? false
              : true,
      });
    },
    [state.sortField, state.ascending, updateState]
  );

  const handleApplyFilters = useCallback(
    (newFilters: ApplicantFilters) => {
      updateState({
        filters: {
          name: newFilters.name || '',
          position: newFilters.position || '',
          appliedOnFrom: newFilters.appliedOnFrom || '',
          appliedOnTo: newFilters.appliedOnTo || '',
        },
        page: 1,
        selectedIds: [],
      });
    },
    [updateState]
  );

  const handleResetFilters = useCallback(() => {
    updateState({
      filters: {
        name: '',
        position: '',
        appliedOnFrom: '',
        appliedOnTo: '',
      },
      page: 1,
      selectedIds: [],
    });
  }, [updateState]);

  const getStatusCounts = useCallback(() => {
    return {
      Pending: applicantsData?.statusCounts?.Pending || 0,
      Shortlisted: applicantsData?.statusCounts?.Shortlisted || 0,
      Contacted: applicantsData?.statusCounts?.Contacted || 0,
      Hired: applicantsData?.statusCounts?.Hired || 0,
      Rejected: applicantsData?.statusCounts?.Rejected || 0,
    };
  }, [applicantsData?.statusCounts]);

  const handleTabChange = useCallback(
    (status: Applicant['status']) => {
      updateState({
        activeTab: status,
        page: 1,
        selectedIds: [],
      });
    },
    [updateState]
  );

  const totalPages = Math.ceil(total / state.size);

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage >= 1 && newPage <= totalPages) {
        updateState({ page: newPage });
      }
    },
    [totalPages, updateState]
  );

  const handleBulkAction = useCallback(
    async (action: string, ids: string[]) => {
      if (!ids.length) return;

      try {
        if (
          ['Shortlisted', 'Contacted', 'Hired', 'Rejected'].includes(action)
        ) {
          const batchSize = 50;
          for (let i = 0; i < ids.length; i += batchSize) {
            const batch = ids.slice(i, i + batchSize);
            await Promise.all(
              batch.map((id) =>
                updateStatus({ id, status: action as Applicant['status'] })
              )
            );
          }
        } else if (action === 'downloadCVs') {
          // eslint-disable-next-line no-console
          console.log('Downloading CVs for IDs:', ids);
        }
      } catch (error) {
        console.error('Error performing bulk action:', error);
      }
    },
    [updateStatus]
  );

  const handleRemoveFromSelection = useCallback(
    (idsToRemove: string[]) => {
      updateState({
        selectedIds: state.selectedIds.filter(
          (id) => !idsToRemove.includes(id)
        ),
      });
    },
    [state.selectedIds, updateState]
  );

  return {
    state: {
      applicants,
      total,
      isLoading,
      isError,
      error,
      allApplicantIdsData,
      statusCounts: getStatusCounts(),
      jobTitles,
      activeTab: state.activeTab,
      page: state.page,
      size: state.size,
      totalPages,
      sortField: state.sortField,
      ascending: state.ascending,
      search: state.search,
      filters: state.filters,
      selectedIds: state.selectedIds,
    },
    actions: {
      handleSort,
      handleApplyFilters,
      handleResetFilters,
      handleTabChange,
      handlePageChange,
      handleBulkAction,
      handleRemoveFromSelection,
      updateStatus,
      updateState,
      resetState,
    },
  };
};
