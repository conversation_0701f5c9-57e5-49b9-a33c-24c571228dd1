'use client';

import { useState, useEffect } from 'react';
import { Download, Loader2, Minus, XIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import type { Applicant } from '@/type';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { useUpdateJobStatus } from '@/mutations/useJobMutations';

interface BulkActionsToolbarProps {
  selectedIds: string[];
  applicants: Applicant[];
  activeTab: Applicant['status'];
  onBulkAction: (_action: string, _ids: string[]) => Promise<void>;
  onClearSelection: () => void;
  onRemoveFromSelection?: (_ids: string[]) => void;
}

export default function BulkActionsToolbar({
  selectedIds,
  applicants,
  activeTab,
  onBulkAction,
  onClearSelection,
  onRemoveFromSelection = (ids) => {
    if (ids.length === selectedIds.length) {
      onClearSelection();
    }
  },
}: BulkActionsToolbarProps) {
  const [showHireModal, setShowHireModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showContactedModal, setShowContactedModal] = useState(false);
  const [showShortlistModal, setShowShortlistModal] = useState(false);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [showRemoveModal, setShowRemoveModal] = useState(false);
  const [selectedToRemove, setSelectedToRemove] = useState<string[]>([]);
  const [prevActiveTab, setPrevActiveTab] =
    useState<Applicant['status']>(activeTab);
  const [hireFlowStep, setHireFlowStep] = useState(0);

  // Loading states
  const [isHireLoading, setIsHireLoading] = useState(false);
  const [isRejectLoading, setIsRejectLoading] = useState(false);
  const [isContactedLoading, setIsContactedLoading] = useState(false);
  const [isShortlistLoading, setIsShortlistLoading] = useState(false);
  const [isDownloadLoading, setIsDownloadLoading] = useState(false);
  const [isRemoveLoading, setIsRemoveLoading] = useState(false);

  const updateJobStatusMutation = useUpdateJobStatus();

  // Clear selection when tab changes
  useEffect(() => {
    if (prevActiveTab !== activeTab) {
      onClearSelection();
      setPrevActiveTab(activeTab);
    }
  }, [activeTab, prevActiveTab, onClearSelection]);

  // Get the selected applicants' data
  const selectedApplicants = applicants.filter((app) =>
    selectedIds.includes(app.id)
  );

  // Handle bulk download CVs
  const handleDownloadCVs = () => {
    setShowDownloadModal(true);
  };

  // Confirm and execute download
  const confirmDownloadCVs = async () => {
    setIsDownloadLoading(true);
    const zip = new JSZip();

    try {
      await Promise.all(
        selectedApplicants.map(async (applicant) => {
          if (applicant.cv) {
            const response = await fetch(applicant.cv.base64Content);
            const blob = await response.blob();
            zip.file(`${applicant.user?.userName || 'Unknown'}_CV.pdf`, blob);
          }
        })
      );

      const content = await zip.generateAsync({ type: 'blob' });
      saveAs(content, 'CVs.zip');
      setIsDownloadLoading(false);
      setShowDownloadModal(false);
    } catch (error) {
      console.error('Error downloading CVs:', error);
      setIsDownloadLoading(false);
    }
  };

  // Handle bulk shortlist
  const handleShortlist = () => {
    setShowShortlistModal(true);
  };

  // Confirm bulk shortlist
  const confirmBulkShortlist = async () => {
    setIsShortlistLoading(true);
    try {
      await onBulkAction('Shortlisted', selectedIds);
      setIsShortlistLoading(false);
      setShowShortlistModal(false);
    } catch (error) {
      console.error('Error shortlisting applicants:', error);
      setIsShortlistLoading(false);
    }
  };

  // Handle bulk mark as contacted
  const handleMarkAsContacted = () => {
    setShowContactedModal(true);
  };

  // Confirm bulk mark as contacted
  const confirmMarkAsContacted = async () => {
    setIsContactedLoading(true);
    try {
      await onBulkAction('Contacted', selectedIds);
      setIsContactedLoading(false);
      setShowContactedModal(false);
    } catch (error) {
      console.error('Error marking applicants as contacted:', error);
      setIsContactedLoading(false);
    }
  };

  // Handle bulk mark as hired
  const handleMarkAsHired = () => {
    setShowHireModal(true);
    setHireFlowStep(0);
  };

  // Handle hire flow steps
  const handleProceedToListingOptions = () => {
    setHireFlowStep(1);
  };

  const handleCloseListing = async () => {
    setIsHireLoading(true);
    try {
      // First mark all selected applicants as hired
      await onBulkAction('Hired', selectedIds);

      const jobIds = [
        ...new Set(
          selectedApplicants
            .filter((app) => app.job?.id)
            .map((app) => app.job?.id)
        ),
      ];

      for (const jobId of jobIds) {
        if (jobId) {
          try {
            await updateJobStatusMutation.mutateAsync({
              id: jobId,
              status: 'Closed',
            });
          } catch (error) {
            console.error(`Failed to close job listing ${jobId}:`, error);
          }
        }
      }

      setIsHireLoading(false);
      setShowHireModal(false);
    } catch (error) {
      console.error('Failed to process hire and close listing:', error);
      setIsHireLoading(false);
    }
  };

  const handleKeepListingOpen = async () => {
    setIsHireLoading(true);
    try {
      await onBulkAction('Hired', selectedIds);
      setIsHireLoading(false);
      setShowHireModal(false);
    } catch (error) {
      console.error('Error marking applicants as hired:', error);
      setIsHireLoading(false);
    }
  };

  const handleConfirmHireFlow = async () => {
    setIsHireLoading(true);
    try {
      await onBulkAction('Hired', selectedIds);
      setIsHireLoading(false);
      setShowHireModal(false);
    } catch (error) {
      console.error('Error completing hire flow:', error);
      setIsHireLoading(false);
    }
  };

  const closeHireFlow = () => {
    setShowHireModal(false);
    setHireFlowStep(0);
  };

  // Handle bulk reject
  const handleReject = () => {
    setShowRejectModal(true);
  };

  // Confirm bulk reject
  const confirmBulkReject = async () => {
    setIsRejectLoading(true);
    try {
      await onBulkAction('Rejected', selectedIds);
      setIsRejectLoading(false);
      setShowRejectModal(false);
    } catch (error) {
      console.error('Error rejecting applicants:', error);
      setIsRejectLoading(false);
    }
  };

  // Handle remove from selection
  const handleRemoveFromSelection = () => {
    setSelectedToRemove([...selectedIds]);
    setShowRemoveModal(true);
  };

  // Confirm remove from selection
  const confirmRemoveFromSelection = () => {
    setIsRemoveLoading(true);
    try {
      onRemoveFromSelection(selectedToRemove);
      setIsRemoveLoading(false);
      setShowRemoveModal(false);
      setSelectedToRemove([]);
    } catch (error) {
      console.error('Error removing from selection:', error);
      setIsRemoveLoading(false);
    }
  };

  // Toggle selection of an applicant to remove
  const toggleRemoveSelection = (id: string) => {
    if (selectedToRemove.includes(id)) {
      setSelectedToRemove(selectedToRemove.filter((appId) => appId !== id));
    } else {
      setSelectedToRemove([...selectedToRemove, id]);
    }
  };

  const renderActions = () => {
    const actions = [];

    switch (activeTab) {
      case 'Pending':
        actions.push(
          <Button key="download" onClick={handleDownloadCVs}>
            <Download className="w-4 h-4 mr-2" />
            Download CVs
          </Button>,
          <Button key="shortlist" onClick={handleShortlist}>
            Shortlist
          </Button>,
          <Button key="contacted" onClick={handleMarkAsContacted}>
            Mark as Contacted
          </Button>,
          <Button
            key="reject"
            className="bg-destructive-500 hover:bg-destructive-600 text-white "
            onClick={handleReject}
          >
            <XIcon className="w-4 h-4 mr-1" />
            Reject
          </Button>
        );
        break;
      case 'Shortlisted':
        actions.push(
          <Button key="download" onClick={handleDownloadCVs}>
            <Download className="w-4 h-4 mr-2" />
            Download CVs
          </Button>,
          <Button key="contacted" onClick={handleMarkAsContacted}>
            Mark as Contacted
          </Button>,
          <Button key="hired" onClick={handleMarkAsHired}>
            Mark as Hired
          </Button>,
          <Button
            key="reject"
            className="bg-destructive-500 hover:bg-destructive-600 text-white"
            onClick={handleReject}
          >
            <XIcon className="w-4 h-4 mr-1" />
            Reject
          </Button>
        );
        break;
      case 'Contacted':
        actions.push(
          <Button key="download" onClick={handleDownloadCVs}>
            <Download className="w-4 h-4 mr-2" />
            Download CVs
          </Button>,
          <Button key="hired" onClick={handleMarkAsHired}>
            Mark as Hired
          </Button>,
          <Button
            key="reject"
            className="bg-destructive-500 hover:bg-destructive-600 text-white"
            onClick={handleReject}
          >
            <XIcon className="w-4 h-4 mr-1" />
            Reject
          </Button>
        );
        break;
      case 'Rejected':
      case 'Hired':
        actions.push(
          <Button key="download" onClick={handleDownloadCVs}>
            <Download className="w-4 h-4 mr-2" />
            Download CVs
          </Button>
        );
        break;
      default:
        break;
    }

    return actions;
  };

  if (
    selectedIds.length === 0 ||
    (activeTab === 'Rejected' && !renderActions().length) ||
    (activeTab === 'Hired' && !renderActions().length)
  ) {
    return null;
  }

  return (
    <>
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 bg-[#0A1929] text-white rounded-md shadow-lg flex items-center py-2 px-4 gap-3">
        <Button className="h-5 w-5" onClick={handleRemoveFromSelection}>
          <Minus className="w-4 h-4" />
        </Button>
        <span className="text-white font-medium">
          {selectedIds.length}{' '}
          {selectedIds.length === 1 ? 'applicant' : 'applicants'} selected
          {/* {selectedIds.length > applicants.length && (
            <span className="ml-1 text-xs">(across all pages)</span>
          )} */}
        </span>
        <div className="flex items-center gap-3">{renderActions()}</div>
      </div>

      {/* Remove from selection modal */}
      <Dialog open={showRemoveModal} onOpenChange={setShowRemoveModal}>
        <DialogContent className="max-w-md">
          <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Remove from Selection
          </h2>
          <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Select applicants to remove from your current selection:
          </p>
          <div className="max-h-[300px] overflow-y-auto p-2 my-3">
            <ul className="space-y-2">
              {selectedApplicants.map((app) => (
                <li key={app.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`remove-${app.id}`}
                    checked={selectedToRemove.includes(app.id)}
                    onChange={() => toggleRemoveSelection(app.id)}
                    className="mr-2 h-4 w-4"
                  />
                  <label
                    htmlFor={`remove-${app.id}`}
                    className="font-medium text-neutral-900"
                  >
                    {app.user?.userName || 'N/A'}
                  </label>
                </li>
              ))}
            </ul>
          </div>
          <div className="flex gap-2 justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => {
                setShowRemoveModal(false);
                setSelectedToRemove([]);
              }}
              disabled={isRemoveLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmRemoveFromSelection}
              disabled={isRemoveLoading || selectedToRemove.length === 0}
            >
              {isRemoveLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removing...
                </>
              ) : (
                `Remove ${selectedToRemove.length} ${selectedToRemove.length === 1 ? 'Applicant' : 'Applicants'}`
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Download CVs modal */}
      <Dialog open={showDownloadModal} onOpenChange={setShowDownloadModal}>
        <DialogContent>
          <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Download CVs
          </h2>
          <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Are you sure you want to download CVs for {selectedIds.length}{' '}
            {selectedIds.length === 1 ? 'applicant' : 'applicants'}?
          </p>
          <div className="max-h-[200px] overflow-y-auto p-2 my-3">
            {selectedIds.length > applicants.length ? (
              <p className="text-neutral-700 font-medium">
                All {selectedIds.length} applicants selected across all pages
              </p>
            ) : (
              <ul>
                {selectedApplicants.map((app) => (
                  <li
                    key={app.id}
                    className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8"
                  >
                    {app.user?.userName || 'N/A'}
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div className="flex gap-2 justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setShowDownloadModal(false)}
              disabled={isDownloadLoading}
            >
              Cancel
            </Button>
            <Button onClick={confirmDownloadCVs} disabled={isDownloadLoading}>
              {isDownloadLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Downloading...
                </>
              ) : (
                'Download CVs'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Shortlist modal */}
      <Dialog open={showShortlistModal} onOpenChange={setShowShortlistModal}>
        <DialogContent>
          <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Confirm Shortlist
          </h2>
          <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Are you sure you want to shortlist {selectedIds.length}{' '}
            {selectedIds.length === 1 ? 'applicant' : 'applicants'}?
          </p>
          <div className="max-h-[200px] overflow-y-auto p-2 my-3">
            {selectedIds.length > applicants.length ? (
              <p className="text-neutral-700 font-medium">
                All {selectedIds.length} applicants selected across all pages
              </p>
            ) : (
              <ul>
                {selectedApplicants.map((app) => (
                  <li
                    key={app.id}
                    className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8"
                  >
                    {app.user?.userName || 'N/A'}
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div className="flex gap-2 justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setShowShortlistModal(false)}
              disabled={isShortlistLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmBulkShortlist}
              disabled={isShortlistLoading}
            >
              {isShortlistLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Confirm Shortlist'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Contacted modal */}
      <Dialog open={showContactedModal} onOpenChange={setShowContactedModal}>
        <DialogContent>
          <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Mark as Contacted
          </h2>
          <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Are you sure you want to mark {selectedIds.length}{' '}
            {selectedIds.length === 1 ? 'applicant' : 'applicants'} as
            contacted?
          </p>
          <div className="max-h-[200px] overflow-y-auto p-2 my-3">
            {selectedIds.length > applicants.length ? (
              <p className="text-neutral-700 font-medium">
                All {selectedIds.length} applicants selected across all pages
              </p>
            ) : (
              <ul>
                {selectedApplicants.map((app) => (
                  <li
                    key={app.id}
                    className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8"
                  >
                    {app.user?.userName || 'N/A'}
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div className="flex gap-2 justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setShowContactedModal(false)}
              disabled={isContactedLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmMarkAsContacted}
              disabled={isContactedLoading}
            >
              {isContactedLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Mark as Contacted'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Hire modal with multi-step flow */}
      <Dialog open={showHireModal} onOpenChange={setShowHireModal}>
        <DialogContent>
          {hireFlowStep === 0 && (
            <div className="space-y-4">
              <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
                Mark as Hired
              </h2>
              <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
                Are you sure you want to mark {selectedIds.length}{' '}
                {selectedIds.length === 1 ? 'applicant' : 'applicants'} as
                hired?
              </p>
              <div className="max-h-[200px] overflow-y-auto p-2 my-3">
                {selectedIds.length > applicants.length ? (
                  <p className="text-neutral-700 font-medium">
                    All {selectedIds.length} applicants selected across all
                    pages
                  </p>
                ) : (
                  <ul>
                    {selectedApplicants.map((app) => (
                      <li
                        key={app.id}
                        className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8"
                      >
                        {app.user?.userName || 'N/A'}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={closeHireFlow}>
                  Cancel
                </Button>
                <Button onClick={handleProceedToListingOptions}>
                  Continue
                </Button>
              </div>
            </div>
          )}

          {hireFlowStep === 1 && (
            <div className="space-y-4">
              <h2 className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
                Close Listing?
              </h2>
              <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
                Would you also like to close this listing?
              </p>
              <p className="text-neutral-500 text-[18px] font-normal leading-[28px]">
                Keep this listing open if you would like to continue hiring for
                this role.
              </p>
              <div className="flex flex-col space-y-3">
                <Button onClick={handleCloseListing}>Close Listing</Button>
                <Button
                  variant="outline"
                  onClick={handleKeepListingOpen}
                  disabled={isHireLoading}
                >
                  {isHireLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Keep Listing Open'
                  )}
                </Button>
              </div>
            </div>
          )}

          {hireFlowStep === 2 && (
            <div className="space-y-4">
              <h2 className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
                Confirm Close
              </h2>
              <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
                Are you sure you want to close this listing?
              </p>
              <div className="flex gap-2 justify-end">
                <Button
                  variant="outline"
                  onClick={() => setHireFlowStep(1)}
                  disabled={isHireLoading}
                >
                  Back
                </Button>
                <Button
                  onClick={handleConfirmHireFlow}
                  disabled={isHireLoading}
                >
                  {isHireLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Confirm Close'
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Reject modal */}
      <Dialog open={showRejectModal} onOpenChange={setShowRejectModal}>
        <DialogContent>
          <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Confirm Rejection
          </h2>
          <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Are you sure you want to reject {selectedIds.length}{' '}
            {selectedIds.length === 1 ? 'applicant' : 'applicants'}?
          </p>
          <div className="max-h-[200px] overflow-y-auto p-2 my-3">
            {selectedIds.length > applicants.length ? (
              <p className="text-neutral-700 font-medium">
                All {selectedIds.length} applicants selected across all pages
              </p>
            ) : (
              <ul>
                {selectedApplicants.map((app) => (
                  <li
                    key={app.id}
                    className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8"
                  >
                    {app.user?.userName || 'N/A'}
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div className="flex gap-2 justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setShowRejectModal(false)}
              disabled={isRejectLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmBulkReject}
              className="bg-destructive-500 text-white hover:bg-destructive-600"
              disabled={isRejectLoading}
            >
              {isRejectLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rejecting...
                </>
              ) : (
                'Confirm Reject'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
