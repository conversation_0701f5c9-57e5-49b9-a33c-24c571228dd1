/* eslint-disable no-unused-vars */
'use client';

import type { ReactNode } from 'react';
import { useState } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { usePermission } from '@/hooks/usePermission';

interface PermissionTab {
  id: string;
  label: string;
  permission: Permission | Permission[];
  permissionOptions?: PermissionCheckOptions;
  content: ReactNode;
  icon?: ReactNode;
}

interface PermissionTabsProps {
  tabs: PermissionTab[];
  defaultTab?: string;
  className?: string;
  onTabChange?: (tabId: string) => void;
}

/**
 * Tabs component that filters tabs based on user permissions
 */
export function PermissionTabs({
  tabs,
  defaultTab,
  className = '',
  onTabChange,
}: PermissionTabsProps) {
  const { checkPermission, isLoading } = usePermission();
  const [activeTab, setActiveTab] = useState<string>('');

  // Filter tabs based on permissions
  const visibleTabs = tabs.filter((tab) =>
    checkPermission(tab.permission, tab.permissionOptions)
  );

  // Set default tab to first visible tab if not specified or if specified tab is not visible
  const effectiveDefaultTab = (() => {
    if (defaultTab && visibleTabs.some((tab) => tab.id === defaultTab)) {
      return defaultTab;
    }
    return visibleTabs[0]?.id || '';
  })();

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="flex space-x-4 mb-4">
          {tabs.map((tab) => (
            <div
              key={tab.id}
              className="h-8 w-20 bg-gray-200 rounded animate-pulse"
            ></div>
          ))}
        </div>
        <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }
  if (visibleTabs.length === 0) {
    return (
      <div className={`${className} text-center py-8`}>
        <p className="text-gray-500">No accessible tabs available.</p>
      </div>
    );
  }

  return (
    <Tabs
      value={activeTab || effectiveDefaultTab}
      onValueChange={handleTabChange}
      className={className}
    >
      <TabsList>
        {visibleTabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className="flex items-center"
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>

      {visibleTabs.map((tab) => (
        <TabsContent key={tab.id} value={tab.id}>
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
}
