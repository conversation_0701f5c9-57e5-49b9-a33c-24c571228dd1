'use client';

import { TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ChevronDown, ChevronUp, ChevronsUpDown } from 'lucide-react';

type SortableHeaderProps = {
  title: string;
  field: string;
  sortField: string | null;
  ascending: boolean;
  onSort: (_field: string) => void;
};

function SortableHeader({
  title,
  field,
  sortField,
  ascending,
  onSort,
}: SortableHeaderProps) {
  return (
    <TableHead className="text-neutral-700 text-[16px] leading-[24px] font-semibold">
      <button
        className="flex items-center gap-1 w-full"
        onClick={() => onSort(field)}
      >
        {title}
        {sortField === field ? (
          <span>
            {ascending ? (
              <ChevronUp className="w-4 h-4 !text-[--bodyTextColor]" />
            ) : (
              <ChevronDown className="w-4 h-4 !text-[--bodyTextColor]" />
            )}
          </span>
        ) : (
          <ChevronsUpDown className="w-4 h-4 !text-[--bodyTextColor]" />
        )}
      </button>
    </TableHead>
  );
}

type TableHeadersProps = {
  sortField: string | null;
  ascending: boolean;
  handleSort: (_field: string) => void;
};

export function JobTableHeaders({
  sortField,
  ascending,
  handleSort,
}: TableHeadersProps) {
  return (
    <TableHeader className="bg-neutral-50 rounded border-0">
      <TableRow>
        <SortableHeader
          title="Position"
          field="title"
          sortField={sortField}
          ascending={ascending}
          onSort={handleSort}
        />
        <SortableHeader
          title="Listing Type"
          field="ListingType"
          sortField={sortField}
          ascending={ascending}
          onSort={handleSort}
        />
        <SortableHeader
          title="Applicants"
          field="applicants"
          sortField={sortField}
          ascending={ascending}
          onSort={handleSort}
        />
        <SortableHeader
          title="Created On"
          field="postedDate"
          sortField={sortField}
          ascending={ascending}
          onSort={handleSort}
        />
        <SortableHeader
          title="Close Date"
          field="expiryDate"
          sortField={sortField}
          ascending={ascending}
          onSort={handleSort}
        />
      </TableRow>
    </TableHeader>
  );
}
