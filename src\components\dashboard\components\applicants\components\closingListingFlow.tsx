'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';

interface CloseListingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  jobTitle: string;
  onConfirmClose: () => Promise<void>;
}

export default function CloseListingFlow({
  isOpen,
  onClose,
  jobTitle,
  onConfirmClose,
}: CloseListingFlowProps) {
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Step 1: Confirm closing the listing
  const handleCloseListing = () => {
    setStep(1);
  };

  // Step 2: Final confirmation to close
  const handleConfirmCloseListing = async () => {
    setIsLoading(true);
    try {
      await onConfirmClose();
      setIsLoading(false);
      onClose();
      setStep(0);
    } catch (error) {
      console.error('Error closing listing:', error);
      setIsLoading(false);
    }
  };

  // Reset step when dialog closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      setStep(0);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        {step === 0 && (
          <div className="space-y-4">
            <h2 className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
              Close Listing?
            </h2>
            <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
              Would you like to close this listing?
            </p>
            <ul>
              <li className="list-disc font-semibold leading-[28px] text-neutral-700 text-[18px] ml-8">
                {jobTitle || 'Unknown Position'}
              </li>
            </ul>
            <p className="text-neutral-500 text-[18px] font-normal leading-[28px]">
              Keep this listing open if you would like to continue hiring for
              this role.
            </p>
            <div className="flex flex-col space-y-3">
              <Button onClick={handleCloseListing}>Close Listing</Button>
              <Button variant="outline" onClick={onClose}>
                Keep Listing Open
              </Button>
            </div>
          </div>
        )}

        {step === 1 && (
          <div className="space-y-4">
            <h2 className="text-[16px] font-semibold">Confirm Close</h2>
            <p>Are you sure you want to close this listing?</p>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button onClick={handleConfirmCloseListing} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Closing...
                  </>
                ) : (
                  'Confirm Close listing'
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
