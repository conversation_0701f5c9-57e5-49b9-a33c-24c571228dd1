'use client';

import { useMemo, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { RxCaretLeft } from 'react-icons/rx';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import { Skeleton } from '@/components/ui/skeleton';
import ApplicantTable from '@/components/dashboard/components/applicants/template/applicants-listing-table';
import Image from 'next/image';
import EditICon from '@/assets/images/EditIcon.svg';
import { X } from 'lucide-react';
import { modals } from '@/components/modals/closing_listing';
import { useJobById } from '@/queries/useJobQueries';
// import { useUpdateJobStatus } from '@/mutations/useJobMutations';
import { normalizeText, sanitizeHtml } from '@/utils';

export default function JobDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [isFirstModalOpen, setIsFirstModalOpen] = useState(false);
  // const [isSecondModalOpen, setIsSecondModalOpen] = useState(false);

  // Use TanStack Query hooks
  const { data: currentJob, isLoading } = useJobById(id as string);
  // const updateJobStatusMutation = useUpdateJobStatus();

  // const handleCloseListing = async () => {
  //   if (!currentJob?.id || isLoading) return

  //   try {
  //     await updateJobStatusMutation.mutateAsync({
  //       id: currentJob.id,
  //       status: 'Closed',
  //     })

  //     setIsSecondModalOpen(false)

  //     router.replace('/dashboard/jobs-and-training/my-listings')
  //   } catch (error) {
  //     console.error('Failed to close listing:', error)
  //   }
  // }

  const handleFirstModalConfirm = async () => {
    setIsFirstModalOpen(false);
    // setIsSecondModalOpen(true);
  };

  const formatPostedDate = (postedDate?: string) => {
    if (!postedDate) return 'Posted date not available';

    const postedTime = new Date(postedDate).getTime();
    const now = Date.now();

    const diffInMs = now - postedTime;
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays >= 1) {
      return `Posted ${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(
      (diffInMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
    );

    if (diffInHours >= 1) {
      return `Posted ${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    return 'Posted today';
  };

  const formatSalary = useMemo(
    () =>
      (from?: number, to?: number, currency = '$') => {
        const formatNumber = (num: number) => num.toLocaleString();

        if (!from && !to) return 'Salary not specified';
        if (from && to)
          return `${currency} ${formatNumber(from)} - ${formatNumber(to)} per month`;
        return from
          ? `${currency}${formatNumber(from)} per month`
          : `Up to ${currency} ${formatNumber(to || 0)} per month`;
      },
    []
  );

  if (isLoading) {
    return (
      <div className="p-6 max-w-5xl mx-auto space-y-6 bg-white shadow-lg rounded-lg mt-10">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-4 w-1/3" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>
    );
  }

  if (!currentJob) {
    return (
      <div className="p-6 max-w-2xl mx-auto space-y-6 bg-white shadow-lg rounded-lg mt-10">
        <h1 className="text-2xl font-bold">Job not found</h1>
        <p>The job you`re looking for doesn`t exist or has been removed.</p>
        <Button
          onClick={() =>
            router.push('/dashboard/jobs-and-training/posting-a-new-listing')
          }
        >
          Back to Listings
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="mx-auto">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'My Listings',
              href: `/dashboard/jobs-and-training/my-listings`,
            },
            {
              label: currentJob.title,
              href: `/dashboard/jobs-and-training/my-listings/${currentJob.id}`,
            },
          ]}
        />
        <Button
          onClick={() => router.back()}
          variant="outline"
          className="py-6 px-6 my-6 text-[16px]"
        >
          <RxCaretLeft className="w-16 h-16" />
          Back
        </Button>
      </div>
      <div className="">
        <h2 className="font-semibold text-[32px] text-neutral-900">
          Job Details
        </h2>
        <div className="p-6 mx-auto space-y-6 bg-white shadow-lg rounded-lg">
          {/* Job Header */}
          <div className="">
            <div className="flex justify-between border-b pb-2 border-neutral-50">
              <div>
                <h1 className="text-[20px] font-semibold">
                  {currentJob.title}
                </h1>
                <p className="text-neutral-500">
                  {currentJob.companyName || 'N/A'} |{' '}
                  {currentJob.location || 'N/A'}
                </p>
                <p className="text-neutral-500">
                  {formatPostedDate(currentJob.startDate)}
                </p>
              </div>
              {currentJob?.status !== 'Closed' && (
                <div className="flex gap-4">
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => setIsFirstModalOpen(true)}
                  >
                    <X className="w-5 h-5" />
                    Close Listing
                  </Button>

                  {/* First Modal */}
                  <modals.CloseListingModal
                    isOpen={isFirstModalOpen}
                    onClose={() => setIsFirstModalOpen(false)}
                    onConfirm={handleFirstModalConfirm}
                    jobId={id}
                  />

                  <Button
                    variant="default"
                    className="mt-4"
                    onClick={() =>
                      router.push(
                        `/dashboard/jobs-and-training/edit-listing/${currentJob.id}`
                      )
                    }
                  >
                    <Image src={EditICon} alt="Edit" width={20} height={20} />
                    Edit Listing
                  </Button>
                </div>
              )}
            </div>

            <div className="py-4">
              <h2 className="mb-2 text-[16px] leading-[24px] font-semibold">
                Job Type{' '}
              </h2>
              <div className="flex gap-2">
                <h6 className="text-[16px] font-medium leading-[16px] text-[#111827] bg-[#E2E4E9] py-1 justify-center items-center px-2 rounded-md capitalize">
                  {normalizeText(currentJob.jobType || 'Not Specified')}
                </h6>

                <h6 className="text-[16px] font-medium leading-[16px] text-[#111827] bg-[#E2E4E9] py-1 justify-center items-center px-2 rounded-md capitalize">
                  {currentJob.jobMode}
                </h6>
              </div>
            </div>

            <div>
              <h2 className="mb-2 text-[16px] leading-[24px] font-semibold">
                Minimium Salary
              </h2>
              <div className="flex gap-9">
                <h6 className="text-[16px] font-medium leading-[16px] text-[#111827] bg-[#E2E4E9] py-1 justify-center items-center px-2 rounded-md">
                  {formatSalary(currentJob.monthlyFrom)}
                </h6>
              </div>
            </div>

            <div className="border-b pb-2 border-neutral-50">
              <h2 className="my-2 text-[16px] leading-[24px] font-semibold">
                Starting Date
              </h2>
              <p className="text-[16px] text-neutral-500">
                {currentJob.expiryDate
                  ? new Date(currentJob.expiryDate).toLocaleDateString(
                      'en-GB',
                      {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric',
                      }
                    )
                  : 'No date available'}
              </p>

              <h2 className="my-2 text-[16px] leading-[24px] font-semibold">
                Job Position (ISCO Code)
              </h2>
              <p className="text-[16px] text-neutral-500 capitalize">
                {/* {currentJob.jobPosition} */}
                {normalizeText(currentJob.jobPosition || 'Not Specified')}
              </p>
            </div>
          </div>

          <div>
            <div className="">
              <h4 className="text-[20px] font-semibold leading-[24px] text-neutral-900 mb-2">
                Details
              </h4>

              {currentJob.description ? (
                <div
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: sanitizeHtml(currentJob.description),
                  }}
                />
              ) : (
                <p className="text-[16px] font-normal leading-[24px] text-[#646A77]">
                  No detailed responsibilities provided
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mx-auto mt-10">
        <ApplicantTable jobId={id} />
      </div>
    </>
  );
}
