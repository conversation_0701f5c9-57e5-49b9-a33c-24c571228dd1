import React from 'react';

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
}) => {
  return (
    <div className={`${subtitle ? 'py-4' : 'py-2'}`}>
      <h2 className="text-xl font-semibold text-neutral-900">{title}</h2>
      {subtitle && (
        <p className="text-neutral-700 text-[16px] font-normal">{subtitle}</p>
      )}
    </div>
  );
};
