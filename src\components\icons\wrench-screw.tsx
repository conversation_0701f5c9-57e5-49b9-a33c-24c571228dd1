import type * as React from 'react';

// interface WrenchScrewdriverProps extends React.SVGProps<SVGSVGElement> {
//   width?: number | string
//   height?: number | string
// }

function WrenchIcon() {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 48 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22.8387 31.1387L34.5 42.8C36.5711 44.8711 39.9289 44.8711 42 42.8C44.0711 40.7289 44.0711 37.3711 42 35.3L30.2466 23.5466M22.8387 31.1387L27.831 25.0767C28.463 24.3092 29.3083 23.8264 30.2466 23.5466M22.8387 31.1387L13.5286 42.4438C12.5607 43.6191 11.1179 44.3 9.59537 44.3C6.78128 44.3 4.5 42.0187 4.5 39.2046C4.5 37.6821 5.18089 36.2393 6.35621 35.2714L20.0292 24.0112M30.2466 23.5466C31.3454 23.2189 32.5716 23.1696 33.7318 23.2677C33.985 23.2891 34.2412 23.3 34.5 23.3C39.4706 23.3 43.5 19.2706 43.5 14.3C43.5 12.9795 43.2156 11.7254 42.7047 10.5956L36.1523 17.148C33.921 16.6357 32.1646 14.8793 31.6523 12.648L38.2048 6.09549C37.0749 5.58449 35.8207 5.30002 34.5 5.30002C29.5294 5.30002 25.5 9.32946 25.5 14.3C25.5 14.5588 25.5109 14.815 25.5323 15.0682C25.7141 17.2191 25.3896 19.5968 23.7233 20.9691L23.5191 21.1372M20.0292 24.0112L11.818 15.8H9L4.5 8.30002L7.5 5.30002L15 9.80002V12.618L23.5191 21.1372M20.0292 24.0112L23.5191 21.1372M36.75 37.55L31.5 32.3M9.73447 39.0501H9.74947V39.0651H9.73447V39.0501Z"
        stroke="url(#paint0_linear_4677_13440)"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4677_13440"
          x1="4.5"
          y1="5.30002"
          x2="43.5533"
          y2="44.3533"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4568DC" />
          <stop offset="1" stopColor="#B06AB3" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default WrenchIcon;
