import { useEffect, useCallback } from 'react';

export function useThemeConfig(apiUrl = '/api/theme-config') {
  // Function to update CSS variables on the document root
  const updateTheme = useCallback(
    (themeConfig: { [x: string]: string | null }) => {
      const root = document.documentElement;
      Object.keys(themeConfig).forEach((key) => {
        // Assumes that our API keys are named without the leading "--"
        root.style.setProperty(`--${key}`, themeConfig[key]);
      });
    },
    []
  );

  // Function to fetch the theme configuration from the API
  const fetchTheme = useCallback(async () => {
    try {
      const res = await fetch(apiUrl);
      if (!res.ok) {
        throw new Error('Failed to fetch theme configuration');
      }
      const themeConfig = await res.json();
      updateTheme(themeConfig);
    } catch (error) {
      console.error('Error fetching theme configuration:', error);
    }
  }, [apiUrl, updateTheme]);

  // Fetch the theme configuration on mount
  useEffect(() => {
    fetchTheme();
  }, [fetchTheme]);

  // Optionally return the refetch function for manual updates
  return { refetchTheme: fetchTheme };
}
