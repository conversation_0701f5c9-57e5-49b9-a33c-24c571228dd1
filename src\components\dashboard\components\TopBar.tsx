'use client';

import React, { useEffect } from 'react';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import GovernmentEmblem from '@/assets/DashboardEmblem.svg';
import useSettingsStore from '@/zustand/store/settingsStore';
import Logo from '@/constants/logo';
import { RequestAccessWidget } from './trainingProvider/requestWidget';
import { useTrainingProviderStore } from '@/zustand/store/trainerProvider';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

interface TopBarProps {
  toggleActionsAlert?: () => void;
}

const TopBar: React.FC<TopBarProps> = () => {
  const { appearanceSettings } = useSettingsStore();
  const { isTrainer, isLoading, checkTrainerStatus } =
    useTrainingProviderStore();
  const { user } = useAuthStore();

  useEffect(() => {
    if (user?.id) {
      checkTrainerStatus(user.id);
    } else {
      useTrainingProviderStore.getState().reset();
    }
  }, [user?.id, checkTrainerStatus]);

  const router = useRouter();

  return (
    <div className="bg-[--topBarBgColor] flex flex-col text-white px-6 py-6 h-[108px] shadow  z-10 ">
      <div className="flex justify-between">
        <div className=" items-center space-x-2">
          <Logo logoColor="white" className="text-white" />
        </div>
        <div className="flex gap-6">
          {!isLoading && !isTrainer && <RequestAccessWidget />}
          <Image
            src={appearanceSettings?.governmentEmblem || GovernmentEmblem}
            width={100}
            height={100}
            className="w-auto h-[70px] cursor-pointer"
            alt="bubbleIcon"
            onClick={() => router.push('/dashboard/contact-support')}
          />
        </div>
      </div>
    </div>
  );
};

export default TopBar;
