import React from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import JobIcon from '@/components/icons/JobIcon';
import JobSearchIcon from '@/components/icons/JobSearchIcon';
import useSettingsStore from '@/zustand/store/settingsStore';

function AdvanceCareerSection() {
  const { appearanceSettings } = useSettingsStore();

  // eslint-disable-next-line prefer-destructuring
  const brandColor = appearanceSettings.brandColor;

  return (
    <div>
      <SectionHeader title="Advance your career" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-4">
        <ActionCard
          icon={<JobIcon />}
          title={'View recommended jobs'}
          description={
            'Discover tailored job listings that match your skills and goals.'
          }
        />
        <ActionCard
          icon={<JobSearchIcon strokeColor={brandColor} />}
          title={'Explore job trends'}
          description={
            'Stay ahead with the latest industry trends and in-demand roles.'
          }
        />
      </div>
    </div>
  );
}

export default AdvanceCareerSection;
