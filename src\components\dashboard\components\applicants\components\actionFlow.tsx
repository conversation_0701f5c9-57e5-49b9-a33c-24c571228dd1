'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Loader2, MoreVertical } from 'lucide-react';
import * as Popover from '@radix-ui/react-popover';
import { Dialog, DialogContent } from '@/components/ui/dialog';

export function MoreActionsDropdown({
  onHire,
  onContacted,
}: {
  onHire: () => void;
  onContacted?: () => void;
}) {
  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <Button
          variant="ghost"
          className="rounded-full px-3 py-4 border border-[--buttonColor] hover:text-neutral-900"
        >
          <MoreVertical className="w-5 h-5 text-[--buttonColor]" />
        </Button>
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          side="bottom"
          align="end"
          className="w-[250px] bg-white shadow-md rounded-lg p-2 space-y-2 mt-2"
        >
          <Button
            variant="outline"
            className="w-full justify-start bg-transparent border-none"
            onClick={onHire}
          >
            Mark as Hired
          </Button>
          {onContacted && (
            <Button
              variant="outline"
              className="w-full justify-start bg-transparent border-none"
              onClick={onContacted}
            >
              Mark as Contacted
            </Button>
          )}
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}

export function ConfirmModal({
  open,
  onOpenChange,
  title,
  description,
  confirmText,
  loading,
  onConfirm,
  destructive = false,
}: {
  open: boolean;
  onOpenChange: (_open: boolean) => void;
  title: string;
  description: string;
  confirmText: string;
  loading: boolean;
  onConfirm: () => void;
  destructive?: boolean;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <h2 className="text-[24px] font-semibold text-neutral-900 leading-[32px] -tracking-[0.48px]">
          {title}
        </h2>
        <p className="text-[18px] text-neutral-500 font-normal">
          {description}
        </p>
        <div className="flex gap-2 justify-end pt-6">
          <Button
            variant="outline"
            className="h-[48px] py-[14px] px-[28px] gap-[8px]"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={loading}
            className="h-[48px] py-[14px] px-[28px] gap-[8px]"
            variant={destructive ? 'destructive' : 'default'}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              confirmText
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
