'use client';

import type { Route } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';
import CareerNavigatorLogo from '@/assets/CareerNavigatorLogo.svg';

import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { GovernmentEmblem } from './governmentEmblem';
import useSettingsStore from '@/zustand/store/settingsStore';
import { validationMessages } from '@/constants/validationMessages';
import InputField from '../common/InputField';

const {
  fullNameRequired,
  invalidFullName,
  emailInvalid,
  emailRequired,
  passwordInvalid,
  passwordRequired,
  confirmPasswordMismatch,
  confirmPasswordRequired,
} = validationMessages;

const passwordRegex =
  /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_]).{8,}$/;

// Validation Schema
const validationSchema = Yup.object().shape({
  fullName: Yup.string().min(3, fullNameRequired).required(invalidFullName),
  partner_id: Yup.string().optional(),
  email: Yup.string().email(emailInvalid).required(emailRequired),
  password: Yup.string()
    .matches(passwordRegex, passwordInvalid)
    .required(passwordRequired),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], confirmPasswordMismatch)
    .required(confirmPasswordRequired),
});

export default function SignUpForm() {
  const { push } = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const register = useAuthStore((state) => state.register);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const redirectToPath = (path: Route) => {
    push(path);
  };

  return (
    <>
      <div className="flex flex-col gap-8">
        {/* Header */}
        <div className="flex flex-col items-start font-semibold text-neutral-900 dark:text-neutral-100">
          <Image
            src={appearanceSettings?.platformLogoDark || CareerNavigatorLogo}
            alt="Logo"
            width={300}
            height={40}
            className="w-auto h-[40px] cursor-pointer"
            onClick={() => redirectToPath('/')}
          />
          <h2 className="text-neutral-900 text-[28px] font-semibold leading-[36px] mt-8">
            Sign up as a Partner Company
          </h2>
        </div>

        <div className="w-full mx-auto">
          <Formik
            initialValues={{
              fullName: '',
              partner_id: '',
              email: '',
              password: '',
              confirmPassword: '',
            }}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                await register({
                  fullName: values.fullName,
                  email: values.email,
                  password: values.password,
                  role: 'partner',
                });
                redirectToPath('/welcome-onboarding');
              } catch (error) {
                console.error('Signup failed:', error);
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ handleChange, handleBlur, values, isSubmitting, errors }) => (
              <Form className="grid grid-cols-1">
                <div className="mb-3 space-y-1">
                  <InputField
                    label="Full Name"
                    error={errors.fullName}
                    name={'fullName'}
                    type={'text'}
                    placeholder="Enter your fullname"
                    value={values.fullName}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                <div className="mb-3 space-y-1">
                  <InputField
                    label="Work Email"
                    error={errors.email}
                    name={'email'}
                    type={'email'}
                    placeholder="<EMAIL>"
                    value={values.email}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                {/* Password */}
                <div className="mb-3 space-y-1 relative">
                  <button
                    type="button"
                    className="absolute right-3 top-[48px] transform -translate-y-1/2 text-neutral-500 hover:text-neutral-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-primary-500" />
                    ) : (
                      <Eye className="h-5 w-5 text-primary-500" />
                    )}
                  </button>
                  <InputField
                    label="Password"
                    error={errors.password}
                    name={'password'}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    value={values.password}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                {/* Confirm Password */}
                <div className="mb-3 space-y-1 relative">
                  <button
                    type="button"
                    className="absolute right-3 top-[48px] transform -translate-y-1/2 text-neutral-500 hover:text-neutral-700"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-primary-500" />
                    ) : (
                      <Eye className="h-5 w-5 text-primary-500" />
                    )}
                  </button>
                  <InputField
                    label="Confirm Password"
                    error={errors.confirmPassword}
                    name={'confirmPassword'}
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={values.confirmPassword}
                    placeholder="Confirm your password"
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="justify-center mt-6 mb-8  flex items-center gap-2 text-[16px] py-[14px] px-[28px] h-[48px]"
                >
                  {isSubmitting ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    'Continue'
                  )}
                </Button>

                {/* Footer Link */}
                <div className="text-[16px] font-normal text-center text-neutral-500">
                  Have an account?
                  <Link href={'/login' as Route} className="">
                    <span className="font-semibold ml-1 text-primary-500 hover:underline">
                      Login
                    </span>
                  </Link>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>

      <GovernmentEmblem />
    </>
  );
}
