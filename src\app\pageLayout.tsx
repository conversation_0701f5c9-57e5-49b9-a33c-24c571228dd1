'use client';
import Notification from '@/utils/notification';
import QueryClientProviderWrapper from './providers/queryClient';
// import { Toaster } from '@/components/ui/sonner'
import { useEffect, useState } from 'react';
import useSettingsStore from '@/zustand/store/settingsStore';
import { Loader } from '@/components/dashboard/common/Loader';

interface PageLayoutProps {
  children: React.ReactNode;
}

const PageLayout: React.FC<PageLayoutProps> = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const { getSettings } = useSettingsStore();

  const getData = async () => {
    setLoading(true);
    await getSettings();
    setLoading(false);
  };
  useEffect(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <QueryClientProviderWrapper>
      {loading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
          <Loader />
        </div>
      )}
      <div>
        <Notification />
        {children}
        {/* <Toaster /> */}
      </div>
    </QueryClientProviderWrapper>
  );
};

export default PageLayout;
