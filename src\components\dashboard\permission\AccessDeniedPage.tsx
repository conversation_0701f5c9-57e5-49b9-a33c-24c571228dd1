'use client';

import type React from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useRouter } from 'next/navigation';
import { Shield, ArrowLeft, Home, Mail, RefreshCw } from 'lucide-react';
import { usePermission } from '@/hooks/usePermission';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

interface AccessDeniedPageProps {
  title?: string;
  description?: string;
  requiredPermission?: string;
  showContactSupport?: boolean;
  showRefreshButton?: boolean;
  customActions?: React.ReactNode;
  variant?: 'page' | 'card' | 'minimal';
  className?: string;
}

/**
 * Full page component for access denied scenarios
 */
export function AccessDeniedPage({
  title = 'Access Denied',
  description = "You don't have permission to access this page.",
  requiredPermission,
  showContactSupport = true,
  showRefreshButton = true,
  customActions,
  variant = 'page',
  className = '',
}: AccessDeniedPageProps) {
  const router = useRouter();
  const { refreshPermissions, isLoading } = usePermission();
  const { user } = useAuthStore();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/dashboard');
    }
  };

  const handleGoHome = () => {
    router.push('/dashboard');
  };

  const handleRefreshPermissions = async () => {
    await refreshPermissions();
    // Optionally reload the page after refreshing permissions
    window.location.reload();
  };

  const handleContactSupport = () => {
    // You can customize this to open a support modal, redirect to support page, etc.
    window.open(
      'mailto:<EMAIL>?subject=Access Request',
      '_blank'
    );
  };

  const content = (
    <div className="text-center space-y-6">
      {/* Icon */}
      <div className="flex justify-center">
        <div className="rounded-full bg-red-100 p-6">
          <Shield className="h-16 w-16 text-red-600" />
        </div>
      </div>

      {/* Title and Description */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
        <p className="text-lg text-gray-600 max-w-md mx-auto">{description}</p>
        {requiredPermission && (
          <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full inline-block">
            Required permission:{' '}
            <code className="font-mono">{requiredPermission}</code>
          </p>
        )}
      </div>

      {/* User Info */}
      {user && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
          <p className="text-sm text-blue-800">
            <strong>Current user:</strong> {user.fullName || user.email}
          </p>
          <p className="text-sm text-blue-600">
            If you believe this is an error, please contact your administrator.
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
        <Button
          onClick={handleGoBack}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Go Back
        </Button>

        <Button onClick={handleGoHome} className="flex items-center gap-2">
          <Home className="h-4 w-4" />
          Go to Dashboard
        </Button>

        {showRefreshButton && (
          <Button
            onClick={handleRefreshPermissions}
            variant="outline"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
            />
            {isLoading ? 'Refreshing...' : 'Refresh Permissions'}
          </Button>
        )}

        {showContactSupport && (
          <Button
            onClick={handleContactSupport}
            variant="ghost"
            className="flex items-center gap-2"
          >
            <Mail className="h-4 w-4" />
            Contact Support
          </Button>
        )}
      </div>

      {/* Custom Actions */}
      {customActions && <div className="pt-4">{customActions}</div>}
    </div>
  );

  if (variant === 'card') {
    return (
      <div
        className={`flex justify-center items-center min-h-[60vh] p-4 ${className}`}
      >
        <Card className="w-full max-w-2xl">
          <CardContent className="p-8">{content}</CardContent>
        </Card>
      </div>
    );
  }

  if (variant === 'minimal') {
    return (
      <div
        className={`flex justify-center items-center min-h-[40vh] p-4 ${className}`}
      >
        <div className="w-full max-w-md">{content}</div>
      </div>
    );
  }

  // Default page variant
  return (
    <div
      className={`min-h-screen flex items-center justify-center bg-gray-50 px-4 ${className}`}
    >
      <div className="w-full max-w-2xl">{content}</div>
    </div>
  );
}
