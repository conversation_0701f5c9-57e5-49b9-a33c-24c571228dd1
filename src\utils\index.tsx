import DOMPurify from 'dompurify';

export function normalizeText(input: string, separator = ' ') {
  return input.replace(/[-_]+/g, separator).replace(/\s+/g, ' ').trim();
}

export const formatDateDDMonthYYYY = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const day = date.getUTCDate();
  const month = date.toLocaleString('en-US', {
    month: 'long',
    timeZone: 'UTC',
  });
  const year = date.getUTCFullYear();
  return `${day} ${month} ${year}`;
};

export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear();
    return `${day} ${month} ${year}`;
  } catch {
    return '';
  }
};

export function sanitizeHtml(html: string) {
  return DOMPurify.sanitize(html, {
    USE_PROFILES: { html: true },
  });
}

export const StatusCountBadge = ({
  count,
  isActive,
}: {
  count: number;
  isActive: boolean;
}) => {
  return (
    <span
      className={`ml-2 rounded-full px-2.5 py-[2.5px] text-[20px] font-medium min-w-[35px] justify-center items-center gap-2.5 ${
        isActive
          ? 'bg-primary-500 !text-white'
          : 'bg-neutral-100 !text-neutral-800'
      }`}
    >
      {count}
    </span>
  );
};
