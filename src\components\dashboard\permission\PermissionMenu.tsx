/* eslint-disable no-unused-vars */
'use client';

import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import type { ReactNode } from 'react';

interface MenuItem {
  id: string;
  label: string;
  icon?: ReactNode;
  permission: Permission | Permission[];
  permissionOptions?: PermissionCheckOptions;
  href?: string;
  onClick?: () => void;
  children?: MenuItem[];
  className?: string;
}

interface PermissionMenuProps {
  items: MenuItem[];
  className?: string;
  itemClassName?: string;
  renderItem?: (item: MenuItem, hasPermission: boolean) => ReactNode;
  showRestrictedItems?: boolean;
  restrictedItemClassName?: string;
}

/**
 * Menu component that filters items based on user permissions
 */
export function PermissionMenu({
  items,
  className = '',
  itemClassName = '',
  renderItem,
  showRestrictedItems = false,
  restrictedItemClassName = 'opacity-50 cursor-not-allowed',
}: PermissionMenuProps) {
  const { checkPermission, isLoading } = usePermission();

  const defaultRenderItem = (item: MenuItem, hasPermission: boolean) => {
    const baseClassName = `${itemClassName} ${!hasPermission ? restrictedItemClassName : ''}`;

    if (item.href && hasPermission) {
      return (
        <a
          key={item.id}
          href={item.href}
          className={`${baseClassName} ${item.className || ''}`}
        >
          {item.icon && <span className="mr-2">{item.icon}</span>}
          {item.label}
        </a>
      );
    }

    if (item.onClick && hasPermission) {
      return (
        <button
          key={item.id}
          onClick={item.onClick}
          className={`${baseClassName} ${item.className || ''}`}
          disabled={!hasPermission}
        >
          {item.icon && <span className="mr-2">{item.icon}</span>}
          {item.label}
        </button>
      );
    }

    return (
      <span
        key={item.id}
        className={`${baseClassName} ${item.className || ''}`}
      >
        {item.icon && <span className="mr-2">{item.icon}</span>}
        {item.label}
      </span>
    );
  };

  const renderMenuItem = (item: MenuItem): ReactNode | null => {
    const hasPermission = checkPermission(
      item.permission,
      item.permissionOptions
    );

    // Hide item if no permission and showRestrictedItems is false
    if (!hasPermission && !showRestrictedItems) {
      return null;
    }

    const renderedItem = renderItem
      ? renderItem(item, hasPermission)
      : defaultRenderItem(item, hasPermission);

    // Render children if they exist
    if (item.children && item.children.length > 0) {
      const visibleChildren = item.children.filter((child) => {
        const childHasPermission = checkPermission(
          child.permission,
          child.permissionOptions
        );
        return childHasPermission || showRestrictedItems;
      });

      if (visibleChildren.length === 0 && !showRestrictedItems) {
        return null;
      }

      return (
        <div key={item.id}>
          {renderedItem}
          <div className="ml-4">
            {visibleChildren.map((child) => renderMenuItem(child))}
          </div>
        </div>
      );
    }

    return renderedItem;
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className={className}>
        {items.map((item) => (
          <div key={item.id} className={`${itemClassName} animate-pulse`}>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={className}>{items.map((item) => renderMenuItem(item))}</div>
  );
}
