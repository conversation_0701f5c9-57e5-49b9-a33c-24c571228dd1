/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  ApiRepresentative,
  IGetPartners,
  InviteRepresentativePayload,
} from '@/type';
import axiosClient from '@/utils/axiosClient';
import { useNotificationStore } from '@/zustand/user/notificationStore';

const API_URL = 'partners/';

class CompanyService {
  async getStats() {
    const res = await axiosClient.get(`${API_URL}stats`);
    return res.data;
  }
  async getPartners(params: Partial<IGetPartners>) {
    const res = await axiosClient.get(`${API_URL}`, { params });
    return res.data;
  }

  async getPartnerById(id: string): Promise<IGetPartners> {
    try {
      const response = await axiosClient.get<{
        success: boolean;
        data: IGetPartners;
      }>(`${API_URL}${id}`);

      if (!response.data.success) {
        throw new Error('Failed to fetch partner data');
      }

      return response.data.data;
    } catch (error) {
      console.error('Error fetching partner by ID:', error);
      throw error;
    }
  }

  async updatePartnerById(
    id: string,
    updatedPartner: Partial<IGetPartners>
  ): Promise<IGetPartners> {
    const { showNotification } = useNotificationStore.getState();
    try {
      const response = await axiosClient.put<{
        success: boolean;
        data: IGetPartners;
      }>(`${API_URL}${id}`, updatedPartner);

      return response.data.data;
    } catch (error) {
      showNotification('Failed to update Company details!', 'success');
      throw error;
    }
  }

  async inviteRepresentative(data: InviteRepresentativePayload): Promise<void> {
    try {
      await axiosClient.post(`${API_URL}invite/representative`, data);
    } catch (error) {
      throw error;
    }
  }

  async getRepresentative(): Promise<ApiRepresentative[]> {
    const response = await axiosClient.get<{ data: ApiRepresentative[] }>(
      `${API_URL}representatives`
    );
    return response.data?.data ?? [];
  }

  async statusUpdate(userId: string): Promise<any> {
    const res = await axiosClient.post(
      `${API_URL}representative/${userId}/toggle-status`
    );
    return res.data;
  }
}

const companyService = new CompanyService();
export default companyService;
