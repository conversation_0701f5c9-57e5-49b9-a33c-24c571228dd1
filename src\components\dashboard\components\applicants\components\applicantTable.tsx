'use client';

import type React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react';
import type { Applicant } from '@/type';
import ApplicantActions from './applicationAction';
import { SelectAllCheckbox } from './selectAllCheckbox';

interface ApplicantTableProps {
  applicants: Applicant[];
  selectedIds: string[];
  onSelect: (_ids: string[]) => void;
  onRowClick: (_id: string, _status: Applicant['status']) => void;
  updateApplicantStatus: (_variables: {
    id: string;
    status: Applicant['status'];
  }) => Promise<void>;
  status: Applicant['status'];
  sortField: string | null;
  ascending: boolean;
  handleSort: (_field: string) => void;
  totalApplicantsCount?: number;
  allApplicantIds?: string[];
  createdById?: string;
}

const ApplicantTable: React.FC<ApplicantTableProps> = ({
  applicants,
  selectedIds,
  onSelect,
  onRowClick,
  updateApplicantStatus,
  status,
  sortField,
  ascending,
  handleSort,
  totalApplicantsCount,
  allApplicantIds,
}) => {
  const calculatedTotalCount =
    totalApplicantsCount ||
    (allApplicantIds ? allApplicantIds.length : applicants.length);

  const toggleRow = (id: string) => {
    onSelect(
      selectedIds.includes(id)
        ? selectedIds.filter((x) => x !== id)
        : [...selectedIds, id]
    );
  };

  const toggleAllOnPage = () => {
    onSelect(
      selectedIds.length === applicants.length
        ? []
        : applicants.map((a) => a.id)
    );
  };

  const selectAllRecords = () => {
    if (allApplicantIds && allApplicantIds.length > 0) {
      onSelect(allApplicantIds);
    } else {
      onSelect(applicants.map((a) => a.id));
    }
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A';
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const isIndeterminate =
    selectedIds.length > 0 && selectedIds.length < calculatedTotalCount;

  const allSelected =
    calculatedTotalCount > 0 && selectedIds.length === calculatedTotalCount;

  return (
    <Table className="overflow-scroll">
      <TableHeader className="bg-neutral-50 rounded border-0">
        <TableRow className="text-neutral-700 text-[16px] font-semibold">
          <TableHead>
            <SelectAllCheckbox
              allSelected={allSelected}
              isIndeterminate={isIndeterminate}
              currentPageCount={applicants.length}
              totalCount={calculatedTotalCount}
              onSelectPage={toggleAllOnPage}
              onSelectAll={selectAllRecords}
            />
          </TableHead>
          <TableHead>
            <button
              className="flex items-center gap-1 w-full text-[--bodyTextColor] text-[16px] font-semibold"
              onClick={() => handleSort('userName')}
            >
              Name
              {sortField === 'userName' ? (
                ascending ? (
                  <ChevronUp className="w-4 h-4 !text-[--bodyTextColor]" />
                ) : (
                  <ChevronDown className="w-4 h-4 !text-[--bodyTextColor]" />
                )
              ) : (
                <ChevronsUpDown className="w-4 h-4 text-gray-300 !text-[--bodyTextColor]" />
              )}
            </button>
          </TableHead>
          <TableHead>
            <button
              className="flex items-center gap-1 w-full text-[--bodyTextColor] text-[16px] font-semibold "
              onClick={() => handleSort('title')}
            >
              Job Title
              {sortField === 'title' ? (
                ascending ? (
                  <ChevronUp className="w-4 h-4 !text-[--bodyTextColor]" />
                ) : (
                  <ChevronDown className="w-4 h-4 !text-[--bodyTextColor]" />
                )
              ) : (
                <ChevronsUpDown className="w-4 h-4 text-gray-300 !text-[--bodyTextColor]" />
              )}
            </button>
          </TableHead>
          <TableHead>
            <button
              className="flex items-center gap-1 w-full text-[--bodyTextColor] text-[16px] font-semibold"
              onClick={() => handleSort('appliedDate')}
            >
              Applied Date
              {sortField === 'appliedDate' ? (
                ascending ? (
                  <ChevronUp className="w-4 h-4 !text-[--bodyTextColor]" />
                ) : (
                  <ChevronDown className="w-4 h-4 !text-[--bodyTextColor]" />
                )
              ) : (
                <ChevronsUpDown className="w-4 h-4 text-gray-300 !text-[--bodyTextColor]" />
              )}
            </button>
          </TableHead>
          <TableHead>
            <button
              className="flex items-center gap-1 w-full text-[--bodyTextColor] text-[16px] font-semibold"
              onClick={() => handleSort('email')}
            >
              Email
              {sortField === 'email' ? (
                ascending ? (
                  <ChevronUp className="w-4 h-4 !text-[--bodyTextColor]" />
                ) : (
                  <ChevronDown className="w-4 h-4 !text-[--bodyTextColor]" />
                )
              ) : (
                <ChevronsUpDown className="w-4 h-4 text-gray-300 !text-[--bodyTextColor]" />
              )}
            </button>
          </TableHead>
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {applicants.length > 0 ? (
          applicants.map((applicant) => (
            <TableRow
              key={applicant.user.id}
              className={`transition cursor-pointer ${
                selectedIds.includes(applicant.id)
                  ? 'bg-neutral-200'
                  : 'hover:bg-neutral-50'
              }`}
              onClick={() => onRowClick(applicant.id, status)}
            >
              <TableCell onClick={(e) => e.stopPropagation()}>
                <Checkbox
                  checked={selectedIds.includes(applicant.id)}
                  onCheckedChange={() => toggleRow(applicant.id)}
                />
              </TableCell>
              <TableCell className="min-w-[200px]">
                <div className="flex flex-col">
                  <h5 className="text-[16px] font-medium !text-[--bodyTextColor]">
                    {applicant.user?.fullName || 'N/A'}
                  </h5>
                </div>
              </TableCell>
              <TableCell className="min-w-[300px]">
                <h5 className="text-[16px] font-medium !text-[--bodyTextColor]">
                  {applicant.job?.title || 'N/A'}
                </h5>
              </TableCell>
              <TableCell className="min-w-[200px]">
                <h6 className="text-[16px] font-normal !text-[--bodyTextColor]">
                  {formatDate(applicant.appliedDate)}
                </h6>
              </TableCell>
              <TableCell>
                <h6 className="text-[16px] font-normal !text-[--bodyTextColor]">
                  {applicant?.user?.email || 'N/A'}
                </h6>
              </TableCell>
              <TableCell onClick={(e) => e.stopPropagation()}>
                <ApplicantActions
                  applicant={applicant}
                  status={status}
                  onUpdateStatus={(id, newStatus) =>
                    updateApplicantStatus({ id, status: newStatus })
                  }
                />
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-8">
              <p className="text-neutral-500">
                No applicants found for this status
              </p>
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

export default ApplicantTable;
