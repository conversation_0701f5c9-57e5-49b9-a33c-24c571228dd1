'use client';

import React, { useEffect, useState } from 'react';
import { OverViewCard } from '../components/over-view-card';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { PlusIcon } from 'lucide-react';
import { getStats } from '@/zustand/services/courseServices';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import ArrowIcon from '@/components/icons/ArrowRight';

import TrainingOpenBook from '@/components/icons/OpenBook';
import CheckCircleIcon from '@/components/icons/CheckCircle';
import GradCapIcon from '@/components/icons/GradCap';
import useSettingsStore from '@/zustand/store/settingsStore';

export function TrainingSection() {
  const { appearanceSettings } = useSettingsStore();
  const router = useRouter();
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalActiveCourses: 0,
    completedEnrollments: 0,
    totalEnrolledUsers: 0,
  });
  const brandColor = appearanceSettings?.brandColor;

  const fetchStats = async () => {
    try {
      setIsLoading(true);

      // Fetch stats for the current user
      const statsResponse = await getStats({ createdById: user?.id });

      setStats({
        totalActiveCourses: statsResponse.data.activeCourses,
        totalEnrolledUsers: statsResponse.data.totalEnrolledUsers,
        completedEnrollments: statsResponse.data.completedEnrollments,
      });
    } catch (error) {
      console.error('Error fetching statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchStats();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id]);

  return (
    <div className="space-y-3">
      <h4 className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
        Training
      </h4>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <OverViewCard
          icon={<TrainingOpenBook stopColor={brandColor} />}
          title={stats.totalActiveCourses}
          description={isLoading ? 'Loading' : 'Total Active Courses'}
          arrowIcon={<ArrowIcon stroke={brandColor} />}
          route="/dashboard/courses"
        />
        <OverViewCard
          icon={<GradCapIcon stopColor={brandColor} />}
          title={stats.totalEnrolledUsers}
          description={isLoading ? 'Loading' : 'Learner Enrolled'}
        />
        <OverViewCard
          icon={<CheckCircleIcon stopColor={brandColor} />}
          title={stats.completedEnrollments}
          description={isLoading ? 'Loading' : 'Courses Completed'}
        />
        <Card
          onClick={() => router.push('/dashboard/courses/add-a-new-course')}
          className="flex flex-col items-center justify-center rounded-lg px-[32px] py-[24px] gap-2 border-2 border-primary-500 hover:border-neutral-500 cursor-pointer hover:bg-neutral-50 transition-colors h-[140px]"
        >
          <PlusIcon
            size={38}
            className="w-[48px] h-[48px] flex-shrink-0 text-primary-500"
          />
          <p className="text-center text-primary-500 text-[18px] font-semibold leading-[20px]">
            Add a New Course
          </p>
        </Card>
      </div>
    </div>
  );
}
