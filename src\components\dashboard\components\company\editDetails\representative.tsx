'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Representative } from '@/type/index';
import { MoreVertical } from 'lucide-react';

interface RepresentativeCardProps {
  representative: Representative;
  onEdit: () => void;
  onDeactivate: () => void;
  onReactivate: () => void;
  onDelete: () => void;
}

export function RepresentativeCard({
  representative,
  onEdit,
  onDeactivate,
  onReactivate,
  onDelete,
}: RepresentativeCardProps) {
  const getInitials = (name: string) => {
    if (!name) return 'SF';

    if (name.includes('@')) return 'SF';

    const parts = name.split(' ');
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  const getBadgeStyles = () => {
    if (representative.isActive) {
      return {
        variant: 'outline' as const,
        className:
          'rounded-full px-2 py-0.5 text-xs flex items-center gap-1 border-primary-500 text-primary-600 h-[24px] py-[2px] px-[8px]',
        dotClass: 'w-1.5 h-1.5 rounded-full bg-primary-500',
        label: 'Active',
      };
    }

    return {
      variant: 'outline' as const,
      className:
        'rounded-full px-2 py-0.5 text-xs flex items-center gap-1 border-destructive-600 text-destructive-600 h-[24px] py-[2px] px-[8px]',
      dotClass: 'w-1.5 h-1.5 rounded-full bg-destructive-600',
      label: 'Inactive',
    };
  };

  const badgeStyles = getBadgeStyles();

  const fullName =
    representative.firstName && representative.lastName
      ? `${representative.firstName} ${representative.lastName}`
      : representative.isActive
        ? representative.email
        : 'Full Name';

  const displayRole = representative.role.includes('Full_Admin_Access')
    ? 'Full Admin Access'
    : representative.role;

  return (
    <Card className="border border-neutral-200 rounded-lg overflow-hidden hover:shadow-sm transition-shadow">
      <CardContent className="p-4 flex items-start gap-4">
        <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-primary-500 rounded-full !text-white font-medium mt-6">
          {getInitials(fullName)}
        </div>

        <div className="space-y-1 flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-neutral-700 leading-[24px] text-[16px]">
                {fullName}
              </h3>
              <Badge
                variant={badgeStyles.variant}
                className={badgeStyles.className}
              >
                <div className={badgeStyles.dotClass} />
                {badgeStyles.label}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                type="button"
                className="rounded-full"
                size="sm"
                onClick={onEdit}
              >
                Edit
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="rounded-full"
                    size="icon"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {!representative.isActive ? (
                    <DropdownMenuItem
                      onClick={onReactivate}
                      className="text-primary-600"
                    >
                      Reactivate
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem
                      onClick={onDeactivate}
                      className="text-destructive-600"
                    >
                      Deactivate
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={onDelete}
                    className="text-destructive-600"
                  >
                    Delete Account
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <p className="text-sm text-neutral-500">{representative.email}</p>
          <p className="text-sm text-neutral-500">{displayRole}</p>
        </div>
      </CardContent>
    </Card>
  );
}
