'use client';

import type React from 'react';
import { useState, useRef } from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { UploadIcon } from 'lucide-react';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { AdminValue, IndustryOption } from '@/type';
import InputField from '@/components/common/InputField';

type ProfessionalInfoData = {
  name: string;
  companyId: string;
  industry: string;
  size: string;
  logo: string;
};

const ProfessionalInfoCard = ({
  data,
  updateData,
  errors = {},
}: {
  data: ProfessionalInfoData;
  updateData: (_data: Partial<ProfessionalInfoData>) => void;
  errors?: Record<string, string>;
}) => {
  const [imageUrl, setImageUrl] = useState<string>(data.logo || '');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setImageUrl(base64String);
        updateData({ logo: base64String });
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const getCompanySize = useAdminValues({
    category: AdminValuesCategories?.companySize?.category,
  });
  const compaySize = getCompanySize.data?.data?.data?.customValues || [];

  const industryQuery = useAdminValues({
    subcategory: AdminValuesCategories?.industryOptions.subcategories.ISIC_1,
  });

  const industryOptions: IndustryOption[] =
    industryQuery.data?.data?.data?.customValues || [];

  return (
    <div className="bg-white text-left px-4 pb-4 rounded-lg">
      <div className="mb-6 space-y-3">
        <Label
          htmlFor="company_logo"
          className="text-[18px] text-neutral-900 font-semibold leading-[28px]"
        >
          Company Logo:
        </Label>
        <div
          className={`flex flex-col gap-2 w-1/2 rounded-md p-5 ${
            imageUrl ? '' : 'border border-neutral-200'
          }`}
        >
          {imageUrl ? (
            <div className="relative w-[250px] h-[80px]">
              <Image
                src={imageUrl || '/placeholder.svg'}
                alt="Company Logo"
                fill
                className="object-cover rounded-none"
              />
            </div>
          ) : (
            <p className="text-neutral-700 font-semibold text-[18px]">
              Browse files to upload
            </p>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/png, image/jpeg"
            onChange={handleImageUpload}
            className="hidden"
          />
          <p className="text-[16px] font-normal text-neutral-500">
            {imageUrl ? '' : 'PNG or JPG up to 2.5MB'}
          </p>
          <Button
            type="button"
            onClick={triggerFileInput}
            className="text-[16px] w-fit mt-4"
            variant={imageUrl ? 'outline' : 'default'}
          >
            {imageUrl ? (
              <>
                <UploadIcon className="w-5 h-5" />{' '}
                <span className="text-primary-500 text-[16px] !text-[--buttonColor]">
                  Choose Another Image
                </span>
              </>
            ) : (
              <>
                <UploadIcon className="w-5 h-5" /> Browse files
              </>
            )}
          </Button>
        </div>
        {errors.logo && (
          <p className="!text-destructive-500 text-[16px]">{errors.logo}</p>
        )}
      </div>

      <form className="space-y-4">
        <div className="space-y-1">
          <InputField
            label="Company Name:"
            labelClass="text-[18px] text-neutral-900 font-semibold leading-[28px]"
            name={'company_name'}
            type={'text'}
            placeholder="My Company"
            value={data.name}
            handleChange={(e) => updateData({ name: e.target.value })}
          />
          {errors.name && (
            <p className="!text-destructive-500 text-[16px]">{errors.name}</p>
          )}
        </div>
        <div className="space-y-1">
          <InputField
            label="Company ID:"
            labelClass="text-[18px] text-neutral-900 font-semibold leading-[28px]"
            name={'companyId'}
            type={'number'}
            placeholder="***********"
            value={data.companyId}
            handleChange={(e) => updateData({ companyId: e.target.value })}
          />
          {errors.companyId && (
            <p className="!text-destructive-500 text-[16px]">
              {errors.companyId}
            </p>
          )}
        </div>
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="industry"
            className="text-[18px] text-neutral-900 font-semibold leading-[28px]"
          >
            Industry:
          </Label>
          <Select
            value={data.industry}
            onValueChange={(value) => updateData({ industry: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Please select" />
            </SelectTrigger>
            <SelectContent>
              {industryOptions.map((industry) => (
                <SelectItem key={industry.id} value={industry.value}>
                  {industry.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.industry && (
            <p className="!text-destructive-500 text-[16px]">
              {errors.industry}
            </p>
          )}
        </div>
        <div className="mb-3 space-y-1 col-span-2">
          <Label
            htmlFor="company_size"
            className="text-[18px] text-neutral-900 font-semibold leading-[28px]"
          >
            Company Size:
          </Label>
          <Select
            value={data.size}
            onValueChange={(value) => updateData({ size: value })}
          >
            <SelectTrigger className="">
              <SelectValue
                placeholder={
                  getCompanySize?.isLoading ? 'Loading...' : 'Company size'
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {compaySize.map((size: AdminValue) => (
                  <SelectItem key={size.id} value={size.label}>
                    {size.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {errors.size && (
            <p className="!text-destructive-500 text-[16px]">{errors.size}</p>
          )}
        </div>
      </form>
    </div>
  );
};

export default ProfessionalInfoCard;
