import React from 'react';

interface StepperProps {
  currentStep: number; // Current active step
  steps: number; // Total number of steps
}

const Stepper: React.FC<StepperProps> = ({ currentStep, steps }) => {
  return (
    <div className="flex items-center justify-center space-x-4">
      {Array.from({ length: steps }).map((_, index) => {
        // const isActive = index + 1 === currentStep;

        return (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index} className="flex items-center">
            {/* Step Indicator */}
            <div
              className={`w-6 h-2 border rounded-full ${
                index <= currentStep
                  ? 'bg-primary-500 border-primary-500'
                  : 'bg-transparent border-gray-400'
              }`}
            ></div>
          </div>
        );
      })}
    </div>
  );
};

export default Stepper;
