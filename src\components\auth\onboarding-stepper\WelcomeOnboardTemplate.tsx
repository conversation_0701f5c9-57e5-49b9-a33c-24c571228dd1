'use client';

import React from 'react';
import type { Route } from 'next';
import { useRouter } from 'next/navigation';
import { RxCaretLeft } from 'react-icons/rx';
import { RxCaretRight } from 'react-icons/rx';
import Stepper from './Stepper';
import AssistantTooltip from '@/components/dashboard/common/AssistantTooltip';
import ProfessionalInfoCard from './cards/ProfessionalInfoCard';
import AgreementsCard from './cards/AgreementsCard';
import { Button } from '@/components/ui/button';
import { useCreatePartnerProfile } from '@/mutations';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { useNotificationStore } from '@/zustand/user/notificationStore';

const steps = [
  {
    title: 'Company Information',
    description:
      "Let's add your company details now! I'll need the following info:",
  },
  {
    title: 'Agreements',
    description:
      "We're almost done! The last step is to review and agree to the necessary terms and policies. Here's what needs your confirmation:",
  },
];

const WelcomeOnboardTemplate: React.FC = () => {
  const { push } = useRouter();
  const { showNotification } = useNotificationStore.getState();

  const redirectToPath = (path: Route) => {
    push(path);
  };

  const [activeStep, setActiveStep] = React.useState(0);
  const [formData, setFormData] = React.useState({
    professionalInfo: {
      name: '',
      companyId: '',
      industry: '',
      size: '',
      logo: '',
    },
    agreements: {
      termsAccepted: false,
      declarationAccepted: false,
    },
  });
  const [validationErrors, setValidationErrors] = React.useState<
    Record<string, string>
  >({});

  const validateCurrentStep = () => {
    const errors: Record<string, string> = {};

    if (activeStep === 0) {
      const { name, companyId, industry, size, logo } =
        formData.professionalInfo;

      if (!name) errors.name = 'Company name is required';
      if (!companyId) errors.companyId = 'Company ID is required';
      if (!industry) errors.industry = 'Industry is required';
      if (!size) errors.size = 'Company size is required';
      if (!logo) errors.logo = 'Company logo is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (!validateCurrentStep()) {
      showNotification('Please fill all required fields', 'error');
      return;
    }
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setValidationErrors({});
  };

  const updateFormData = (
    section: keyof typeof formData,
    data: Partial<(typeof formData)[typeof section]>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        ...data,
      },
    }));

    // Clear validation error for the field being updated
    if (activeStep === 0) {
      const fieldName = Object.keys(data)[0];
      if (fieldName && validationErrors[fieldName]) {
        const newErrors = { ...validationErrors };
        delete newErrors[fieldName];
        setValidationErrors(newErrors);
      }
    }
  };

  const handleSuccess = () => redirectToPath('/dashboard?showModal=true');

  const { createPartnerProfile, isLoading, isError, error } =
    useCreatePartnerProfile(handleSuccess);
  const userId = useAuthStore((state) => state?.user?.id);

  const onSubmit = async () => {
    if (
      !formData.agreements.declarationAccepted ||
      !formData.agreements.termsAccepted
    ) {
      showNotification('You must accept both agreements to continue', 'error');
      return;
    }

    createPartnerProfile({
      partner: {
        ...formData.professionalInfo,
        partnerId: formData.professionalInfo.companyId,
        CreatedById: userId,
        lastModifiedById: userId,
      },
    });
  };

  return (
    <div className="max-w-[42rem] mx-auto space-y-4 mb-20">
      <h1 className="text-[28px] font-semibold text-neutral-900 mb-28 mt-10 leading-[36px]">
        {steps[activeStep]?.title}
      </h1>

      <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
        <p className="mt-2 text-[18px] font-normal text-neutral-700 text-left leading-[28px] px-4 py-6">
          {steps[activeStep]?.description}
        </p>

        {activeStep === 0 && (
          <ProfessionalInfoCard
            data={formData.professionalInfo}
            updateData={(data) => updateFormData('professionalInfo', data)}
            errors={validationErrors}
          />
        )}
        {activeStep === 1 && (
          <AgreementsCard
            data={formData.agreements}
            updateData={(data) => updateFormData('agreements', data)}
          />
        )}
      </AssistantTooltip>

      {isError && <p className="text-destructive-500">{error?.message}</p>}

      <div className="mt-6 flex justify-between space-x-4 max-w-2xl mx-auto">
        {activeStep !== 0 ? (
          <Button
            onClick={handleBack}
            disabled={activeStep === 0}
            variant="outline"
            className="px-4 py-2 flex items-center text-[16px]"
          >
            <RxCaretLeft />
            Back
          </Button>
        ) : (
          <div />
        )}

        <Stepper currentStep={activeStep} steps={steps.length} />

        <Button
          onClick={activeStep === steps.length - 1 ? onSubmit : handleNext}
          disabled={activeStep === steps.length || isLoading}
          className="px-4 py-2  flex items-center"
        >
          {isLoading ? (
            'Submitting...'
          ) : (
            <>
              {activeStep === steps.length - 1 ? 'Continue' : 'Next'}
              <RxCaretRight />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default WelcomeOnboardTemplate;
