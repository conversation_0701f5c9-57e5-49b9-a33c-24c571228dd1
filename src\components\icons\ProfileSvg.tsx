import React from 'react';

function ProfileIcon({ fill = '#2A516E' }: { fill?: string }) {
  return (
    <div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.50082 6C7.50082 3.51472 9.51554 1.5 12.0008 1.5C14.4861 1.5 16.5008 3.51472 16.5008 6C16.5008 8.48528 14.4861 10.5 12.0008 10.5C9.51554 10.5 7.50082 8.48528 7.50082 6Z"
          fill={fill}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.75207 20.1053C3.82941 15.6156 7.4928 12 12.0008 12C16.5089 12 20.1724 15.6157 20.2496 20.1056C20.2547 20.4034 20.0832 20.676 19.8125 20.8002C17.4335 21.8918 14.7873 22.5 12.0011 22.5C9.21468 22.5 6.56825 21.8917 4.18914 20.7999C3.91847 20.6757 3.74694 20.4031 3.75207 20.1053Z"
          fill={fill}
        />
      </svg>
    </div>
  );
}

export default ProfileIcon;
