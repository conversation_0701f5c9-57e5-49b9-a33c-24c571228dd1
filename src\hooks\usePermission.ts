'use client';

import { useEffect } from 'react';
import { usePermissionStore } from '@/zustand/store/usePermissionStore';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

/**
 * Hook for checking user permissions
 */
export function usePermission() {
  const {
    permissions,
    isLoading,
    error,
    fetchPermissions,
    hasPermission,
    clearPermissions,
  } = usePermissionStore();
  const { user, token } = useAuthStore();

  const isAuthenticated = !!(user && token);

  useEffect(() => {
    if (isAuthenticated && permissions.length === 0 && !isLoading && !error) {
      fetchPermissions();
    } else if (!isAuthenticated) {
      clearPermissions();
    }
  }, [
    isAuthenticated,
    permissions.length,
    isLoading,
    error,
    fetchPermissions,
    clearPermissions,
  ]);

  /**
   * Check if the current user has the required permission(s)
   * @param requiredPermission - Single permission or array of permissions to check
   * @param options - Options for permission checking
   * @returns boolean indicating if user has the required permission(s)
   */
  const checkPermission = (
    requiredPermission: Permission | Permission[],
    options?: PermissionCheckOptions
  ): boolean => {
    if (!isAuthenticated) {
      return false;
    }

    return hasPermission(requiredPermission, options);
  };

  return {
    permissions,
    isLoading,
    error,
    isAuthenticated,
    checkPermission,
    refreshPermissions: fetchPermissions,
    clearPermissions,
  };
}
