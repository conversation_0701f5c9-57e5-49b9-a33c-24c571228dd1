'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import { CourseForm } from '@/components/dashboard/components/courses/courseForm';
import { getCourses } from '@/zustand/services/courseServices';
import { CourseListPage } from '@/components/dashboard/components/courses/courseTable/courseList';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { Loader } from '@/components/dashboard/common/Loader';

export default function CoursesPage() {
  const [hasCourses, setHasCourses] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const userId = useAuthStore((state) => state.user?.id);
  const router = useRouter();

  useEffect(() => {
    router.refresh();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkForCourses = async () => {
    if (!userId) {
      setIsLoading(false);
      return;
    }

    try {
      const response = await getCourses(
        {
          createdById: userId,
        },
        1,
        1
      );
      setHasCourses(response.total > 0);
    } catch (error) {
      console.error('Error checking for courses:', error);
      setHasCourses(false);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    checkForCourses();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
        <Loader />
      </div>
    );
  }

  return (
    <>
      <div className="mb-4">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: hasCourses ? 'My Courses' : 'Add a New Course',
              href: '/dashboard/courses',
            },
          ]}
        />
      </div>

      {!isLoading &&
        hasCourses !== null &&
        (hasCourses ? (
          <CourseListPage />
        ) : (
          <>
            <h1 className="text-[28px] font-semibold leading-[36px] text-neutral-900 mb-6">
              Add a New Course
            </h1>
            <CourseForm />
          </>
        ))}
    </>
  );
}
