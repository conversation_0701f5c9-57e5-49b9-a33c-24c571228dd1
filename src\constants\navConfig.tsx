import { DashboardHomeIcon } from '@/assets/images/dashboard';
import CourseImage from '@/assets/images/dashboard/book-open.svg';
import Image from 'next/image';
import CareersIcon from '@/components/icons/careers-icon';
import FinancialBenefitIcon from '@/components/icons/financial-benefit';

export const navConfig = [
  {
    id: 'benefits',
    name: 'Benefits',
    path: '/benefits',
    children: [
      {
        id: 'resume-builder',
        name: 'Resume Builder',
        path: '/services/resume-builder',
      },
      {
        id: 'career-advice',
        name: 'Career Advice',
        path: '/services/career-advice',
      },
    ],
  },
  {
    id: 'partners',
    name: 'Our Partners',
    path: '/our-partners',
    children: [
      {
        id: 'resume-builder',
        name: 'Resume Builder',
        path: '/services/resume-builder',
      },
      {
        id: 'career-advice',
        name: 'Career Advice',
        path: '/services/career-advice',
      },
    ],
  },
  {
    id: 'about',
    name: 'About',
    path: '/about',
  },
  {
    id: 'faqs',
    name: 'FAQ<PERSON>',
    path: '/faqs',
  },
  {
    id: 'contact',
    name: 'Contact Us',
    path: '/contact',
  },
];

export const dashboardSideBarRoutes = [
  {
    label: 'Home',
    icon: (
      <Image
        src={DashboardHomeIcon}
        alt="BriefCase"
        className="w-5 h-5 text-white"
      />
    ),
    route: '/dashboard',
  },
  {
    label: 'Careers',
    icon: <CareersIcon />,
    children: [
      {
        label: 'Add a New Listing',
        route: '/dashboard/jobs-and-training/posting-a-new-listing',
      },
      {
        label: 'My Listings',
        route: '/dashboard/jobs-and-training/my-listings',
      },
      {
        label: 'Applicants',
        route: '/dashboard/jobs-and-training/applicants',
      },
    ],
  },
  {
    label: 'Courses',
    icon: (
      <Image src={CourseImage} alt="BriefCase" className="w-5 h-5 text-white" />
    ),
    route: '/dashboard/courses',
  },
  {
    label: 'Financial Benefits',
    icon: <FinancialBenefitIcon />,
    children: [
      {
        label: 'Eligible Benefits',
        route: '/dashboard/financial-benefits/eligible',
      },
      {
        label: 'Benefits Payments',
        route: '/dashboard/financial-benefits/payments',
      },
    ],
  },
];

export const ExploreJobsTrendTab = ['Trends', 'Occupations', 'Education'];
