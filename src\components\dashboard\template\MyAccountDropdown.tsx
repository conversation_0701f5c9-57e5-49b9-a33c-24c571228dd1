import Image from 'next/image';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { usePartnerProfile } from '@/queries';
import { LogOut, Check, User2 } from 'lucide-react';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { Button } from '@/components/ui/button';
import ManageCompanyIcon from '@/components/icons/ManageCompanyIcon';
import InactiveModules from '@/components/modals/inactive_module';
import PlusIcon from '@/components/icons/plus';

function MyAccountDropdown() {
  const { data: partnerData } = usePartnerProfile();
  const company = partnerData?.data?.items[0] ?? [];
  const logout = useAuthStore((state) => state.logout);
  const router = useRouter();
  const [showAccounts, setShowAccounts] = useState(false);
  const availableAccounts = [
    {
      id: 1,
      name: company?.partner?.name || 'Main Account',
      logo: company?.partner?.logo,
    },
    { id: 2, name: 'Team Account', logo: null },
  ];
  const [showInactiveModal, setShowInactiveModal] = useState(false);

  const handleProfile = () => {
    router.push('/dashboard/profile');
  };
  const handleCareerProfile = () => {
    router.push('/dashboard/company');
  };
  const toggleAccounts = () => {
    setShowAccounts(!showAccounts);
  };
  return (
    <div className="absolute right-0 mt-2 w-[320px] bg-white rounded-xl shadow-lg py-3 px-0 text-black border border-gray-200 z-50 flex flex-col">
      <div className="flex flex-col space-y-2">
        <div className="mx-auto justify-center items-center w-full px-4">
          <div className="flex flex-col space-y-2">
            <div className="w-12 h-12 rounded-lg bg-[--topBarBgColor] !text-white flex items-center justify-center font-semibold mx-auto">
              <p className="text-[24px] !text-white">
                {' '}
                {company?.partner?.name?.charAt(0).toUpperCase() || 'N'}
              </p>
            </div>
            <p className="text-center text-[20px] font-semibold leading-[28px] text-primary-500 capitalize">
              {' '}
              {company?.partner?.name || 'N/A'}
            </p>
          </div>
          <Button
            variant={'outline'}
            className="mx-auto justify-center items-center text-center w-full mt-2 mb-2"
            onClick={toggleAccounts}
          >
            <ManageCompanyIcon />
            Manage Company
          </Button>
        </div>
        <div>
          {showAccounts && (
            <div className="mb-2 my-2 pt-2 border-y space-y-2 py-3">
              <p className="px-4 uppercase text-[14px] font-medium text-neutral-500">
                {' '}
                Switch Company{' '}
              </p>
              {availableAccounts.map((account) => (
                <button
                  key={account.id}
                  className="flex items-center w-full px-4 py-2 text-[14px] hover:!text-white transition-colors gap-2"
                  onClick={handleProfile}
                >
                  <div className="flex items-center">
                    {account.logo ? (
                      <Image
                        src={account.logo}
                        width={24}
                        height={24}
                        alt="Account"
                        className="rounded-md object-contain mr-2"
                      />
                    ) : (
                      <div className="w-6 h-6 rounded-md bg-primary-500 !text-white flex items-center justify-center font-semibold text-xs mr-2">
                        {account.name.charAt(0).toUpperCase()}
                      </div>
                    )}
                    <p className="text-[16px] capitalize">{account.name}</p>
                  </div>
                  {account.name === company?.partner?.name && (
                    <Check className="h-5 w-5 text-primary-500" />
                  )}
                </button>
              ))}
              <Button
                variant={'ghost'}
                onClick={() => setShowInactiveModal(true)}
                className="flex px-4 gap-2 hover:bg-transparent"
              >
                <PlusIcon />
                <p className="text-[16px] font-normal text-neutral-900 leading-[24px]">
                  {' '}
                  Add a New Company
                </p>
              </Button>
            </div>
          )}

          <button
            onClick={handleCareerProfile}
            className="flex items-center text-neutral-900 w-full px-4 py-2 text-[16px] hover:bg-primary-500 hover:text-white transition-colors gap-2 font-normal"
          >
            <User2 />
            My Profile
          </button>

          <button
            onClick={logout}
            className="flex items-center w-full px-4 py-2 text-[16px] text-destructive-500 hover:bg-primary-500 hover:text-white transition-colors gap-2"
          >
            <LogOut className="h-5 w-5" />
            Logout
          </button>
        </div>
      </div>

      {showInactiveModal && <InactiveModules />}
    </div>
  );
}

export default MyAccountDropdown;
