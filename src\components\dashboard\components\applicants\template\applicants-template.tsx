import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import React from 'react';
import Link from 'next/link';
import ApplicantListingTable from '../components/applicantListTable';

function ApplicantsTemplate() {
  return (
    <div className="space-y-4">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Applicants',
            href: '/dashboard/jobs-and-training/applicants',
          },
        ]}
      />
      <div className="space-y-4 leading-[30px]">
        <h4 className="text-neutral-900 font-semibold text-[40px] mt-10">
          Applicants
        </h4>
        <p className="text-neutral-700 text-[18px] font-normal">
          Track and review candidates for your job, apprenticeship, and training
          opportunities.
        </p>
      </div>
      <ApplicantListingTable />

      <div className="text-[20px] text-neutral-500 flex items-center gap-2">
        <p className=" text-neutral-900 font-normal leading-[16px]">
          {' '}
          Need assistance?
        </p>
        <Link href="" className="text-neutral-500 font-medium">
          Contact Support
        </Link>
      </div>
    </div>
  );
}

export default ApplicantsTemplate;
