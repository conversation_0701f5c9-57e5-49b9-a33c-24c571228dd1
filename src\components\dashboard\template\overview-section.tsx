import React from 'react';
import { OverViewCard } from '../components/over-view-card';
import FolderIcon from '@/components/icons/folder';
import ClipboardListIcon from '@/components/icons/clipboard';
import { useOverviewStats } from '@/queries';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { PlusIcon } from 'lucide-react';
import ArrowIcon from '@/components/icons/ArrowRight';
import useSettingsStore from '@/zustand/store/settingsStore';

function OverviewSection() {
  const { appearanceSettings } = useSettingsStore();
  const router = useRouter();
  const { data, isLoading } = useOverviewStats();
  const brandColor = appearanceSettings?.brandColor;

  return (
    <div className="space-y-3">
      <h4 className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
        Career
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <OverViewCard
          icon={<FolderIcon stopColor={brandColor} />}
          title={data?.data?.totalActiveJobs || 0}
          description={isLoading ? 'Loading' : 'Active Listings'}
          arrowIcon={<ArrowIcon />}
          route="/dashboard/jobs-and-training/my-listings"
        />
        <OverViewCard
          icon={<ClipboardListIcon stopColor={brandColor} />}
          title={data?.data?.totalNewApplicants || 0}
          description={isLoading ? 'Loading' : 'New Applicants'}
          arrowIcon={<ArrowIcon />}
        />
        <Card
          onClick={() =>
            router.push('/dashboard/jobs-and-training/posting-a-new-listing')
          }
          className="flex flex-col items-center justify-center rounded-lg px-[32px] py-[24px] gap-2 border-2 border-primary-500 hover:border-neutral-500 cursor-pointer hover:bg-neutral-50 transition-colors h-[140px]"
        >
          <PlusIcon
            size={38}
            className="w-[48px] h-[48px] flex-shrink-0 text-primary-500"
          />
          <p className="text-center text-primary-500 text-[18px] font-semibold leading-[20px]">
            Add a New Listing
          </p>
        </Card>
      </div>
    </div>
  );
}

export default OverviewSection;
