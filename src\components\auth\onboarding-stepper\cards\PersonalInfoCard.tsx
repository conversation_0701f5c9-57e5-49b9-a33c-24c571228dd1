import InputField from '@/components/common/InputField';
import { Form, Formik } from 'formik';
// import { ErrorMessage } from 'formik'
import React from 'react';

const PersonalInfoCard = () => {
  return (
    <Formik
      initialValues={{ first_name: '', last_name: '', email: '' }}
      onSubmit={async (values) => {
        // eslint-disable-next-line no-console
        console.log(values);
      }}
    >
      {({ handleChange, handleBlur, values, errors }) => (
        <div className="bg-white text-left py-6">
          <Form className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <InputField
                label="First Name:"
                name={'first_name'}
                type={'text'}
                placeholder="John"
                value={values.first_name}
                handleChange={handleChange}
                handleBlur={handleBlur}
              />
            </div>
            <div className="space-y-1">
              <InputField
                label="Last Name:"
                name={'last_name'}
                type={'text'}
                placeholder="smith"
                value={values.last_name}
                handleChange={handleChange}
                handleBlur={handleBlur}
              />
            </div>
            <div className="mb-3 space-y-1 col-span-2">
              <InputField
                label="Work Email"
                name={'email'}
                type={'email'}
                error={errors.email}
                placeholder="Enter your work email"
                value={values.email}
                handleChange={handleChange}
                handleBlur={handleBlur}
              />
            </div>
          </Form>
        </div>
      )}
    </Formik>
  );
};

export default PersonalInfoCard;
