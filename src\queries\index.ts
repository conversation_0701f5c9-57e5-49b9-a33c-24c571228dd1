import companyService from '@/services/company';
import coursesService from '@/services/courses/index.service';
import overviewService from '@/services/overview/overview.service';
import adminValuesService from '@/services/settings/adminValues.service';
import userService from '@/services/user/user.service';
import type {
  ApiRepresentative,
  getAdminValuesProps,
  InviteRepresentativePayload,
  UpdateUserProfileData,
} from '@/type';
import type { CourseFilters } from '@/types/courseType';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

const useAdminValues = (params: Partial<getAdminValuesProps> = {}) => {
  return useQuery({
    queryKey: ['adminValues', params],
    queryFn: () => adminValuesService.getAll(params),
  });
};

const useOverviewStats = () => {
  const userId = useAuthStore((state) => state.user?.id);

  return useQuery({
    queryKey: ['stats', userId],
    queryFn: () => overviewService.getStats(userId),
  });
};
const usePartnerProfile = () => {
  const userId = useAuthStore((state) => state.user?.id);

  return useQuery({
    queryKey: ['profile', userId],
    queryFn: () => userService.getPartnerProfile(userId as string),
  });
};

export const usePartnersById = (id: string) => {
  return useQuery({
    queryKey: ['partnerId', id],
    queryFn: () => companyService.getPartnerById(id),
  });
};

const useCourses = (params: Partial<CourseFilters>) => {
  return useQuery({
    queryKey: ['courses', params],
    queryFn: () => coursesService.all(params),
  });
};

const useInviteRepresentative = () => {
  const { showNotification } = useNotificationStore();

  return useMutation({
    mutationFn: (data: InviteRepresentativePayload) =>
      companyService.inviteRepresentative(data),
    onSuccess: () => {
      showNotification('Invitation sent successfully!', 'success');
    },
  });
};

const useGetRepresentative = () => {
  return useQuery<ApiRepresentative[]>({
    queryKey: ['representative'],
    queryFn: companyService.getRepresentative,
  });
};

const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateUserProfileData) =>
      userService.updateUserProfile(data.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['representative'] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

const useDeleteUserProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => userService.deleteUserProfile(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

export const useToggleStatus = () => {
  return useMutation({
    mutationFn: (userId: string) => companyService.statusUpdate(userId),
  });
};

export {
  useAdminValues,
  useOverviewStats,
  usePartnerProfile,
  useCourses,
  useInviteRepresentative,
  useGetRepresentative,
  useUpdateUserProfile,
  useDeleteUserProfile,
};
