'use client';

import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
// import ApplicantListingSkeleton from '@/components/common/applicant-listing-skeleton'
import BulkActionsToolbar from './bulkAction';
import ApplicantTable from './applicantTable';
import Tabs from './tabs';
import { PaginationControls } from '../../my-listings/components/template/pagination-controller';
import FilterPanel from './filterPanel';
import { useApplicantListing } from '@/hooks/useApplicantListing';

export default function ApplicantListingTable() {
  const router = useRouter();
  const { user } = useAuthStore();

  // const [localLoading, setLocalLoading] = useState(false);

  const {
    state: {
      applicants,
      total,
      isLoading,
      isError,
      allApplicantIdsData,
      statusCounts,
      activeTab,
      page,
      totalPages,
      sortField,
      ascending,
      search,
      filters,
      selectedIds,
    },
    actions: {
      handleSort,
      handleApplyFilters,
      handleResetFilters,
      handleTabChange,
      handlePageChange,
      handleBulkAction,
      handleRemoveFromSelection,
      updateStatus,
      updateState,
    },
  } = useApplicantListing(user?.id || null);

  // if (isLoading || localLoading) {
  //   return <ApplicantListingSkeleton />
  // }

  if (isError) {
    return <div>Error fetching applicants. Please try again later.</div>;
  }

  return (
    <div className="bg-white border border-neutral-200 rounded-lg p-4 space-y-4">
      <Tabs
        activeTab={activeTab}
        onTabChange={handleTabChange}
        counts={statusCounts}
      />

      <FilterPanel
        search={search}
        filters={filters}
        onSearchChange={(value) => updateState({ search: value })}
        onApplyFilters={handleApplyFilters}
        onResetFilters={handleResetFilters}
      />

      {isLoading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-neutral-500">Loading applicants...</p>
        </div>
      ) : (
        <>
          <ApplicantTable
            key={`${activeTab}-${page}`}
            applicants={applicants}
            selectedIds={selectedIds}
            sortField={sortField}
            ascending={ascending}
            handleSort={handleSort}
            onSelect={(ids) => updateState({ selectedIds: ids })}
            onRowClick={(id) =>
              router.push(`/dashboard/jobs-and-training/applicants/${id}`)
            }
            updateApplicantStatus={async (variables) => {
              await updateStatus(variables);
            }}
            status={activeTab}
            totalApplicantsCount={total}
            allApplicantIds={allApplicantIdsData}
          />

          {total > 0 && (
            <PaginationControls
              page={page}
              totalPages={totalPages}
              handlePageChange={handlePageChange}
            />
          )}
        </>
      )}

      <BulkActionsToolbar
        selectedIds={selectedIds}
        applicants={applicants}
        activeTab={activeTab}
        onBulkAction={handleBulkAction}
        onClearSelection={() => updateState({ selectedIds: [] })}
        onRemoveFromSelection={handleRemoveFromSelection}
      />
    </div>
  );
}
