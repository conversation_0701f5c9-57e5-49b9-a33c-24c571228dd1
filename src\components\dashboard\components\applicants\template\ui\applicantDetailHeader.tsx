import { Button } from '@/components/ui/button';
import type { Applicant } from '@/type';

import DotLoader from '@/components/dashboard/common/loader/dot-loader';
import LocationIcon from '@/components/icons/locationIcon';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import useSettingsStore from '@/zustand/store/settingsStore';

interface ApplicantDetailHeaderProps {
  applicant: Applicant;
  currentIndex: number;
  total: number;
  navigateToApplicant: (_direction: 'next' | 'prev') => void;
  isNavigating: boolean;
  isLoadingApplicant: boolean;
}

export default function ApplicantDetailHeader({
  applicant,
  currentIndex,
  total,
  navigateToApplicant,
  isNavigating,
  isLoadingApplicant,
}: ApplicantDetailHeaderProps) {
  const { appearanceSettings } = useSettingsStore();
  const { brandColor } = appearanceSettings;
  return (
    <div className="bg-white">
      <div className="flex items-start justify-between">
        <div className="space-y-3">
          <h1 className="text-[28px] font-semibold text-neutral-700 leading-[36px]">
            {applicant.user?.userName || 'N/A'}
          </h1>
          <div className="flex items-center space-x-2">
            <LocationIcon stroke={brandColor} />
            <span className="text-[16px] text-neutral-500 font-normal leading-6">
              {applicant?.job?.location}
            </span>
          </div>
        </div>

        <div className="flex space-x-3 items-center">
          {isLoadingApplicant ? (
            <DotLoader />
          ) : (
            <span className="text-[16px] text-neutral-500 font-normal leading-[24px]">
              {currentIndex + 1} of {total}
            </span>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full w-[40px] h-[40px] bg-[--buttonColorLight] hover:bg-[--buttonColorLight]"
            onClick={() => navigateToApplicant('prev')}
            disabled={isNavigating || isLoadingApplicant}
          >
            <ChevronLeft className="w-4 h-4 text-[--buttonColor]" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full w-[40px] h-[40px] bg-[--buttonColorLight] hover:bg-[--buttonColorLight]"
            onClick={() => navigateToApplicant('next')}
            disabled={isNavigating || isLoadingApplicant}
          >
            <ChevronRight className="w-4 h-4 text-[--buttonColor]" />
          </Button>
        </div>
      </div>
    </div>
  );
}
