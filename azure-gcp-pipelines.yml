# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  branches:
    include: 
    - main

pool:
  vmImage: ubuntu-latest

steps:
  - script: 'echo Current Branch is $(Build.SourceBranchName)'
    displayName: Current Branch

  - script: ls -a
    displayName: Get List of Files

  - script: |
          echo "Setting up environment for $(Build.SourceBranch)"
          if [ "${BUILD_SOURCEBRANCH}" == "refs/heads/main" ]; then
            echo "##vso[task.setvariable variable=VERSION]production"
          fi
          echo "##vso[task.setvariable variable=BUILD_ID]$(Build.BuildId)"
    displayName: 'Setting variables for GCP deployment'
  
  - script: 'echo Current stage to be deployed is $(VERSION)'
    displayName: Current Stage to be deployed

  - task: DownloadSecureFile@1
    inputs:
      secureFile: 'whiteshield-public-google-cloud-key.json'
    name: downloadKey

  # Authenticate with Google Cloud using the downloaded key file
  - script: |
      gcloud auth activate-service-account --key-file $(downloadKey.secureFilePath)
      gcloud config set project careernavigator-430608
    displayName: 'Authenticate with Google Cloud'
  - task: SonarCloudPrepare@3
    inputs:
      SonarQube: 'SonarQube Cloud Service Connection'
      organization: 'ws-sonar'
      scannerMode: 'cli'
      configMode: 'manual'
      cliProjectKey: 'whiteshieldTech_CareerNavigatorProPartnerUI'
      cliProjectName: 'CareerNavigatorProPartnerUI'
      cliSources: '.'
  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
    displayName: 'Install Node.js'
  - task: CmdLine@2
    displayName: 'npm install and build'
    inputs:
      script: |
        npm install
        npm run build
  - task: SonarCloudAnalyze@3
    inputs:
      jdkversion: 'JAVA_HOME_17_X64'
  - task: SonarCloudPublish@3
    inputs:
      pollingTimeoutSec: '300'
  - script: |
      gcloud app deploy app.$(VERSION).yaml --quiet --version $(VERSION)-$(BUILD_ID)
    displayName: 'Deploy to Google App Engine'