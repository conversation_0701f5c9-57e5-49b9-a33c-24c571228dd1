'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Filter, Search } from 'lucide-react';

type SearchFilterProps = {
  search: string;
  setSearch: (_value: string) => void;
  setShowFilterModal: (_show: boolean) => void;
};

export function SearchFilter({
  search,
  setSearch,
  setShowFilterModal,
}: SearchFilterProps) {
  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center w-[300px] border border-neutral-200 px-2 rounded-md">
        <Search className="w-6 h-6 text-neutral-200" />
        <Input
          placeholder="Search listing..."
          className="border-none focus:border-none ring-0 outline-none py-0"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>
      <Button variant="outline" onClick={() => setShowFilterModal(true)}>
        <Filter className="w-4 h-4 mr-2" />
        Filter
      </Button>
    </div>
  );
}
