import { useState, useCallback, useMemo, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter, useSearchParams } from 'next/navigation';
import type { Job } from '@/type/jobType';
import { useJobs } from '@/queries/useJobQueries';
import { useJobStore } from '@/zustand/store/jobStore';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

interface JobFilters {
  search?: string;
  status?: string;
  listingType?: string;
  dateType?: 'createdOn' | 'closedDate';
  createdOnFrom?: string;
  createdOnTo?: string;
  closedDateFrom?: string;
  closedDateTo?: string;
  userId?: string;
  ascending?: boolean;
  sortField?: string;
}

interface JobResponse {
  data: Job[];
  page: number;
  size: number;
  total: number;
  statusCounts?: {
    Active: number;
    Closed: number;
  };
}

export const useMyListings = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const { user } = useAuthStore();

  const {
    page,
    size,
    setPage,
    setSearchTerm,
    searchTerm,
    ascending,
    sortField,
    setSortField,
    setAscending,
    setStatus,
  } = useJobStore();

  const initialStatus =
    searchParams.get('status') === 'Closed' ? 'Closed' : 'Active';
  const [activeTab, setActiveTab] = useState<'active' | 'closed'>(
    initialStatus === 'Closed' ? 'closed' : 'active'
  );
  const [localSearch, setLocalSearch] = useState(searchTerm || '');
  const [error, setError] = useState<string | null>(null);

  const [uiFilters, setUiFilters] = useState<JobFilters>({
    search: searchTerm,
    status: initialStatus,
    ascending,
    sortField,
    userId: user?.id,
  });

  const [appliedFilters, setAppliedFilters] = useState<JobFilters>({
    ...uiFilters,
    userId: user?.id,
  });

  // Update filters when user is loaded
  useEffect(() => {
    if (user?.id) {
      setUiFilters((prev) => ({ ...prev, userId: user.id }));
      setAppliedFilters((prev) => ({ ...prev, userId: user.id }));
    }
  }, [user?.id]);

  const {
    data,
    isLoading,
    error: queryError,
    refetch,
  } = useJobs<JobResponse>(appliedFilters);

  const jobs = useMemo(() => data?.data || [], [data]);
  const total = data?.total || 0;
  const totalActive = data?.statusCounts?.Active || 0;
  const totalClosed = data?.statusCounts?.Closed || 0;
  const totalPages = size > 0 ? Math.ceil(total / size) : 1;

  const handleTabChange = useCallback(
    (tab: 'active' | 'closed') => {
      setActiveTab(tab);
      const status = tab === 'active' ? 'Active' : 'Closed';
      setStatus(status);

      const updatedFilters = {
        ...uiFilters,
        status,
        search: localSearch,
        userId: user?.id,
      };

      setUiFilters(updatedFilters);
      setAppliedFilters(updatedFilters);
      setSearchTerm(localSearch);
      setPage(1);
    },
    [uiFilters, localSearch, setStatus, setSearchTerm, setPage, user?.id]
  );

  const handleSort = useCallback(
    (field: string) => {
      const isDateField = ['startDate', 'expiryDate'].includes(field);
      const newAscending =
        sortField === field ? !ascending : isDateField ? false : true;

      setSortField(field);
      setAscending(newAscending);

      const updatedFilters = {
        ...appliedFilters,
        sortField: field,
        ascending: newAscending,
        userId: user?.id,
      };

      setAppliedFilters(updatedFilters);
      setPage(1);
    },
    [
      appliedFilters,
      ascending,
      sortField,
      setSortField,
      setAscending,
      setPage,
      user?.id,
    ]
  );

  const handleFilterChange = useCallback(
    (newFilters: JobFilters) => {
      setUiFilters((prev) => ({
        ...prev,
        ...newFilters,
        search: localSearch,
        status: activeTab === 'active' ? 'Active' : 'Closed',
        userId: user?.id,
      }));
    },
    [activeTab, localSearch, user?.id]
  );

  const handleApplyFilters = useCallback(() => {
    const newFilters = {
      ...uiFilters,
      search: localSearch,
      status: activeTab === 'active' ? 'Active' : 'Closed',
      userId: user?.id,
    };

    setAppliedFilters(newFilters);
    setSearchTerm(localSearch);
    setPage(1);
  }, [uiFilters, localSearch, activeTab, setSearchTerm, setPage, user?.id]);

  const handleResetFilters = useCallback(() => {
    const resetFilters: JobFilters = {
      search: '',
      status: activeTab === 'active' ? 'Active' : 'Closed',
      listingType: undefined,
      dateType: undefined,
      createdOnFrom: undefined,
      createdOnTo: undefined,
      closedDateFrom: undefined,
      closedDateTo: undefined,
      ascending,
      sortField,
      userId: user?.id,
    };

    setUiFilters(resetFilters);
    setAppliedFilters(resetFilters);
    setLocalSearch('');
    setSearchTerm('');
    setPage(1);

    const params = new URLSearchParams(window.location.search);
    params.delete('status');
    router.push(
      `/dashboard/jobs-and-training/my-listings?${params.toString()}`
    );
  }, [
    activeTab,
    ascending,
    sortField,
    router,
    setPage,
    setSearchTerm,
    user?.id,
  ]);

  const handlePageChange = useCallback(
    (newPage: number) => {
      setPage(Math.max(1, Math.min(newPage, totalPages)));
    },
    [setPage, totalPages]
  );

  const handleRetry = useCallback(async () => {
    try {
      await refetch();
    } catch (err) {
      setError('Failed to fetch job listings. Please try again later.');
      console.error('Error fetching jobs:', err);
    }
  }, [refetch]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setLocalSearch(e.target.value);
    },
    []
  );

  const handleSearchKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        handleApplyFilters();
      }
    },
    [handleApplyFilters]
  );

  const refreshJobs = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['jobs'] });
  }, [queryClient]);

  useEffect(() => {
    if (queryError && !error) {
      setError('Failed to fetch job listings. Please try again later.');
      console.error('Error fetching jobs:', queryError);
    }
  }, [queryError, error]);

  return {
    state: {
      activeTab,
      localSearch,
      jobs,
      total,
      totalActive,
      totalClosed,
      totalPages,
      page,
      size,
      sortField,
      ascending,
      uiFilters,
      isLoading,
      error,
    },
    actions: {
      setActiveTab: handleTabChange,
      handleSort,
      handleFilterChange,
      handleApplyFilters,
      handleResetFilters,
      handlePageChange,
      handleRetry,
      handleSearchChange,
      handleSearchKeyDown,
      refreshJobs,
    },
  };
};
