import React from 'react';

interface PotentialCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function PotentialCard({ title, icon, description }: PotentialCardProps) {
  return (
    <div className="bg-white py-4 text-left">
      <div className="bg-[#F3F5F7] rounded-full p-4 w-fit mb-5">{icon}</div>

      <h3 className="text-[16px] leading-[28px] font-semibold">{title}</h3>
      <p className="text-[13px] leading-[24px] font-normal mt-2 text-neutral-500">
        {description}
      </p>
    </div>
  );
}

export default PotentialCard;
