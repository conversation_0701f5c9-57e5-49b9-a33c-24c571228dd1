export const formatPostedDate = (postedDate?: string) => {
  if (!postedDate) return 'Posted date not available';

  const postedTime = new Date(postedDate).getTime(); // Parses UTC correctly
  const now = Date.now(); // Local timezone

  const diffInMs = now - postedTime;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays >= 1) {
    return `Posted ${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  if (diffInHours >= 1) {
    return `Posted ${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  if (diffInMinutes >= 1) {
    return `Posted ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }

  return 'Posted just now';
};
