/* eslint-disable no-unused-vars */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { Permission } from '@/types/permission';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import permissionService from '../services/permission.service';

interface PermissionState {
  // State
  permissions: Permission[];
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchPermissions: () => Promise<void>;
  hasPermission: (
    permission: Permission | Permission[],
    options?: { all?: boolean }
  ) => boolean;
  clearPermissions: () => void;
}

export const usePermissionStore = create<PermissionState>()(
  persist(
    (set, get) => ({
      permissions: [],
      isLoading: false,
      error: null,

      fetchPermissions: async () => {
        set({ isLoading: true, error: null });
        try {
          const permissions = await permissionService.fetchUserPermissions();
          set({ permissions, isLoading: false });
        } catch (error) {
          const { showNotification } = useNotificationStore.getState();
          const errorMessage =
            error instanceof Error
              ? error.message
              : 'Failed to fetch permissions';

          set({
            error: errorMessage,
            isLoading: false,
          });

          // Show notification for permission fetch errors
          showNotification(
            'Failed to load permissions. Some features may be restricted.',
            'error'
          );
        }
      },

      hasPermission: (permission, options = {}) => {
        const { permissions } = get();
        const { all = false } = options;

        if (Array.isArray(permission)) {
          if (all) {
            // User must have ALL specified permissions
            return permission.every((p) => permissions.includes(p));
          } else {
            // User must have ANY of the specified permissions
            return permission.some((p) => permissions.includes(p));
          }
        }

        // Single permission check
        return permissions.includes(permission);
      },

      clearPermissions: () => {
        set({ permissions: [], error: null });
      },
    }),
    {
      name: 'permission-storage',
      partialize: (state) => ({ permissions: state.permissions }),
    }
  )
);
