/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Course, CourseFilters } from '@/types/courseType';
import { Plus } from 'lucide-react';
import { CourseFilterPanel } from './courseFilter';
import { CourseTable } from './table';

import { Card } from '@/components/ui/card';
import { useCourses } from '@/queries';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { StatusCountBadge } from '@/utils';
import { PaginationControls } from '../../my-listings/components/template/pagination-controller';

// Custom Badge component
export const CourseListPage: React.FC = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('Active');
  const userId = useAuthStore((state) => state.user?.id);

  const [filters, setFilters] = useState<CourseFilters>({
    status: 'Active',
    search: undefined,
    mainTopic: undefined,
    deliveryMode: undefined,
    city: undefined,
    experienceLevel: undefined,
    startDate: undefined,
    endDate: undefined,
    createdOn: undefined,
    modifiedOn: undefined,
    sortField: undefined,
    page: 1,
    pageSize: 10,
    ascending: undefined,
    createdById: userId,
  });

  const [applyFilter, setApplyFilter] = useState<CourseFilters>({
    status: 'Active',
    page: 1,
    pageSize: 10,
    createdById: userId,
  });

  const { data: myCourses, isLoading } = useCourses(applyFilter);

  const totalPages = Math.ceil(myCourses?.total / myCourses?.pageSize);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setFilters((prev) => ({
      ...prev,
      status: value as any,
      page: 1,
    }));
    setApplyFilter((prev) => ({
      ...prev,
      status: value as any,
      page: 1,
    }));
  };

  const handleFilterChange = (newFilters: CourseFilters) => {
    setFilters({
      ...newFilters,
      status: filters.status,
      page: 1,
      createdById: userId,
    });
  };

  const handleApplyFilters = (appliedFilters?: CourseFilters) => {
    // const dateType = filters.dateType
    const updatedFilters = {
      ...filters,
      createdById: userId,
      page: 1,
      ...appliedFilters,
    };

    // updatedFilters.startDate = undefined
    // updatedFilters.endDate = undefined
    // updatedFilters.createdOn = undefined
    // updatedFilters.modifiedOn = undefined

    // if (dateType === 'startDate') {
    //   updatedFilters.startDate = filters.dateFrom
    //   updatedFilters.endDate = filters.dateTo
    // } else if (dateType === 'createdOn') {
    //   updatedFilters.createdOn = filters.dateFrom
    //   if (filters.dateTo) {
    //     updatedFilters.createdOn = filters.dateTo
    //   }
    // } else if (dateType === 'modifiedOn') {
    //   updatedFilters.modifiedOn = filters.dateFrom
    //   if (filters.dateTo) {
    //     updatedFilters.modifiedOn = filters.dateTo
    //   }
    // }

    setApplyFilter(updatedFilters);
  };

  const handleResetFilters = () => {
    setFilters({
      status: filters.status,
      page: 1,
      createdById: userId,
    });
    setApplyFilter({
      status: filters.status,
      page: 1,
      createdById: userId,
    });
  };

  const handleSort = (field: string, ascending: boolean) => {
    setApplyFilter((prev) => ({
      ...prev,
      sortField: field,
      ascending,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setApplyFilter((prev) => ({
      ...prev,
      page,
    }));
  };

  const handleAddCourse = () => {
    router.push('/dashboard/courses/add-a-new-course');
  };

  const handleEditCourse = (course: Course) => {
    router.push(`/dashboard/courses/${course.id}/edit`);
  };

  const handleDeleteCourse = (courseId: string) => {
    console.warn('Delete course:', courseId);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-[40px] font-semibold text-neutral-900 leading-[48px]">
          My Courses
        </h1>
        <Button
          onClick={handleAddCourse}
          className="flex items-center gap-2 px-8"
        >
          <Plus className="h-4 w-4" />
          Add a New Course
        </Button>
      </div>

      <Card className="px-4 py-4 rounded-lg">
        <Tabs
          defaultValue="Active"
          value={activeTab}
          onValueChange={handleTabChange}
          className="mt-6"
        >
          <TabsList className="mb-4 bg-transparent gap-10">
            <TabsTrigger
              value="Active"
              className={
                activeTab === 'Active'
                  ? 'border-2 border-primary-500 text-primary-500'
                  : 'bg-white text-neutral-500 border-neutral-300'
              }
            >
              Active
              <StatusCountBadge
                count={myCourses?.statusCounts?.Active ?? 0}
                isActive={activeTab === 'Active'}
              />
            </TabsTrigger>
            <TabsTrigger
              value="Pending"
              className={
                activeTab === 'Pending'
                  ? 'border-2 border-primary-500 text-primary-500'
                  : 'bg-white border-neutral-300'
              }
            >
              Pending Approval
              <StatusCountBadge
                count={myCourses?.statusCounts?.Pending ?? 0}
                isActive={activeTab === 'Pending'}
              />
            </TabsTrigger>
            <TabsTrigger
              value="Draft"
              className={
                activeTab === 'Draft'
                  ? 'border-2 border-primary-500 text-primary-500'
                  : 'bg-white border-neutral-300'
              }
            >
              Drafts
              <StatusCountBadge
                count={myCourses?.statusCounts?.Draft ?? 0}
                isActive={activeTab === 'Draft'}
              />
            </TabsTrigger>
            <TabsTrigger
              value="Inactive"
              className={
                activeTab === 'Inactive'
                  ? 'border-2 border-primary-500 text-primary-500'
                  : 'bg-white  border-neutral-300'
              }
            >
              Inactive
              <StatusCountBadge
                count={myCourses?.statusCounts?.Inactive ?? 0}
                isActive={activeTab === 'Inactive'}
              />
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-6">
            <div className="">
              <p className="text-[16px] font-medium mb-4">Filter:</p>
              <CourseFilterPanel
                filters={filters}
                onFilterChange={handleFilterChange}
                onApplyFilters={handleApplyFilters}
                onResetFilters={handleResetFilters}
              />
            </div>

            <div className="mt-10 overflow-hidden">
              {isLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 mx-auto"></div>
                  <p className="mt-4 text-neutral-500">Loading courses...</p>
                </div>
              ) : (
                <>
                  <CourseTable
                    courses={myCourses?.data || []}
                    onSort={handleSort}
                    sortField={applyFilter.sortField}
                    ascending={applyFilter.ascending}
                    onEdit={handleEditCourse}
                    onDelete={handleDeleteCourse}
                  />

                  <PaginationControls
                    page={myCourses?.page}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                  />
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};
