import React, { useState } from 'react';

interface SearchInputProps {
  onSearch: (_job: string, _location: string) => void;
}

const JobSearchInput: React.FC<SearchInputProps> = ({ onSearch }) => {
  const [job, setJob] = useState('');
  const [location, setLocation] = useState('');

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch(job.trim(), location.trim());
    }
  };

  return (
    <div className="relative rounded-full bg-gradient-to-r from-[#4568DC] to-[#B06AB3] p-[2px]">
      <div className="flex items-center w-full rounded-full bg-white px-4 py-2">
        <input
          type="text"
          className="p-2 flex-1 focus:outline-none focus:ring-0 border-none"
          placeholder="Job title, skills, or company"
          value={job}
          onChange={(e) => setJob(e.target.value)}
          onKeyDown={handleKeyPress}
        />

        {/* Divider */}
        <span className="px-2 text-gray-400">|</span>

        {/* Location Input */}
        <input
          type="text"
          className="p-2 flex-1 focus:outline-none focus:ring-0 border-none"
          placeholder="City, state, or remote"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          onKeyDown={handleKeyPress}
        />
      </div>
      {/* Job Input */}
    </div>
  );
};

export default JobSearchInput;
