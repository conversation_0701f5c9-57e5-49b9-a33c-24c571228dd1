'use client';

import React, { useState } from 'react';
// import { SectionHeader } from '../components/SectionHeader'
import { BenefitCard } from '../components/benefits/benefitCard';
import { ActionCard } from '../components/ActionCard';
import ClockIcon from '@/components/icons/ClockIcon';
import { DashboardImage3, DashboardImage1, DashboardImage2 } from '@/assets';
import { useRouter } from 'next/navigation';
import Inactive from '@/components/modals/inactive';
import useSettingsStore from '@/zustand/store/settingsStore';

const benefits = [
  {
    title: 'Family Support Allowance Scheme',
    description:
      "Get financial support to help you manage your family's essential living expenses.",
    amount: '$300 per month',
    banner: DashboardImage3,
  },
  {
    title: 'National Savings Program',
    description:
      'Start saving securely and grow your funds with government-backed benefits.',
    amount: '$100 per month',
    banner: DashboardImage1,
  },
  {
    title: 'Housing Assistance',
    description:
      "Get financial support to help you manage your family's essential living expenses.",
    amount: '$350 per month',
    banner: DashboardImage2,
  },
];

function EligibleBenefits() {
  const router = useRouter();
  const { appearanceSettings } = useSettingsStore();
  const [showModal, setShowModal] = useState(false);
  const brandColor = appearanceSettings?.brandColor;

  const handleBenefitClick = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const handleViewDetails = () => {
    router.push('/dashboard/financial-benefits/payments');
  };

  return (
    <div className="flex flex-col flex-grow">
      <h4 className="text-[24px] font-semibold text-neutral-900 leading-[24px] my-4">
        Access eligible benefits
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {benefits.map((benefit) => (
          <BenefitCard
            key={benefit.title}
            title={benefit.title}
            description={benefit.description}
            amount={benefit.amount}
            onClick={handleBenefitClick}
            onViewDetails={handleViewDetails}
            banner={benefit.banner}
          />
        ))}
        <ActionCard
          onClick={() => router.push('/dashboard/financial-benefits/payments')}
          icon={<ClockIcon stopColor1={brandColor} stopColor2={brandColor} />}
          title="View your benefits payments history"
          description="Track your upcoming financial benefits and review payment history."
        />
      </div>

      {showModal && <Inactive onClose={closeModal} />}
    </div>
  );
}

export default EligibleBenefits;
