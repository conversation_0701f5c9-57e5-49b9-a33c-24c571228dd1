import React from 'react';

interface CollapseButtonProps {
  width?: number;
  height?: number;
  fill?: string;
  onClick: () => void;
}

const CollapseButton: React.FC<CollapseButtonProps> = ({
  width = 48,
  height = 48,
  fill,
  onClick,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill={'none'}
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <rect width={width} height={height} rx="12" fill={fill} />
      <path
        d="M33.3333 12H14.6667C13.1939 12 12 13.1939 12 14.6667V33.3333C12 34.8061 13.1939 36 14.6667 36H33.3333C34.8061 36 36 34.8061 36 33.3333V14.6667C36 13.1939 34.8061 12 33.3333 12Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20 12V36M26.6667 20L30.6667 24L26.6667 28"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CollapseButton;
