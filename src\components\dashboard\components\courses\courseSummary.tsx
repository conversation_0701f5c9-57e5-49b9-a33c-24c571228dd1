'use client';

import type React from 'react';
import { useState } from 'react';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface CourseSummaryProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => Promise<void> | void;
  onContinueEditing: () => void;
  onSaveDraft: () => void;
}

export const CourseSummary: React.FC<CourseSummaryProps> = ({
  isOpen,
  onClose,
  onSubmit,
  onContinueEditing,
  onSaveDraft,
}) => {
  const { course, skillsCovered, startDates } = useCourseFormStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getExperienceLevelColor = () => {
    switch (course.experienceLevel) {
      case 'Beginner':
        return 'bg-success-100 text-neutral-900 hover:bg-success-100 rounded border-none text-[16px]';
      case 'Intermediate':
        return 'bg-warning-100 text-neutral-900 hover:bg-warning-100 rounded border-none text-[16px]';
      case 'Advanced':
        return 'bg-destructive-100 text-neutral-900 hover:bg-warning-200 rounded border-none text-[16px]';
      default:
        return 'bg-neutral-100 text-neutral-900 hover:bg-neutral-100 rounded border-none text-[16px]';
    }
  };

  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  // Convert learning objectives string to bullet points
  const parseLearningObjectives = (objectives: string) => {
    if (!objectives) return [];
    // Split by new lines and filter out empty lines
    return objectives.split('\n').filter((line) => line.trim() !== '');
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit();
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open && !isSubmitting) {
          onClose();
        }
      }}
    >
      <DialogContent className="max-w-[824px] h-[90vh] flex flex-col p-6">
        <DialogHeader className="">
          <DialogTitle className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Course Summary
          </DialogTitle>
          <DialogClose
            className="absolute right-4 top-4 h-8 w-8"
            disabled={isSubmitting}
          />
        </DialogHeader>

        {/* Main content area with scroll inside border */}
        <div className="flex-1 overflow-y-auto">
          <div className="space-y-4 border border-neutral-200 rounded-[12px] p-4 h-full overflow-y-auto max-h-[calc(100%-0.2rem)]">
            <div className="space-y-3">
              <h2 className="text-[28px] font-semibold text-neutral-900 leading-[24px]">
                {course.name || 'Untitled Course'}
              </h2>

              <h1 className="text-[24px] leading-[32px] font-semibold text-neutral-900">
                USD ${(Number(course.courseFee) || 0).toFixed(2)}
              </h1>

              <div className="flex items-center gap-4 flex-wrap  text-[16px]">
                {course.experienceLevel && (
                  <Badge
                    variant="outline"
                    className={`${getExperienceLevelColor()}`}
                  >
                    {course.experienceLevel}
                  </Badge>
                )}

                {course.deliveryMode && (
                  <Badge
                    variant="outline"
                    className="bg-neutral-100 text-neutral-900 hover:bg-neutral-100 rounded   text-[16px]"
                  >
                    {course.deliveryMode}
                  </Badge>
                )}

                {course.startDate && !course.isFlexible && (
                  <Badge
                    variant="outline"
                    className="bg-neutral-100 text-neutral-900 hover:bg-neutral-100 rounded  text-[16px]"
                  >
                    Next Start: {formatDateForDisplay(course.startDate)}
                  </Badge>
                )}

                {course.isFlexible && (
                  <Badge
                    variant="outline"
                    className="bg-neutral-100 text-neutral-900 hover:bg-neutral-100 rounded text-[16px]"
                  >
                    Flexible Start Date
                  </Badge>
                )}

                {!course.isSelfPaced &&
                  course.duration &&
                  course.durationUnit && (
                    <Badge
                      variant="outline"
                      className="bg-neutral-100 text-neutral-900 hover:bg-neutral-100 rounded text-[16px]"
                    >
                      {course.duration} {course.durationUnit}
                    </Badge>
                  )}

                {course.isSelfPaced && (
                  <Badge
                    variant="outline"
                    className="bg-neutral-100 text-neutral-900 hover:bg-neutral-100 rounded text-[16px]"
                  >
                    Self-paced
                  </Badge>
                )}
              </div>

              {(course.deliveryMode === 'In Person' ||
                course.deliveryMode === 'Hybrid') &&
                course.city && (
                  <div className="text-neutral-500 text-[16px] font-normal capitalize leading-[24px]">
                    Location: {course.city} {course.state || ''}{' '}
                    {course.country || ''}
                  </div>
                )}
            </div>

            <div className="space-y-3">
              <h3 className="text-[24px] font-semibold text-neutral-900">
                Description
              </h3>
              <p className="text-neutral-500 text-[16px] font-normal whitespace-pre-line">
                {course.description}
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="text-[24px] font-semibold text-neutral-900">
                Learning Objectives
              </h3>
              <ul className="space-y-2">
                {parseLearningObjectives(course.learningObjective).map(
                  (objective) => (
                    <li
                      key={objective}
                      className="text-neutral-500 text-[16px] font-normal"
                    >
                      {objective}
                    </li>
                  )
                )}
              </ul>
            </div>

            <div className="space-y-3">
              <h2 className="text-[24px] font-semibold text-neutral-900 leading-[32px]">
                Program Outline
              </h2>
              <Accordion
                type="multiple"
                // value={expandedSections}
                className="border border-neutral-100 bg-neutral-50 text-[16px]"
              >
                {course.programOutlines?.map((outline, index) => (
                  <AccordionItem
                    key={outline.key}
                    value={`section-${index}`}
                    className={`
                      p-4  data-[state=open]:bg-white 
                    ${
                      index !== course.programOutlines.length - 1
                        ? 'border-b'
                        : ''
                    }`}
                  >
                    <AccordionTrigger
                      className={`hover:no-underline bg-neutral-50 data-[state=open]:bg-white  data-[state=open]:border-0`}
                    >
                      <div className="text-left font-medium text-[16px] text-neutral-900 leading-[24px] capitalize">
                        {index + 1}: {outline.key}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="text-neutral-500 text-[14px] bg-white">
                      {outline.value}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>

            {!course.isFlexible && startDates.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-[24px] font-semibold text-neutral-900">
                  Additional Start Dates
                </h3>
                <ul className="">
                  {startDates.map((date) => (
                    <li
                      key={date}
                      className="text-neutral-500 text-[16px] font-normal"
                    >
                      {formatDateForDisplay(date)}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="space-y-3">
              <h3 className="text-[24px] font-semibold text-neutral-900">
                Course Skills
              </h3>
              <div className="flex flex-col gap-2">
                {skillsCovered.map((skill) => (
                  <Badge
                    key={skill.key}
                    variant="secondary"
                    className="text-[14px] rounded-md bg-neutral-100 text-neutral-900 flex items-center w-fit"
                  >
                    {skill.key}
                    {skill.value && (
                      <span className="text-[14px] text-neutral-500 ml-2">
                        ({skill.value})
                      </span>
                    )}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Fixed footer at the bottom */}
        <div className="shrink-0 px-6 py-4">
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={onContinueEditing}
              disabled={isSubmitting}
            >
              Continue Editing
            </Button>
            <Button
              variant="outline"
              onClick={onSaveDraft}
              disabled={isSubmitting}
            >
              Save Draft
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
