'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { Card, CardContent } from '@/components/ui/card';
import { PaginationControls } from '../../../my-listings/components/template/pagination-controller';

// Sample Data
const payments = [
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Jan 2025',
    status: 'Upcoming',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Jan 2025',
    status: 'Upcoming',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Dec 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Dec 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Nov 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Nov 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Oct 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Oct 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'Housing Assistance',
    amount: 'KWD 350',
    frequency: 'Monthly',
    paymentDate: '15 Sep 2024',
    status: 'Paid',
  },
  {
    reference: 'REF123456',
    benefit: 'National Savings Program',
    amount: 'KWD 100',
    frequency: 'Monthly',
    paymentDate: '15 Sep 2024',
    status: 'Paid',
  },
];

// Pagination Config
const itemsPerPage = 5;

const PaymentTable: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate the pagination
  const totalPages = Math.ceil(payments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedPayments = payments.slice(startIndex, endIndex);

  return (
    <Card className="p-4 shadow-md my-5">
      <CardContent>
        <Table>
          <TableHeader className="bg-[#F1F2F4] text-neutral-900 font-semibold text-[16px]">
            <TableRow>
              <TableHead>Reference</TableHead>
              <TableHead>Benefit</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Payment Date</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPayments.map((payment) => (
              <TableRow
                key={payment.reference}
                className="text-neutral-500 text-[16px] font-normal"
              >
                <TableCell>{payment.reference}</TableCell>
                <TableCell>{payment.benefit}</TableCell>
                <TableCell>{payment.amount}</TableCell>
                <TableCell>{payment.frequency}</TableCell>
                <TableCell>{payment.paymentDate}</TableCell>
                <TableCell
                  className={
                    payment.status === 'Upcoming'
                      ? 'text-green-600'
                      : 'text-neutral-600'
                  }
                >
                  {payment.status}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Pagination */}
        {totalPages > 0 && (
          <PaginationControls
            page={currentPage}
            totalPages={totalPages}
            handlePageChange={(page) => setCurrentPage(page)}
          />
        )}
        {/* <Pagination className="mt-4">
          <PaginationContent className="gap-3">
            <PaginationItem>
              <PaginationPrevious
                href="#"
                className={`rounded-full ${currentPage > 1 ? 'border border-[--buttonColor] hover:bg-[--buttonColorLight]' : 'hover:bg-transparent'} !text-[--buttonColor]`}
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              />
            </PaginationItem>
            <PaginationItem>
              <PaginationLink
                href="#"
                onClick={() => setCurrentPage(1)}
                isActive={currentPage === 1}
                className={`rounded-full ${currentPage === 1 ? 'border border-[--buttonColor]' : ''} hover:bg-[--buttonColorLight] !text-[--buttonColor]`}
              >
                1
              </PaginationLink>
            </PaginationItem>
            {currentPage > 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            {currentPage > 1 && currentPage < totalPages && (
              <PaginationItem>
                <PaginationLink href="#" isActive className="rounded-full border border-[--buttonColor] hover:bg-[--buttonColorLight] !text-[--buttonColor]">
                  {currentPage}
                </PaginationLink>
              </PaginationItem>
            )}
            {currentPage < totalPages - 1 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            <PaginationItem>
              <PaginationLink
                href="#"
                onClick={() => setCurrentPage(totalPages)}
                isActive={currentPage === totalPages}
                className={`rounded-full ${currentPage === totalPages ? 'border border-[--buttonColor]' : ''} hover:bg-[--buttonColorLight] !text-[--buttonColor]`}
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext
                href="#"
                className="rounded-full border border-[--buttonColor] hover:bg-[--buttonColorLight] !text-[--buttonColor]"
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination> */}
      </CardContent>
    </Card>
  );
};

export default PaymentTable;
