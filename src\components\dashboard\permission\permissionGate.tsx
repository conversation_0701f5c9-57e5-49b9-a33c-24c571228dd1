'use client';

import type { ReactNode } from 'react';
import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { AccessDeniedModal } from './AccessDeniedModal';
import { useState } from 'react';
import { InlineAccessDenied } from './inlineAccessDenied';

interface PermissionGateProps {
  permission: Permission | Permission[];
  children: ReactNode;
  options?: PermissionCheckOptions;
  fallbackType?: 'modal' | 'inline' | 'custom' | 'none';
  fallback?: ReactNode;
  title?: string;
  description?: string;
  showRefreshButton?: boolean;
  showContactSupport?: boolean;
  onAccessDenied?: () => void;
  className?: string;
}

/**
 * Advanced permission gate with multiple fallback options
 */
export function PermissionGate({
  permission,
  children,
  options,
  fallbackType = 'inline',
  fallback,
  title,
  description,
  showRefreshButton = true,
  showContactSupport = true,
  onAccessDenied,
  className = '',
}: PermissionGateProps) {
  const { checkPermission, isLoading } = usePermission();
  const [showModal, setShowModal] = useState(false);

  const hasPermission = checkPermission(permission, options);

  // Show loading state
  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    );
  }

  // If user has permission, render children
  if (hasPermission) {
    return <div className={className}>{children}</div>;
  }

  // Handle access denied
  if (onAccessDenied) {
    onAccessDenied();
  }

  // Custom fallback
  if (fallbackType === 'custom' && fallback) {
    return <div className={className}>{fallback}</div>;
  }

  // No fallback
  if (fallbackType === 'none') {
    return null;
  }

  // Modal fallback
  if (fallbackType === 'modal') {
    // Show a trigger or automatically open modal
    return (
      <div className={className}>
        <button
          onClick={() => setShowModal(true)}
          className="text-blue-600 hover:text-blue-800 underline"
        >
          Access Restricted Content
        </button>
        <AccessDeniedModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={title}
          description={description}
          requiredPermission={permission}
          showRefreshButton={showRefreshButton}
          showContactSupport={showContactSupport}
        />
      </div>
    );
  }

  // Default inline fallback
  return (
    <div className={className}>
      <InlineAccessDenied
        message={
          description || "You don't have permission to access this content."
        }
        requiredPermission={permission}
        showRefreshButton={showRefreshButton}
        showContactSupport={showContactSupport}
      />
    </div>
  );
}
