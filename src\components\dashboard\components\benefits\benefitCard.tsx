import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ExternalLink } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

interface BenefitCardProps {
  banner?: string;
  title: string;
  description: string;
  amount: string;
  onClick?: () => void;
  onViewDetails?: () => void;
}

export const BenefitCard: React.FC<BenefitCardProps> = ({
  title,
  description,
  amount,
  banner = '',
  onClick,
  onViewDetails,
}) => {
  return (
    <Card className="border rounded-lg shadow-md bg-white">
      <CardHeader className="p-0">
        <Image
          height={100}
          width={100}
          src={banner}
          quality={100}
          className="rounded-t-lg h-[188px]"
          style={{
            width: '100%',
            height: 'auto',
          }}
          alt={'benefit banner'}
          priority
        />
      </CardHeader>
      <CardContent className="space-y-2 pt-3">
        <CardTitle className="font-semibold text-neutral-900 text-[20px] leading-[28px]">
          {title}
        </CardTitle>
        <CardDescription className="text-[16px] font-normal text-neutral-500 leading-[24px]">
          {description}
        </CardDescription>
        <h6 className="mt-4 text-[16px] font-semibold text-neutral-900 leading-[24px]">
          How much you&apos;ll receive:
        </h6>
        <div className="bg-success-50 rounded m w-fit px-2 py-1">
          <p className="font-medium text-success-500 text-[16px] leading-[18px]">
            {amount}
          </p>
        </div>
        <div className="text-[16px] font-normal text-neutral-900 space-y-1">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="confirm-info"
              className="rounded border border-neutral-300 accent-primary-500"
            />
            <label
              className="text-neutral-900 font-normal leading-[20px] text-[14px]"
              htmlFor="confirm-info"
            >
              I confirm my information is up to date
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="agree-terms"
              className="rounded border border-neutral-300 accent-primary-500"
            />
            <label
              className="text-neutral-900 font-normal leading-[20px] text-[14px]"
              htmlFor="agree-terms"
            >
              I have read and agree to the terms and conditions
            </label>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-3">
        <Button
          onClick={onClick}
          className="px-[28px] py-[14px] h-[48px]  mr-2 w-full font-medium text-[18px]"
        >
          Claim Benefit
        </Button>
        <Button
          onClick={onViewDetails}
          variant="outline"
          className="px-[28px] py-[14px] h-[48px]  w-full font-medium text-[18px] flex justify-center items-center gap-2"
        >
          View More Details <ExternalLink className="w-3 h-3" />
        </Button>
      </CardFooter>
    </Card>
  );
};
