import AssistantTooltip from '@/components/dashboard/common/AssistantTooltip';
import { Button } from '@/components/ui/button';
import React from 'react';

type Props = {
  handleNext: () => void;
};

const AssistantMessage = (props: Props) => {
  return (
    <section className="max-w-[52rem] m-auto h-full flex items-center justify-center">
      <AssistantTooltip>
        <div className="w-full flex flex-col items-start p-7">
          <h2 className="text-[28px] font-medium text-neutral-900  leading-[36px]">
            Hi there!
          </h2>
          <p className="mt-2 text-[18px] font-normal text-neutral-700 text-left leading-[28px]">
            My name is <PERSON><PERSON>, your personal AI assistant. I&apos;m here to make
            signing up quick and easy so you can start finding the right talent
            and managing your opportunities—all in one place. Let&apos;s get
            started!
          </p>
          <Button onClick={props.handleNext} className="mt-4 px-6 ml-auto py-2">
            Continue
          </Button>
        </div>
      </AssistantTooltip>
    </section>
  );
};

export default AssistantMessage;
