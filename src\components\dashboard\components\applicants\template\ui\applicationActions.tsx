'use client';

import { Button } from '@/components/ui/button';
import * as Popover from '@radix-ui/react-popover';
import Image from 'next/image';
import CloseIcon from '@/assets/Close.svg';
import FileTextIcon from '@/assets/fileFolder.svg';
import type { Applicant } from '@/type';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import applicantService from '@/zustand/services/applicantServices';
import { useState } from 'react';
import { ConfirmationModal } from './confimationModal';
import { HireFlowModal } from './hireFlow';
import { useUpdateJobStatus } from '@/mutations/useJobMutations';
import { useBase64Downloader } from '@/hooks/useBase64Downloader';
import {
  Bookmark,
  BriefcaseBusiness,
  PhoneCall,
  EllipsisVertical,
} from 'lucide-react';

interface ApplicantActionsProps {
  applicant: Applicant;
  handleStatusUpdate?: (_status: Applicant['status']) => void;
  isUpdating?: boolean;
  openHireFlow?: (_applicantId: string) => void;
}

type ModalConfig = {
  isOpen: boolean;
  title: string;
  description: string;
  confirmText: string;
  confirmVariant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  onConfirm: () => void;
  positionName?: string;
};

export default function ApplicantActions({ applicant }: ApplicantActionsProps) {
  const queryClient = useQueryClient();
  const [modalConfig, setModalConfig] = useState<ModalConfig>({
    isOpen: false,
    title: '',
    description: '',
    confirmText: '',
    confirmVariant: 'default',
    onConfirm: () => {},
  });
  const [showHireFlow, setShowHireFlow] = useState(false);

  const updateJobStatusMutation = useUpdateJobStatus();

  const { mutate: updateStatus, isPending: isUpdating } = useMutation({
    mutationFn: (status: Applicant['status']) =>
      applicantService.updateApplicant(String(applicant.id), {
        status,
        applicantId: String(applicant.id),
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['applicant', applicant.id] });
      closeModal();
    },
    onError: (error) => {
      console.error('Failed to update status:', error);
      closeModal();
    },
  });

  const downloadBase64 = useBase64Downloader();

  const closeModal = () => {
    setModalConfig((prev) => ({ ...prev, isOpen: false }));
  };

  const confirmStatusUpdate = (status: Applicant['status']) => {
    let config: Partial<ModalConfig> = {};

    switch (status) {
      case 'Hired':
        setShowHireFlow(true);
        return;
      case 'Rejected':
        config = {
          title: 'Confirm Rejection',
          description: 'Are you sure you want to reject this applicant?',
          confirmText: 'Confirm Reject',
          confirmVariant: 'destructive',
          onConfirm: () => updateStatus('Rejected'),
        };
        break;
      case 'Shortlisted':
        config = {
          title: 'Mark as Shortlisted',
          description:
            'Are you sure you want to mark this applicant as shortlisted?',
          confirmText: 'Mark as Shortlisted',
          onConfirm: () => updateStatus('Shortlisted'),
        };
        break;
      case 'Contacted':
        config = {
          title: 'Mark as Contacted',
          description:
            'Are you sure you want to mark this applicant as contacted?',
          confirmText: 'Mark as Contacted',
          onConfirm: () => updateStatus('Contacted'),
        };
        break;
      default:
        return updateStatus(status);
    }

    setModalConfig({
      isOpen: true,
      ...config,
    } as ModalConfig);
  };

  const handleHireFlow = () => {
    setShowHireFlow(true);
  };

  const handleHireConfirm = async (closeJob?: boolean) => {
    try {
      await updateStatus('Hired');

      if (closeJob && applicant.job?.id) {
        await updateJobStatusMutation.mutateAsync({
          id: applicant.job.id,
          status: 'Closed',
        });
      }
      return Promise.resolve();
    } catch (error) {
      console.error('Failed to process hire action:', error);
      return Promise.reject(
        error instanceof Error ? error : new Error(String(error))
      );
    }
  };

  return (
    <>
      <div className="flex gap-[12px]">
        {applicant.status === 'Pending' && (
          <>
            <Button
              variant="default"
              className="py-[10px] px-[20px] h-[40px]"
              onClick={() =>
                downloadBase64(
                  applicant.cv?.base64Content,
                  applicant?.cv?.fileName
                )
              }
            >
              <Image
                src={FileTextIcon || '/placeholder.svg'}
                alt="folder"
                className="h-5 w-5"
              />
              CV
            </Button>
            <Button
              variant="outline"
              className={`py-[10px] px-[20px] h-[40px] ${isUpdating ? 'opacity-70' : ''}`}
              onClick={() => confirmStatusUpdate('Shortlisted')}
              disabled={isUpdating}
            >
              <Bookmark className="!size-5 text-[--buttonColor]" />
              <p className="!text-[--buttonColor] font-medium leading-[24px] text-[16px]">
                {' '}
                {isUpdating ? 'Updating...' : 'Shortlist'}
              </p>
            </Button>
            <Button
              variant="outline"
              className="py-[10px] px-[20px] h-[40px] border border-destructive-500 !text-destructive-500 hover:bg-destructive-50"
              onClick={() => confirmStatusUpdate('Rejected')}
              disabled={isUpdating}
            >
              <Image
                src={CloseIcon || '/placeholder.svg'}
                alt="Reject"
                className="h-5 w-5"
              />
              Reject
            </Button>
            <Popover.Root>
              <Popover.Trigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center justify-center bg-white w-10 h-10 p-0"
                  onClick={(e) => e.stopPropagation()}
                >
                  <EllipsisVertical className="!text-[--buttonColor]" />
                </Button>
              </Popover.Trigger>
              <Popover.Portal>
                <Popover.Content
                  side="bottom"
                  align="end"
                  className="w-[250px] bg-white shadow-lg border rounded-lg p-2 space-y-2 mt-2"
                >
                  <Button
                    variant="outline"
                    className="w-full rounded-none border-none bg-transparent text-neutral-900"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleHireFlow();
                    }}
                  >
                    Mark as Hired
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full rounded-none border-none bg-transparent text-neutral-900 "
                    onClick={(e) => {
                      e.stopPropagation();
                      confirmStatusUpdate('Contacted');
                    }}
                  >
                    Mark as Contacted
                  </Button>
                </Popover.Content>
              </Popover.Portal>
            </Popover.Root>
          </>
        )}

        {applicant.status === 'Shortlisted' && (
          <>
            <Button
              variant="default"
              className="py-[10px] px-[20px] h-[40px]"
              onClick={() =>
                downloadBase64(
                  applicant.cv?.base64Content,
                  applicant?.cv?.fileName
                )
              }
            >
              <Image
                src={FileTextIcon || '/placeholder.svg'}
                alt="folder"
                className="h-5 w-5"
              />
              CV
            </Button>
            <Button
              variant="outline"
              className="py-[10px] px-[20px] h-[40px]"
              onClick={() => confirmStatusUpdate('Contacted')}
              disabled={isUpdating}
            >
              <PhoneCall className="!size-5 text-[--buttonColor]" />
              Mark as Contacted
            </Button>
            <Button
              variant="outline"
              className="py-[10px] px-[20px] h-[40px]"
              onClick={() => confirmStatusUpdate('Hired')}
              disabled={isUpdating}
            >
              <BriefcaseBusiness className="!size-5 text-[--buttonColor]" />
              Mark as Hired
            </Button>
            <Button
              variant="outline"
              className="!text-destructive-500 border-destructive-200 bg-destructive-50  py-[10px] px-[20px] h-[40px] text-[16px]"
              onClick={() => confirmStatusUpdate('Rejected')}
              disabled={isUpdating}
            >
              <Image
                src={CloseIcon || '/placeholder.svg'}
                alt="Reject"
                className="h-5 w-5"
              />
              Reject
            </Button>
          </>
        )}

        {applicant.status === 'Contacted' && (
          <>
            <Button
              variant="default"
              className="py-[10px] px-[20px] h-[40px]"
              onClick={() =>
                downloadBase64(
                  applicant.cv?.base64Content,
                  applicant?.cv?.fileName
                )
              }
            >
              <Image
                src={FileTextIcon || '/placeholder.svg'}
                alt="folder"
                className="h-5 w-5"
              />
              CV
            </Button>
            <Button
              variant="outline"
              className="py-[10px] px-[20px] h-[40px]"
              onClick={() => confirmStatusUpdate('Hired')}
              disabled={isUpdating}
            >
              <BriefcaseBusiness className="!size-5 text-[--buttonColor]" />
              Mark as Hired
            </Button>
            <Button
              variant="outline"
              className="!text-destructive-500 hover:bg-destructive-50 border-destructive-200"
              onClick={() => confirmStatusUpdate('Rejected')}
              disabled={isUpdating}
            >
              <Image
                src={CloseIcon || '/placeholder.svg'}
                alt="Reject"
                width={16}
                height={16}
                className="mr-2"
              />
              Reject
            </Button>
          </>
        )}

        {applicant.status === 'Hired' && (
          <Button
            variant="default"
            className="py-[10px] px-[20px] h-[40px]"
            onClick={() =>
              downloadBase64(
                applicant.cv?.base64Content,
                applicant?.cv?.fileName
              )
            }
          >
            <Image
              src={FileTextIcon || '/placeholder.svg'}
              alt="folder"
              className="h-5 w-5"
            />
            CV
          </Button>
        )}
      </div>

      <ConfirmationModal
        isOpen={modalConfig.isOpen}
        onClose={closeModal}
        onConfirm={modalConfig.onConfirm}
        title={modalConfig.title}
        description={modalConfig.description}
        confirmText={modalConfig.confirmText}
        confirmVariant={modalConfig.confirmVariant}
        applicantName={applicant.user?.userName || 'Applicant'}
        positionName={modalConfig.positionName}
      />

      <HireFlowModal
        isOpen={showHireFlow}
        onClose={() => setShowHireFlow(false)}
        onConfirm={handleHireConfirm}
        applicantName={applicant.user?.userName || 'Applicant'}
        positionName={applicant.job?.title}
        jobId={applicant.job?.id}
      />
    </>
  );
}
