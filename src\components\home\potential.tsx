'use client';
import React from 'react';
import PotentialCard from './components/potential-card';
import ArrowIcon from '../icons/ArrowRight';
import { Montserrat } from 'next/font/google';
import useSettingsStore from '@/zustand/store/settingsStore';
import PencilBook from '@/components/icons/PencilBook';
import CashIcon from '@/components/icons/CashAdmin';
import SearchIcon from '@/components/icons/JobSearchIcon';
import TrainingIcon from '@/components/icons/Trainings';
import BriefcaseAdmin from '../icons/BriefcaseAdmin';
const montserrat = Montserrat({ subsets: ['latin'] });

function Potential() {
  const { appearanceSettings } = useSettingsStore();
  const brandColor = appearanceSettings?.brandColor;

  const cardsData = [
    {
      id: 1,
      icon: <BriefcaseAdmin width={32} height={32} stroke={brandColor} />,
      title: 'Find Jobs',
      description:
        "Search for job openings in different fields. Whether you're new to the workforce or have years of experience, there’s something for everyone.",
    },
    {
      id: 2,
      icon: <TrainingIcon width={32} height={32} stroke={brandColor} />,
      title: 'Access Training & Apprenticeships',
      description:
        'Explore courses and training programs to improve your skills or gain real-world experience while earning money.',
    },
    {
      id: 3,
      icon: <PencilBook width={32} height={32} stroke={brandColor} />,
      title: 'Plan Your Career',
      description:
        'Not sure what’s next? Use our career planning tools to find out what jobs match your skills and interests.',
    },
    {
      id: 4,
      icon: <CashIcon width={32} height={32} stroke={brandColor} />,
      title: 'Get Financial Help',
      description:
        'See what government support is available, including unemployment benefits, training grants, and other financial aid to help you along the way.',
    },
    {
      id: 5,
      icon: <SearchIcon strokeColor={brandColor} />,
      title: 'Explore Job Market Trends',
      description:
        'Find out which jobs are in demand and what skills employers are looking for.',
    },
  ];

  return (
    <div className="`bg-white/50 md:p-20 p-0">
      <p className="text-[14px] leading-[20px] font-semibold text-primary-500">
        Unlock your potential
      </p>
      <h3
        className={`${montserrat.className} font-semibold leading-[44px] text-[36px] mt-4 text-neutral-900`}
      >
        Your Future Starts Here
      </h3>
      <p className="font-normal text-[16px] leading-[28px] text-[#374151] md:w-[85%] w-full mt-4 text-neutral-700">
        Career Navigator is here to help you find a job, grow in your career,
        and get financial support when you need it. Whether you`re just starting
        out, changing careers, or looking for training, we`ve got your back.
      </p>

      <div className="w-full mx-auto py-5">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {cardsData.map((card) => (
            <PotentialCard
              key={card.id}
              title={card.title}
              icon={card.icon}
              description={card.description}
            />
          ))}
          <div className="flex gap-4 h-full bg-[#F3F5F7] items-center justify-center rounded-lg border border-gray-400">
            <p className="font-semibold leading-[18px] text-center text-[16px] text-primary-500">
              Get Started Now
            </p>
            <ArrowIcon stroke={brandColor} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Potential;
