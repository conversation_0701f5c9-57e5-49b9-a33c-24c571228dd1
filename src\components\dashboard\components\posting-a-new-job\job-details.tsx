'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import AssistantTooltip from '@/components/dashboard/common/AssistantTooltip';
import SuccessModal from '@/components/modals/success-modal';
import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  Formik,
  Form,
  Field,
  ErrorMessage,
  useField,
  type FieldProps,
  type FormikProps,
} from 'formik';
import * as Yup from 'yup';
import { useJobStore } from '@/zustand/store/jobStore';
import { useAddJob } from '@/mutations/useJobMutations';
import type { FormikHelpers } from 'formik';
import type { Job } from '@/type/jobType';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { ChevronLeft, Loader2 } from 'lucide-react';
import { RichTextEditor } from '@/components/ui/text-editor';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { AdminValue } from '@/type';
import useAiJobStore from '@/zustand/store/aiJobStore';
import { validationMessages } from '@/constants/validationMessages';
import InputField from '@/components/common/InputField';

const {
  jobTitleRequired,
  jobPositionRequired,
  jobDescriptionRequired,
  jobTypeRequired,
  jobModeRequired,
  minmumSalaryRequired,
  positiveSalary,
  companyNameRequired,
  locationRequired,
  maxSalaryRequired,
} = validationMessages;

const validationSchema = Yup.object({
  title: Yup.string().required(jobTitleRequired),
  jobPosition: Yup.string().required(jobPositionRequired),
  jobType: Yup.string().required(jobTypeRequired),
  jobMode: Yup.string().required(jobModeRequired),
  monthlyFrom: Yup.number()
    .required(minmumSalaryRequired)
    .min(0, positiveSalary),
  monthlyTo: Yup.number().required(maxSalaryRequired).min(0, positiveSalary),
  description: Yup.string().required(jobDescriptionRequired),
  companyName: Yup.string().required(companyNameRequired),
  location: Yup.string().required(locationRequired),
});

const DescriptionField = ({ ...props }) => {
  const [field, meta, helpers] = useField(props);

  const handleEditorChange = (value: string) => {
    helpers.setValue(value);
  };

  return (
    <div>
      <RichTextEditor
        value={field.value}
        onChange={handleEditorChange}
        placeholder="Enter job description, responsibilities, and qualifications..."
        maxWords={1200}
      />
      {meta.touched && meta.error ? (
        <div className="!text-destructive-500 text-[16px] mt-1">
          {meta.error}
        </div>
      ) : null}
    </div>
  );
};

export default function JobListingForm() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const { setPage } = useJobStore();
  const addJobMutation = useAddJob();
  const userId = useAuthStore((state) => state.user?.id);

  const expiryDate = new Date().toISOString();
  const startDate = new Date().toISOString();

  const { getJob, clearJob } = useAiJobStore((state) => state);
  const aiJob = getJob();

  const initialValues = {
    createdById: userId || '',
    title: aiJob?.title || '',
    description: aiJob?.description?.replace(/\n/g, '<br />') || '',
    location: aiJob?.location || '',
    monthlyFrom: aiJob?.monthlyFrom || 0,
    monthlyTo: aiJob?.monthlyTo ?? 0,
    expiryDate: aiJob?.expiryDate ?? expiryDate,
    companyName: aiJob?.companyName || '',
    startDate: aiJob?.startDate || startDate,
    jobType: aiJob?.jobType || '',
    jobMode: aiJob?.jobMode || '',
    industry: aiJob?.industry || '',
    experienceLevel: aiJob?.experienceLevel || '',
    jobPosition: aiJob?.jobPosition || '',
    lastModifiedById: userId || '',
  };

  const onSubmit = async (
    values: Job,
    { setSubmitting }: FormikHelpers<Job>
  ) => {
    try {
      const currentTime = new Date().toISOString();

      const formattedValues: Job = {
        ...values,
        startDate: currentTime,
        expiryDate: values.expiryDate
          ? new Date(values.expiryDate).toISOString()
          : undefined,
      };

      await addJobMutation.mutateAsync(formattedValues);
      clearJob();

      setOpen(true);
      setPage(1);
    } catch (error) {
      console.error('Job posting failed:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const formikRef = useRef<FormikProps<Job>>(null);

  const redirectingRef = useRef(false);

  const handlePostAnotherListing = () => {
    redirectingRef.current = true;
    router.push('/dashboard/jobs-and-training/posting-a-new-listing/job');
  };

  const getJobType = useAdminValues({
    category: AdminValuesCategories?.jobType?.category,
  });
  const JobType = getJobType.data?.data?.data?.customValues || [];

  const getOccupationClassification = useAdminValues({
    category: AdminValuesCategories?.occupationClassification?.category,
    subcategory:
      AdminValuesCategories?.occupationClassification?.subcategories?.ISCO_4,
  });
  const occupationClassification =
    getOccupationClassification.data?.data?.data?.customValues || [];

  if (redirectingRef.current) {
    return null;
  }

  return (
    <div className="p-4 max-w-4xl mx-auto space-y-4 rounded-lg">
      <div className="mb-20">
        <Button
          onClick={() => router.back()}
          variant="outline"
          className="py-3.5 px-7  mb-10 h-[48px] text-[16px] font-medium"
        >
          <ChevronLeft className="w-20 h-20" />
          Back
        </Button>
        <h1 className="text-[28px] font-semibold  mb-28">
          Add a New Job Listing
        </h1>
      </div>
      <div className="">
        <AssistantTooltip avatarPosition="top-left" arrowPosition="top-left">
          <Card className="max-w-5xl mx-auto text-justify rounded-lg">
            <CardHeader>
              <CardDescription className="text-[18px] font-normal text-neutral-700 leading-[28px]">
                Great! Here&apos;s the job listing based on the details you
                provided. Feel free to review and modify the information below:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Formik
                innerRef={formikRef}
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={onSubmit}
                enableReinitialize
              >
                {({
                  isSubmitting,
                  errors,
                  values,
                  handleChange,
                  handleBlur,
                }) => (
                  <Form className="space-y-6">
                    <div>
                      <InputField
                        label="Job Title"
                        name="title"
                        labelClass="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                        error={errors.title}
                        type={'text'}
                        placeholder="Senior AI Scientist II"
                        value={values.title}
                        handleChange={handleChange}
                        handleBlur={handleBlur}
                      />
                    </div>

                    <div>
                      <label
                        className="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                        htmlFor="jobPosition"
                      >
                        Job Position (ISCO Code)
                      </label>
                      <Field name="jobPosition">
                        {({ field, form }: FieldProps) => (
                          <Select
                            onValueChange={(value) =>
                              form.setFieldValue(field.name, value)
                            }
                            value={field.value}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select Job Position" />
                            </SelectTrigger>
                            <SelectContent>
                              {occupationClassification.map(
                                (item: AdminValue) => (
                                  <SelectItem key={item.id} value={item.value}>
                                    {item.label}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        )}
                      </Field>
                      <ErrorMessage
                        name="jobPosition"
                        component="div"
                        className="!text-destructive-500 text-[16px] mt-1"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label
                          className="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                          htmlFor="jobType"
                        >
                          Job Type
                        </label>
                        <Field name="jobType">
                          {({ field, form }: FieldProps) => (
                            <Select
                              onValueChange={(value) =>
                                form.setFieldValue(field.name, value)
                              }
                              value={field.value}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue
                                  placeholder={
                                    getJobType?.isLoading
                                      ? 'Loading..'
                                      : 'Select Job Type'
                                  }
                                />
                              </SelectTrigger>
                              <SelectContent>
                                {JobType.map((item: AdminValue) => (
                                  <SelectItem key={item.id} value={item.value}>
                                    {item.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </Field>
                        <ErrorMessage
                          name="jobType"
                          component="div"
                          className="!text-destructive-500 text-[16px] mt-1"
                        />
                      </div>

                      <div>
                        <label
                          className="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                          htmlFor="jobMode"
                        >
                          Remote/Onsite/Hybrid
                        </label>
                        <Field name="jobMode">
                          {({ field, form }: FieldProps) => (
                            <Select
                              onValueChange={(value) =>
                                form.setFieldValue(field.name, value)
                              }
                              value={field.value}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue placeholder="Remote" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="remote">Remote</SelectItem>
                                <SelectItem value="onsite">Onsite</SelectItem>
                                <SelectItem value="hybrid">Hybrid</SelectItem>
                              </SelectContent>
                            </Select>
                          )}
                        </Field>
                        <ErrorMessage
                          name="jobMode"
                          component="div"
                          className="!text-destructive-500 text-[16px] mt-1"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <InputField
                          label="Company Name"
                          name="companyName"
                          error={errors.companyName}
                          labelClass="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                          type={'text'}
                          value={values.companyName}
                          placeholder="Enter Company Name"
                          handleChange={handleChange}
                          handleBlur={handleBlur}
                        />
                      </div>

                      <div>
                        <InputField
                          label="Location"
                          name="location"
                          error={errors.location}
                          labelClass="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                          type={'text'}
                          value={values.location}
                          placeholder="Enter Job location"
                          handleChange={handleChange}
                          handleBlur={handleBlur}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <InputField
                          label="Minimum Salary"
                          name="monthlyFrom"
                          error={errors.monthlyFrom}
                          labelClass="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                          type={'number'}
                          value={values.monthlyFrom}
                          placeholder="3000"
                          handleChange={handleChange}
                          handleBlur={handleBlur}
                          startTitle="USD"
                        />
                      </div>

                      <div>
                        <InputField
                          label=" Maximum Salary To (Optional)"
                          name="monthlyTo"
                          error={errors.monthlyTo}
                          labelClass="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                          type={'number'}
                          value={Number(values.monthlyTo)}
                          placeholder="3000"
                          handleChange={handleChange}
                          handleBlur={handleBlur}
                          startTitle="USD"
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="description"
                        className="block text-[18px] font-semibold text-neutral-900 leading-[28px]"
                      >
                        Details:
                      </label>
                      <div className="rounded-md mt-1">
                        <DescriptionField id="description" name="description" />
                      </div>
                    </div>

                    <p className="text-[18px] leading-[28px] font-semibold text-neutral-700">
                      If everything looks good, click the button below to submit
                      your job post.
                    </p>

                    <div className="flex justify-center">
                      <Button
                        type="submit"
                        className="w-full h-[48px] py-[14px] px-[28px]"
                        disabled={isSubmitting || addJobMutation.isPending}
                      >
                        {isSubmitting || addJobMutation.isPending ? (
                          <div className="flex text-center items-center gap-3 ">
                            {' '}
                            <Loader2 className="animate-spin" />{' '}
                            <p> Submitting</p>{' '}
                          </div>
                        ) : (
                          'Submit Job Post'
                        )}
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            </CardContent>
          </Card>
        </AssistantTooltip>
      </div>

      <SuccessModal
        open={open}
        setOpen={setOpen}
        onPostAnotherListing={handlePostAnotherListing}
      />
    </div>
  );
}
