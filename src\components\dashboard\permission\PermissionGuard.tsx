import type { ReactNode } from 'react';
import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';

interface PermissionGuardProps {
  permission: Permission | Permission[];
  children: ReactNode;
  fallback?: ReactNode;
  options?: PermissionCheckOptions;
  loadingComponent?: ReactNode;
}

/**
 * Component that conditionally renders children based on user permissions
 */
export function PermissionGuard({
  permission,
  children,
  fallback = null,
  options,
  loadingComponent = null,
}: PermissionGuardProps) {
  const { checkPermission, isLoading } = usePermission();

  // Show loading component while permissions are being fetched
  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  // Render children if user has permission, otherwise render fallback
  return checkPermission(permission, options) ? (
    <>{children}</>
  ) : (
    <>{fallback}</>
  );
}
