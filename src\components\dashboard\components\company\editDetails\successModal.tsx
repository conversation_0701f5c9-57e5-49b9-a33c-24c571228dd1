'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  subDescription?: string;
  icon?: string;
}

export function SuccessModal({
  isOpen,
  onClose,
  title,
  description,
  subDescription,
  icon,
}: SuccessModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[600px]">
        <div className="flex items-start gap-2 mb-4">
          {icon && <span className="text-amber-500 text-xl">{icon}</span>}
          <div>
            <h3 className="font-semibold text-[28px] leading-[36px] text-neutral-900 mb-2">
              {title}
            </h3>
            <p className="text-[18px] text-neutral-500 font-normal leading-[24px] mt-2">
              {description}
            </p>
            {subDescription && (
              <p className="text-[18px] text-neutral-500 font-normal leading-[24px] mt-2">
                {subDescription}
              </p>
            )}
          </div>
        </div>
        <Button
          className="w-full bg-primary-500 hover:bg-primary-600 text-white py-[14px] px-[28px] h-[48px]"
          onClick={onClose}
        >
          Done
        </Button>
      </DialogContent>
    </Dialog>
  );
}
