// services/partnerService.ts
import axiosClient from '@/utils/axiosClient';
import type { IPartner } from '@/type';

const API_URL = '/partners';

const partnerService = {
  // Register a new partner
  registerPartner: async (
    partnerData: Omit<IPartner, 'partnerId'>
  ): Promise<IPartner> => {
    try {
      const response = await axiosClient.post<IPartner>(API_URL, partnerData);
      return response.data;
    } catch (error) {
      console.error('Error registering partner:', error);
      throw new Error('Failed to register partner');
    }
  },

  // Fetch a partner by ID
  getPartnerById: async (id: string): Promise<IPartner> => {
    try {
      const response = await axiosClient.get<IPartner>(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching partner with id ${id}:`, error);
      throw new Error('Failed to fetch partner');
    }
  },

  // Update a partner
  updatePartner: async (
    id: string,
    updatedPartner: Partial<IPartner>
  ): Promise<IPartner> => {
    try {
      const response = await axiosClient.put<IPartner>(
        `${API_URL}/${id}`,
        updatedPartner
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating partner with id ${id}:`, error);
      throw new Error('Failed to update partner');
    }
  },

  // Delete a partner
  deletePartner: async (id: string): Promise<void> => {
    try {
      await axiosClient.delete(`${API_URL}/${id}`);
    } catch (error) {
      console.error(`Error deleting partner with id ${id}:`, error);
      throw new Error('Failed to delete partner');
    }
  },

  // Fetch all partners (optional, if needed)
  getAllPartners: async (): Promise<IPartner[]> => {
    try {
      const response = await axiosClient.get<IPartner[]>(API_URL);
      return response.data;
    } catch (error) {
      console.error('Error fetching all partners:', error);
      throw new Error('Failed to fetch partners');
    }
  },
};

export default partnerService;
