import React from 'react';

function SpaceshipIcon() {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M31.1809 28.7392C31.3896 29.6256 31.5 30.5499 31.5 31.5C31.5 38.1274 26.1274 43.5 19.5 43.5V33.9006M31.1809 28.7392C38.6488 23.2822 43.5 14.4575 43.5 4.5C33.5429 4.50042 24.7189 9.35172 19.2624 16.8195M31.1809 28.7392C27.7638 31.2362 23.7988 33.028 19.5 33.9006M19.2624 16.8195C18.3755 16.6106 17.4507 16.5 16.5 16.5C9.87258 16.5 4.5 21.8726 4.5 28.5H14.1014M19.2624 16.8195C16.7657 20.2365 14.974 24.2014 14.1014 28.5M19.5 33.9006C19.2932 33.9426 19.0856 33.9825 18.8772 34.0202C17.0634 32.582 15.4198 30.9384 13.9816 29.1245C14.0194 28.9156 14.0593 28.7074 14.1014 28.5M9.62382 33.2815C7.42427 34.9223 6 37.5448 6 40.5C6 40.9737 6.0366 41.4389 6.10713 41.8929C6.56109 41.9634 7.02627 42 7.5 42C10.4552 42 13.0777 40.5757 14.7185 38.3762M33 18C33 19.6569 31.6569 21 30 21C28.3431 21 27 19.6569 27 18C27 16.3431 28.3431 15 30 15C31.6569 15 33 16.3431 33 18Z"
        stroke="url(#paint0_linear_4107_16072)"
        strokeWidth={3}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4107_16072"
          x1="4.5"
          y1="4.5"
          x2="43.5"
          y2="43.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4568DC" />
          <stop offset="1" stopColor="#B06AB3" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default SpaceshipIcon;
