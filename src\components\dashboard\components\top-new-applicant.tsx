/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ApplicantListingSkeleton from '@/components/dashboard/common/applicant-listing-skeleton';
import type { Applicant } from '@/type';
import applicantService from '@/zustand/services/applicantServices';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { Card } from '@/components/ui/card';
import { formatDate } from '@/utils';
import ApplicantActions from './applicants/components/applicationAction';

export default function TopApplicant() {
  const router = useRouter();
  const { user } = useAuthStore();
  const queryClient = useQueryClient();

  const [showHireFlow, setShowHireFlow] = useState(false);
  const [hireFlowStep, setHireFlowStep] = useState(0);
  const [hiringApplicantId, setHiringApplicantId] = useState<string | null>(
    null
  );
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectApplicantId, setRejectApplicantId] = useState<string | null>(
    null
  );
  // eslint-disable-next-line no-unused-vars
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const {
    data: applicantsData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['topApplicants'],
    queryFn: async () => {
      return await applicantService.getApplicants(
        1,
        5,
        null,
        true,
        'Pending',
        user?.id || null,
        ''
      );
    },
  });

  const applicants = applicantsData?.data || [];
  const total = applicantsData?.total || 0;

  const { mutateAsync: updateStatus } = useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: Applicant['status'];
    }) => {
      await applicantService.updateApplicant(id, { status });
    },
    onSuccess: (_, { id, status }) => {
      queryClient.setQueryData<Applicant[]>(['topApplicants'], (oldData) =>
        oldData?.map((app) => (app.id === id ? { ...app, status } : app))
      );
      queryClient.invalidateQueries({ queryKey: ['topApplicants'] });
    },
    onError: (error) => {
      console.error('Failed to update applicant status', error);
    },
  });

  const closeHireFlow = () => {
    setShowHireFlow(false);
    setHireFlowStep(0);
    setHiringApplicantId(null);
  };

  const handleMarkAsHired = () => {
    if (hiringApplicantId !== null) {
      updateStatus({ id: hiringApplicantId, status: 'Hired' });
    }
    setHireFlowStep(1);
  };

  const handleCloseListing = () => {
    setHireFlowStep(2);
  };

  const handleConfirmCloseListing = () => {
    closeHireFlow();
  };

  const closeRejectModal = () => {
    setShowRejectModal(false);
    setRejectApplicantId(null);
  };

  const handleConfirmReject = () => {
    if (rejectApplicantId !== null) {
      updateStatus({ id: rejectApplicantId, status: 'Rejected' });
    }
    closeRejectModal();
  };

  if (isLoading) {
    return <ApplicantListingSkeleton />;
  }

  if (isError) {
    return <div>Error fetching applicants. Please try again later.</div>;
  }

  const hireApplicant = applicants.find((app) => app.id === rejectApplicantId);

  return (
    <>
      <Card className="space-y-3 p-6 rounded-lg">
        <h4 className="text-[20px] font-semibold text-neutral-900 leading-[28px] mb-4">
          Top New Applicants
        </h4>
        <div className="bg-white space-y-3">
          <Table>
            <TableHeader className="bg-neutral-50 rounded border-0">
              <TableRow className="text-neutral-700 font-semibold text-[16px]">
                <TableHead className="text-[16px] font-semibold leading-[24px] !text-[--bodyTextColor]">
                  Name
                </TableHead>
                <TableHead className="text-[16px] font-semibold leading-[24px] !text-[--bodyTextColor]">
                  Position
                </TableHead>
                <TableHead className="text-[16px] font-semibold leading-[24px] !text-[--bodyTextColor]">
                  Applied Date
                </TableHead>
                <TableHead className="text-[16px] font-semibold leading-[24px] !text-[--bodyTextColor]">
                  Email
                </TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applicants.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center">
                      <p className="text-neutral-500 text-[16px] font-medium">
                        No new applicants found
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                applicants.map((applicant: Applicant) => (
                  <TableRow
                    key={applicant.id}
                    className={`transition cursor-pointer ${
                      selectedIds.includes(applicant.id)
                        ? 'bg-neutral-200'
                        : 'hover:bg-neutral-100'
                    }`}
                    onClick={() =>
                      router.push(
                        `/dashboard/jobs-and-training/applicants/${applicant.id}`
                      )
                    }
                  >
                    <TableCell className="min-w-[200px]">
                      <div className="flex flex-col">
                        <h5 className="text-[16px] font-medium !text-[--bodyTextColor] leading-[24px] ">
                          {applicant.user?.fullName || 'N/A'}
                        </h5>
                      </div>
                    </TableCell>
                    <TableCell className="min-w-[280px]">
                      <h5 className="text-[16px] font-medium !text-[--bodyTextColor] leading-[24px]">
                        {applicant.job?.title || 'N/A'}
                      </h5>
                    </TableCell>
                    <TableCell className="min-w-[200px]">
                      <h6 className="text-[16px] font-normal !text-[--bodyTextColor] leading-[24px]">
                        {formatDate(applicant.appliedDate)}
                      </h6>
                    </TableCell>
                    <TableCell className="min-w-[200px]">
                      <h6 className="text-[16px] font-normal !text-[--bodyTextColor] leading-[24px]">
                        {applicant?.user?.email || 'N/A'}
                      </h6>
                    </TableCell>
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <ApplicantActions
                        applicant={applicant}
                        status={'Pending'}
                        onUpdateStatus={(id, newStatus) =>
                          updateStatus({
                            id: applicant.id,
                            status: newStatus,
                          })
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {applicants.length > 0 && (
            <>
              <hr />
              <Link
                href="dashboard/jobs-and-training/applicants"
                className="flex justify-center w-full mt-6"
              >
                <Button
                  variant="outline"
                  className="mx-auto h-[48px] px-[28px] py-[14px]  text-[18px] leading-[27px] font-medium"
                >
                  View All New Applicants ({total})
                </Button>
              </Link>
            </>
          )}
        </div>
      </Card>

      <Dialog open={showHireFlow} onOpenChange={setShowHireFlow}>
        <DialogContent className="">
          {hireFlowStep === 0 && (
            <div className="space-y-4">
              <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
                Mark as Hired
              </h2>
              <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
                Are you sure you want to mark this applicant as hired?
              </p>
              <ul className="">
                <li className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8">
                  {hireApplicant?.user?.userName || 'N/A'} for the position{' '}
                  {hireApplicant?.job.jobPosition || 'Unknown Position'}
                </li>
              </ul>
              <div className="flex gap-2 justify-end">
                <Button
                  className="rounded-full bg-transparent text-neutral-700 border border-neutral-700 "
                  onClick={closeHireFlow}
                >
                  Cancel
                </Button>
                <Button onClick={handleMarkAsHired}>Mark as Hired</Button>
              </div>
            </div>
          )}

          {hireFlowStep === 1 && (
            <div className="space-y-4">
              <h2 className="text-[24px] font-semibold text-neutral-900 leading-[24px]">
                Close Listing?
              </h2>
              <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
                Would you also like to close this listing?
              </p>
              <ul>
                <li className=" list-disc font-semibold leading-[28px] text-neutral-700 text-[18px] ml-8 ">
                  {' '}
                  HR Manager{' '}
                </li>
              </ul>
              <p className=" text-neutral-500 text-[18px] font-normal leading-[28px]">
                {' '}
                Keep this listing open if you would like to continue hiring for
                this role.
              </p>
              <div className="flex flex-col space-y-3">
                <Button onClick={handleCloseListing}>Close Listing</Button>
                <Button
                  className="bg-transparent"
                  variant="outline"
                  onClick={closeHireFlow}
                >
                  Keep Listing Open
                </Button>
              </div>
            </div>
          )}

          {hireFlowStep === 2 && (
            <div className="space-y-4">
              <h2 className="text-[16px] font-semibold">Confirm Close</h2>
              <p>Are you sure you want to close this listing?</p>
              <div className="flex gap-2">
                <Button variant={'outline'} onClick={closeHireFlow}>
                  Cancel
                </Button>
                <Button onClick={handleConfirmCloseListing}>
                  Confirm Close listing
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={showRejectModal} onOpenChange={setShowRejectModal}>
        <DialogContent>
          <h2 className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Confirm Reject
          </h2>
          <p className="text-[16px] font-normal text-neutral-500 leading-[28px]">
            Are you sure you want to reject this applicant?
          </p>
          <ul>
            <li className="list-disc font-semibold leading-[28px] text-neutral-900 ml-8">
              {applicants.find((app) => app.id === rejectApplicantId)?.user
                ?.userName || 'N/A'}
            </li>
          </ul>
          <div className="flex gap-2 justify-end mt-4">
            <Button variant="outline" onClick={closeRejectModal}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmReject}
              className="bg-destructive-500 text-white hover:bg-destructive-600"
            >
              Confirm Reject
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
