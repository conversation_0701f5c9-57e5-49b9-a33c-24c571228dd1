'use client';

import React from 'react';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { cn } from '@/lib/utils';

const steps = [
  { id: 'summary', label: '1' },
  { id: 'details', label: '2' },
  { id: 'skills', label: '3' },
];

export const CourseFormStepper: React.FC = () => {
  const { currentStep } = useCourseFormStore();

  // Get the index of the current step
  const currentStepIndex = steps.findIndex((step) => step.id === currentStep);

  return (
    <div className="flex justify-center items-center my-6">
      {steps.map((step, index) => {
        // Step is completed if it's before the current step
        const isCompleted = index < currentStepIndex;
        const isCurrent = index === currentStepIndex;

        return (
          <React.Fragment key={step.id}>
            <div
              className={cn(
                'h-2 w-8 rounded-full', // Maintain original size classes
                isCompleted || isCurrent
                  ? 'bg-primary-500'
                  : 'bg-transparent border border-primary-500'
              )}
            />
            {index < steps.length - 1 && (
              <div className="h-1 w-4 bg-transparent" /> // Maintain original connector style
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};
