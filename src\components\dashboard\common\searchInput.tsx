import React from 'react';

// Define the props interface
interface SearchInputProps {
  type?: string;
  placeholder?: string;
  className?: string;
}

function SearchInput({
  type = 'search',
  placeholder = 'Search keyword...',
  className = '',
  ...rest
}: SearchInputProps) {
  return (
    <div className="relative max-w-md rounded-full bg-gradient-to-r from-[#4568DC] to-[#B06AB3] p-[2px]">
      <div className="flex items-center w-full rounded-full bg-white px-4 ">
        <input
          type={type}
          className={`p-2 flex-1 focus:outline-none focus:ring-0 border-none ${className}`}
          placeholder={placeholder}
          {...rest}
        />
      </div>
    </div>
  );
}

export default SearchInput;
