import React from 'react';

function CvIcon() {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.2659 14.6667H10.9326C8.87069 14.6667 7.19922 16.3381 7.19922 18.4V35.2C7.19922 37.2618 8.87069 38.9333 10.9326 38.9333H37.0659C39.1277 38.9333 40.7992 37.2618 40.7992 35.2V18.4C40.7992 16.3381 39.1277 14.6667 37.0659 14.6667H27.7326M20.2659 14.6667V12.8C20.2659 10.7381 21.9374 9.06665 23.9992 9.06665C26.0611 9.06665 27.7326 10.7381 27.7326 12.8V14.6667M20.2659 14.6667C20.2659 16.7285 21.9374 18.4 23.9992 18.4C26.0611 18.4 27.7326 16.7285 27.7326 14.6667M18.3992 29.6C20.4611 29.6 22.1326 27.9285 22.1326 25.8667C22.1326 23.8048 20.4611 22.1333 18.3992 22.1333C16.3374 22.1333 14.6659 23.8048 14.6659 25.8667C14.6659 27.9285 16.3374 29.6 18.3992 29.6ZM18.3992 29.6C20.8375 29.6 22.9117 31.1583 23.6805 33.3333M18.3992 29.6C15.9609 29.6 13.8865 31.1583 13.1178 33.3333M29.5992 24H35.1992M29.5992 31.4667H33.3326"
        stroke="url(#paint0_linear_4107_15994)"
        strokeWidth={3}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4107_15994"
          x1="7.19922"
          y1="9.06665"
          x2="36.8599"
          y2="42.4349"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4568DC" />
          <stop offset="1" stopColor="#B06AB3" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default CvIcon;
