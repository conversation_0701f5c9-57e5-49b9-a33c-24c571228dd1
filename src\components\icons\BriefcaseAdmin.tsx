import React from 'react';

interface BriefcaseAdminProps {
  width?: number;
  height?: number;
  stroke?: string;
}

const BriefcaseAdmin: React.FC<BriefcaseAdminProps> = ({
  width = 32,
  height = 32,
  stroke = '#043C5B',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M27 18.8665V24.5332C27 25.9925 25.9508 27.2478 24.5042 27.4399C21.7219 27.8093 18.8832 27.9999 16 27.9999C13.1168 27.9999 10.2781 27.8093 7.49579 27.4399C6.0492 27.2478 5 25.9925 5 24.5332V18.8665M27 18.8665C27.6292 18.3301 28 17.5184 28 16.6518V11.6075C28 10.1663 26.9761 8.92103 25.5507 8.70775C24.0502 8.48323 22.5327 8.31081 21 8.19237M27 18.8665C26.7419 19.0866 26.4402 19.2603 26.1028 19.3724C22.9271 20.4283 19.5303 20.9999 16 20.9999C12.4697 20.9999 9.07292 20.4283 5.89718 19.3724C5.55979 19.2603 5.25814 19.0865 5 18.8665M5 18.8665C4.37077 18.3301 4 17.5184 4 16.6518V11.6075C4 10.1663 5.02387 8.92103 6.44926 8.70775C7.94977 8.48324 9.46732 8.31081 11 8.19237M21 8.19237V6.99994C21 5.34308 19.6569 3.99994 18 3.99994H14C12.3431 3.99994 11 5.34308 11 6.99994V8.19237M21 8.19237C19.3501 8.06487 17.6826 7.99994 16 7.99994C14.3174 7.99994 12.6499 8.06487 11 8.19237M16 16.9999H16.01V17.0099H16V16.9999Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default BriefcaseAdmin;
