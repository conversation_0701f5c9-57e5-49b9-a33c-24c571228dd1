import type { IJob } from '@/type';
import { create } from 'zustand';

interface JobState {
  job: IJob | null;
  setJob: (_job: IJob) => void;
  getJob: () => IJob | null;
  clearJob: () => void;
}

// type MyPersist = (
//   config: StateCreator<JobState, [], []>,
//   options: PersistOptions<JobState>
// ) => StateCreator<JobState, [], []>

const useAiJobStore = create<JobState>((set, get) => ({
  job: null,
  setJob: (job: IJob) => set({ job }),
  getJob: () => get().job,
  clearJob: () => set({ job: null }),
}));

export default useAiJobStore;
