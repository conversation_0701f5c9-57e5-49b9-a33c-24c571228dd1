import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import JobListingReview from '@/components/dashboard/components/posting-a-new-job/job-details';
import React from 'react';

function Job() {
  return (
    <div className="">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Post a New Listing',
            href: '/dashboard/jobs-and-training/posting-a-new-listing',
          },
          {
            label: 'Job',
            href: '/dashboard/jobs-and-training/posting-a-new-listing/job',
          },
        ]}
      />

      <JobListingReview />
    </div>
  );
}

export default Job;
