import React from 'react';
import { SectionHeader } from '../components/SectionHeader';
import { ActionCard } from '../components/ActionCard';
import SpaceshipIcon from '@/components/icons/SpaceshipIcon';
import CvIcon from '@/components/icons/CvIcon';
import StudyIcon from '@/components/icons/StudyIcon';

function AdvanceCareerSection() {
  return (
    <div>
      <SectionHeader title="Update your career profile" subtitle="" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-4">
        <ActionCard
          icon={<CvIcon />}
          title={'Upload your CV'}
          description={
            'Upload your latest CV and refine your career profile for smarter job recommendations.'
          }
        />
        <ActionCard
          icon={<StudyIcon />}
          title={'Discover personalised learning paths'}
          description={
            'Find courses, certifications, and training programs that align with your career goals.'
          }
        />
        <ActionCard
          icon={<SpaceshipIcon />}
          title={'Take a career test'}
          description={
            'Take a self-assessment to identify strengths and uncover opportunities for growth.'
          }
        />
      </div>
    </div>
  );
}

export default AdvanceCareerSection;
