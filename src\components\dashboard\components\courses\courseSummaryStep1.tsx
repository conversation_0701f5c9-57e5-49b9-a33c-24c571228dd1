'use client';

import type React from 'react';
import { useState } from 'react';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PlusIcon } from 'lucide-react';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { Topic } from '@/type';

interface CourseSummaryStepProps {
  fieldErrors?: Record<string, string>;
}

export const CourseSummaryStep: React.FC<CourseSummaryStepProps> = ({
  fieldErrors = {},
}) => {
  const {
    course,
    updateCourse,
    addProgramOutline,
    updateProgramOutline,
    removeProgramOutline,
  } = useCourseFormStore();
  const [descriptionChars, setDescriptionChars] = useState(
    800 - (course.description?.length || 0)
  );
  const [objectiveChars, setObjectiveChars] = useState(
    800 - (course.learningObjective?.length || 0)
  );

  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});

  const errors = { ...fieldErrors, ...localErrors };

  // Validate fields on blur
  const validateField = (field: string, value: string) => {
    if (!value.trim()) {
      setLocalErrors((prev) => ({
        ...prev,
        [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }
    setLocalErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const validateSelectField = (field: string, value: string) => {
    if (!value) {
      setLocalErrors((prev) => ({
        ...prev,
        [field]: `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
      }));
      return false;
    }
    setLocalErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    return true;
  };

  const validateOutlineField = (
    index: number,
    field: 'key' | 'value',
    value: string
  ) => {
    const fieldName = `outline-${index}-${field}`;
    if (!value.trim()) {
      setLocalErrors((prev) => ({
        ...prev,
        [fieldName]: `${field === 'key' ? 'Section title' : 'Section description'} is required`,
      }));
      return false;
    }
    setLocalErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
    return true;
  };

  const trainingTopics = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });

  const mainTopics: Topic[] =
    trainingTopics.data?.data?.data?.customValues || [];

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { value } = e.target;
    if (value.length <= 800) {
      updateCourse({ description: value });
      setDescriptionChars(800 - value.length);
    }
    validateField('description', value);
  };

  const handleObjectiveChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    if (value.length <= 800) {
      updateCourse({ learningObjective: value });
      setObjectiveChars(800 - value.length);
    }
    validateField('learningObjective', value);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (value.split(/\s+/).length <= 50) {
      updateCourse({ name: value });
    }
    validateField('name', value);
  };

  const handleSectionTitleChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = e.target;
    if (value.split(/\s+/).length <= 50) {
      updateProgramOutline(index, value, course.programOutlines[index].value);
    }
    validateOutlineField(index, 'key', value);
  };

  const handleSectionDescriptionChange = (
    index: number,
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { value } = e.target;
    if (value.length <= 400) {
      updateProgramOutline(index, course.programOutlines[index].key, value);
    }
    validateOutlineField(index, 'value', value);
  };

  const handleMainTopicChange = (value: string) => {
    updateCourse({ mainTopic: value });
    validateSelectField('mainTopic', value);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-[24px] leading-[32px] text-neutral-900 font-semibold">
        Step 1: Course Summary
      </h2>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="name"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Course Name:
            {/* <span className="text-destructive-500">*</span> */}
            {/* <span className="text-[16px] text-neutral-500 ml-1">
              (Max 50 words)
            </span> */}
          </label>
          <Input
            id="name"
            value={course.name}
            onChange={handleNameChange}
            onBlur={() => validateField('name', course.name)}
            placeholder="Enter the course title"
            className={errors.name ? 'border-destructive-500' : 'mt-2'}
            required
          />
          {errors.name && (
            <p className="text-destructive-500 text-[14px] mt-1">
              {errors.name}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="mainTopic"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Main Topic:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          {trainingTopics.isLoading ? (
            <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-[16px]">
              Loading...
            </div>
          ) : (
            <Select
              value={course.mainTopic}
              onValueChange={handleMainTopicChange}
              onOpenChange={(open) =>
                !open && validateSelectField('mainTopic', course.mainTopic)
              }
            >
              <SelectTrigger
                id="mainTopic"
                className={errors.mainTopic ? 'border-destructive-500' : 'mt-2'}
              >
                <SelectValue placeholder="Please select" />
              </SelectTrigger>
              <SelectContent>
                {mainTopics.map((topic: Topic) => (
                  <SelectItem key={topic?.id} value={topic?.value}>
                    {topic?.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          {errors.mainTopic && (
            <p className="text-destructive-500 text-[14px] mt-1">
              {errors.mainTopic}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="description"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Course Description:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          <Textarea
            id="description"
            value={course.description}
            onChange={handleDescriptionChange}
            onBlur={() => validateField('description', course.description)}
            placeholder="Provide a brief overview of what this course covers"
            className={`min-h-[120px] ${errors.description ? 'border-destructive-500' : 'mt-2'}`}
            required
          />
          <div className="flex justify-between mt-1">
            <p className="text-[16px] text-neutral-500">
              {descriptionChars} characters remaining
            </p>
            {errors.description && (
              <p className="text-destructive-500 text-[14px]">
                {errors.description}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            htmlFor="learningObjective"
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          >
            Learning Objectives:
            {/* <span className="text-destructive-500">*</span> */}
          </label>
          <Textarea
            id="learningObjective"
            value={course.learningObjective}
            onChange={handleObjectiveChange}
            onBlur={() =>
              validateField('learningObjective', course.learningObjective)
            }
            placeholder="Describe the key skills or knowledge participants will gain"
            className={`min-h-[120px] ${errors.learningObjective ? 'border-destructive-500' : 'mt-2'}`}
            required
          />
          <div className="flex justify-between mt-1">
            <p className="text-[16px] text-neutral-500">
              {objectiveChars} characters remaining
            </p>
            {errors.learningObjective && (
              <p className="text-destructive-500 text-[14px]">
                {errors.learningObjective}
              </p>
            )}
          </div>
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <label className="text-[18px] font-semibold leading-[28px] text-neutral-900">
              Program Outline:
              {/* <span className="text-destructive-500">*</span> */}
            </label>
          </div>

          {course.programOutlines.map((outline, index) => (
            <div key={outline.key} className="mb-4 pt-2 space-y-2">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div>
                    <label
                      htmlFor={`section-title-${index}`}
                      className="text-[16px] font-semibold leading-[28px] text-neutral-900"
                    >
                      Section {index + 1} Title{' '}
                      {/* <span className="text-destructive-500">*</span> */}
                    </label>
                    <Input
                      id={`section-title-${index}`}
                      value={outline.key}
                      onChange={(e) => handleSectionTitleChange(index, e)}
                      onBlur={() =>
                        validateOutlineField(index, 'key', outline.key)
                      }
                      placeholder="Module 1: Advanced Service Design Principles"
                      className={`w-full ${errors[`outline-${index}-key`] ? 'border-destructive-500' : 'mt-1'}`}
                      required
                    />
                    {errors[`outline-${index}-key`] && (
                      <p className="text-destructive-500 text-[14px] mt-1">
                        {errors[`outline-${index}-key`]}
                      </p>
                    )}
                  </div>

                  <div className="mt-2">
                    <label
                      htmlFor={`section-desc-${index}`}
                      className="text-[16px] font-semibold leading-[28px] text-neutral-900"
                    >
                      Section {index + 1} Description{' '}
                      {/* <span className="text-destructive-500">*</span> */}
                    </label>
                    <Textarea
                      id={`section-desc-${index}`}
                      value={outline.value}
                      onChange={(e) => handleSectionDescriptionChange(index, e)}
                      onBlur={() =>
                        validateOutlineField(index, 'value', outline.value)
                      }
                      placeholder="Describe what this section or module is about"
                      className={`min-h-[120px] w-full ${errors[`outline-${index}-value`] ? 'border-destructive-500' : 'mt-2'}`}
                      required
                    />
                    <div className="flex justify-between mt-1">
                      <p className="text-[16px] text-neutral-500">
                        {400 - outline.value.length} characters remaining
                      </p>
                      {errors[`outline-${index}-value`] && (
                        <p className="text-destructive-500 text-[14px]">
                          {errors[`outline-${index}-value`]}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeProgramOutline(index)}
                  className="ml-4 text-destructive-500 border border-destructive-500 rounded-[--buttonStyle] hover:text-destructive-500 relative top-8 hover:bg-inherit cursor-pointer"
                  disabled={course.programOutlines.length <= 1}
                  aria-label={`Remove section ${index + 1}`}
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}

          <Button
            variant="outline"
            size="sm"
            onClick={addProgramOutline}
            className="mt-2 ml-2 py-[10px] px-[20px] h-[40px] !rounded-[--buttonStyle]"
            disabled={course.programOutlines.length >= 30}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add another program outline section
          </Button>
        </div>
      </div>
    </div>
  );
};
