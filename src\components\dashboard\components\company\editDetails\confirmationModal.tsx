'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { Representative } from '@/type/index';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText: string;
  representative: Representative | null;
  type: 'deactivate' | 'delete' | 'reactivate';
  confirmButtonVariant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText,
  representative,
  type,
  confirmButtonVariant = type === 'delete' ? 'destructive' : 'default',
}: ConfirmationModalProps) {
  if (!representative) return null;

  // Get initials from name
  const getInitials = (name: string) => {
    if (!name) return 'SF';

    // If it's an email address (for invite sent)
    if (name.includes('@')) return 'SF';

    const parts = name.split(' ');
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  const fullName =
    representative.firstName && representative.lastName
      ? `${representative.firstName} ${representative.lastName}`
      : representative.isActive
        ? representative.email
        : 'Full Name';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-[24px] leading-[32px] text-neutral-900 font-semibold">
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="flex items-center gap-3 mb-4 border p-3 rounded-lg mt-2">
          <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-primary-500 rounded-full !text-white font-medium">
            {getInitials(fullName)}
          </div>
          <div>
            <p className="font-medium">{fullName}</p>
            <p className="text-sm text-neutral-500">{representative.email}</p>
            <p className="text-sm text-neutral-500">{representative.role}</p>
          </div>
        </div>

        <p className="text-neutral-500 text-[18px] font-normal leading-[28px]">
          {description}
        </p>

        <ul className="space-y-2 text-[16px] text-neutral-500 font-normal mb-4">
          {type === 'deactivate' ? (
            <>
              <li>• This will suspend their access to the platform.</li>
              <li>
                • Their account will remain in the system but will be marked as
                inactive.
              </li>
              <li>• This action can be reversed at any time.</li>
            </>
          ) : type === 'reactivate' ? (
            <>
              <li>• This will restore their access to the platform.</li>
              <li>• They will regain all their previous permissions.</li>
              <li>• Their account will be marked as active.</li>
            </>
          ) : (
            <>
              <li>• This will delete their account from the platform.</li>
              <li>
                • They will need to be re-invited manually if access is needed
                again.
              </li>
              <li>• This action cannot be undone.</li>
            </>
          )}
        </ul>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant={confirmButtonVariant} onClick={onConfirm}>
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
