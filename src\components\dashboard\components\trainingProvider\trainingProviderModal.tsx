'use client';

import type React from 'react';
import { useState } from 'react';
import { X, Upload } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { requestTrainingProviderAccess } from '@/zustand/services/trainingProviderServices';
import type { UploadedFile } from '@/types/courseType';
import toast from 'react-hot-toast';
import { useAdminValues, usePartnerProfile } from '@/queries';
import { AdminValuesCategories } from '@/constants';
import type { Topic } from '@/type';

interface TrainingProviderRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const TrainingProviderRequestModal: React.FC<
  TrainingProviderRequestModalProps
> = ({ isOpen, onClose }) => {
  const { user } = useAuthStore();

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [otherCategory, setOtherCategory] = useState('');
  const [showOtherField, setShowOtherField] = useState(false);
  const [description, setDescription] = useState('');
  const [website, setWebsite] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [descriptionCharCount, setDescriptionCharCount] = useState(0);
  const MAX_DESCRIPTION_CHARS = 800;
  const MAX_UPLOADS = 10;
  const { data: profile } = usePartnerProfile();

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      if (category === 'Other') {
        setShowOtherField(true);
      }
      setSelectedCategories([...selectedCategories, category]);
    } else {
      if (category === 'Other') {
        setShowOtherField(false);
        setOtherCategory('');
      }
      setSelectedCategories(selectedCategories.filter((c) => c !== category));
    }
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const text = e.target.value;
    if (text.length <= MAX_DESCRIPTION_CHARS) {
      setDescription(text);
      setDescriptionCharCount(text.length);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    if (uploadedFiles.length >= MAX_UPLOADS) {
      toast.error('You can upload a maximum of 10 files.');
      return;
    }

    const file = e.target.files[0];
    try {
      const base64String = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(new Error('Failed to read file'));
        reader.readAsDataURL(file);
      });

      const uploadedFile: UploadedFile = {
        id: Date.now().toString(),
        name: file.name,
        url: base64String,
        size: file.size,
        type: file.type,
      };

      setUploadedFiles([...uploadedFiles, uploadedFile]);
      toast.success('File uploaded successfully');
      e.target.value = '';
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('There was an error uploading your file. Please try again.');
    }
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(uploadedFiles.filter((file) => file.id !== fileId));
    toast.success('File removed');
  };

  const handleSubmit = async () => {
    if (selectedCategories.length === 0) {
      toast.error('Please select at least one training category.');
      return;
    }
    if (selectedCategories.includes('Other') && !otherCategory) {
      toast.error('Please specify the other category.');
      return;
    }
    if (!description) {
      toast.error('Please provide a description of your training programs.');
      return;
    }

    if (website && !isValidUrl(website)) {
      toast.error('Please enter a valid website URL.');
      return;
    }

    setIsSubmitting(true);

    try {
      let categories = [...selectedCategories];
      if (showOtherField && otherCategory) {
        categories = categories.filter((c) => c !== 'Other');
        categories.push(otherCategory);
      }

      const accreditations = uploadedFiles.map((file) => file.url);
      const partId = profile.data.items[0].partner.id;

      await requestTrainingProviderAccess({
        name: user?.fullName || '',
        description,
        website,
        categories,
        accreditations,
        partnerId: partId || '',
        createdById: user?.id || '',
      });

      toast.success(
        'Your Training provider access request has been submitted successfully!'
      );

      onClose();
      resetForm();
      window.location.href = `${window.location.href}?refresh=${Date.now()}`;
    } catch (error) {
      console.error('Error requesting training provider access:', error);
      toast.error(
        'You need a valid partner account to become a Training Provider.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setSelectedCategories([]);
    setOtherCategory('');
    setShowOtherField(false);
    setDescription('');
    setWebsite('');
    setUploadedFiles([]);
  };

  const trainingTopics = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });

  const mainTopics: Topic[] =
    trainingTopics.data?.data?.data?.customValues || [];

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent
          className="
             max-h-[90vh]   /* never exceed 90 % of viewport height */
             overflow-y-auto /* scroll inside the dialog */
             z-50"
        >
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold text-neutral-900">
              Become a Training provider
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <Label className="font-semibold text-[18px] text-neutral-900">
                Training Categories Offered:{' '}
                <span className="text-destructive-500">*</span>
              </Label>
              <div className="space-y-2 mt-2">
                {mainTopics.map((topic) => (
                  <div key={topic.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${topic.id}`}
                      checked={selectedCategories.includes(topic?.label)}
                      onCheckedChange={(checked) =>
                        handleCategoryChange(topic.label, checked === true)
                      }
                    />
                    <Label
                      htmlFor={`category-${topic.id}`}
                      className="font-normal text-neutral-500 text-[18px] font-normal"
                    >
                      {topic.label}
                    </Label>
                  </div>
                ))}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-other"
                    checked={selectedCategories.includes('Other')}
                    onCheckedChange={(checked) =>
                      handleCategoryChange('Other', checked === true)
                    }
                  />
                  <Label
                    htmlFor="category-other"
                    className="font-normal text-neutral-500 text-[18px] font-normal"
                  >
                    Other
                  </Label>
                </div>
              </div>
              {selectedCategories.includes('Other') && (
                <Input
                  className="mt-2"
                  placeholder="Please specify..."
                  value={otherCategory}
                  onChange={(e) => setOtherCategory(e.target.value)}
                />
              )}
            </div>

            <div>
              <Label
                htmlFor="website"
                className="text-neutral-900 text-[18px] font-normal"
              >
                Website: <span className="text-neutral-500">(Optional)</span>
              </Label>
              <Input
                id="website"
                placeholder="Enter your website url"
                value={website}
                onChange={(e) => setWebsite(e.target.value)}
                className="mt-2"
              />
            </div>

            <div>
              <Label
                htmlFor="description"
                className="text-neutral-900 text-[18px] font-normal"
              >
                Brief Description of Your Training Programs:{' '}
                <span className="text-destructive-500">*</span>
              </Label>
              <Textarea
                id="description"
                placeholder="Enter description"
                value={description}
                onChange={handleDescriptionChange}
                className="mt-2 min-h-[120px]"
              />
              <p className="text-[16px] text-neutral-500 mt-1">
                {descriptionCharCount}/{MAX_DESCRIPTION_CHARS} characters
              </p>
            </div>

            <div>
              <Label className="text-neutral-900 text-[18px] font-normal">
                Accreditations & Certifications:{' '}
                <span className="text-neutral-500">(Optional)</span>
              </Label>

              {uploadedFiles.length > 0 && (
                <div className="mt-2 space-y-2">
                  {uploadedFiles.map((file) => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between p-2 border rounded"
                    >
                      <span className="text-[16px] truncate max-w-[80%]">
                        {file.name}
                      </span>
                      <Button
                        variant="outline"
                        onClick={() => removeFile(file.id)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {uploadedFiles.length < MAX_UPLOADS && (
                <div className="mt-2">
                  <Label
                    htmlFor="file-upload"
                    className="inline-flex items-center px-4 py-2 shadow-sm text-[16px] font-medium cursor-pointer !text-[--buttonColor] border border-[--buttonColor] rounded-[--buttonStyle] hover:bg-[--buttonColorLight]"
                  >
                    <Upload className="h-4 w-4 mr-2 !text-[--buttonColor]" />
                    Upload File
                  </Label>
                  <input
                    id="file-upload"
                    type="file"
                    className="sr-only"
                    onChange={handleFileUpload}
                  />
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? 'Processing...' : 'Request Access'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
