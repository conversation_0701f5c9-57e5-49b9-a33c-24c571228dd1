import type { IPartner } from '@/type';

export interface PartnerState {
  partner: IPartner | null;
  isLoading: boolean;
  showLoader: boolean;
}

export type PartnerAction =
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' }
  | { type: 'SHOW_LOADER' }
  | { type: 'HIDE_LOADER' }
  | { type: 'SET_PARTNER'; payload: IPartner };

export const partnerReducer = (
  state: PartnerState,
  action: PartnerAction
): PartnerState => {
  switch (action.type) {
    case 'START_LOADING':
      return { ...state, isLoading: true };
    case 'STOP_LOADING':
      return { ...state, isLoading: false };
    case 'SHOW_LOADER':
      return { ...state, showLoader: true };
    case 'HIDE_LOADER':
      return { ...state, showLoader: false };
    case 'SET_PARTNER':
      return {
        ...state,
        partner: action.payload,
        isLoading: false,
        showLoader: false,
      };
    default:
      return state;
  }
};
