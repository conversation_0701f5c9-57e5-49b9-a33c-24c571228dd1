export interface CourseSession {
  id?: number;
  key: string;
  value: string;
}

export interface SkillsCovered {
  id?: number;
  key: string;
  value: string;
}

export interface ProgramOutline {
  id?: number;
  key: string;
  value: string;
}

export interface Course {
  id?: string | null;
  name: string;
  description: string;
  coverImage: string;
  mainTopic: string;
  subTopic: string;
  learningObjective: string;
  courseLink: string;
  deliveryMode: string;
  address: string;
  city: string;
  state: string;
  country: string;
  duration: string;
  durationUnit: string;
  isSelfPaced: boolean;
  isFlexible: boolean;
  experienceLevel: string;
  certificationType?: string;
  startDate: string;
  endDate: string;
  courseFee: number;
  createdById?: string;
  lastModifiedById?: string;
  createdOn?: string;
  modifiedOn?: string;
  status?: 'Active' | 'Pending' | 'Draft' | 'Inactive';
  sessions?: CourseSession[];
  programOutlines: ProgramOutline[];
  skillsCovered: SkillsCovered[];
  partner?: {
    id?: string;
    name?: string;
    email?: string;
    partnerId?: string;
    logo?: string;
    phone?: null;
  };
}

export interface CourseRespones {
  data: Course;
}

export type CourseFormStep = 'summary' | 'details' | 'skills' | 'review';

export const MAIN_TOPICS = [
  'Technology',
  'Business',
  'Design',
  'Marketing',
  'Finance',
  'Healthcare',
  'Education',
  'Other',
];

export const DELIVERY_MODES = ['In Person', 'Online', 'Hybrid'];

export const DURATION_UNITS = ['Hours', 'Days', 'Weeks', 'Months', 'Years'];

export const EXPERIENCE_LEVELS = ['Beginner', 'Intermediate', 'Advanced'];

export const CITIES = [
  'New York',
  'San Francisco',
  'London',
  'Tokyo',
  'Sydney',
  'Singapore',
  'Berlin',
  'Paris',
  'Toronto',
  'Other',
];

export interface CourseFilters {
  search?: string;
  mainTopic?: string;
  deliveryMode?: 'In Person' | 'Online' | 'Hybrid';
  city?: string;
  experienceLevel?: 'Beginner' | 'Intermediate' | 'Advanced';
  dateType?: 'startDate' | 'createdOn' | 'modifiedOn';
  dateFrom?: string;
  dateTo?: string;
  startDate?: string;
  endDate?: string;
  createdOn?: string;
  modifiedOn?: string;
  status?: 'Active' | 'Pending' | 'Draft' | 'Inactive';
  createdById?: string;
  sortField?: string;
  page?: number;
  pageSize?: number;
  ascending?: boolean;
}

export interface CourseListResponse {
  data: Course[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ProgramOutline {
  id?: number;
  key: string;
  value: string;
}

export interface CourseSkill {
  name: string;
  category?: string;
}

export interface CourseListResponse {
  data: Course[];
  total: number;
  page: number;
  pageSize: number;
}

export interface Learner {
  id: string;
  name: string;
  email: string;
  enrolledDate: string;
  status: 'Enrolled' | 'Completed';
}

export interface LearnerFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: 'Enrolled' | 'Completed' | 'All';
  sortField?: string;
  ascending?: boolean;
}

export interface LearnerListResponse {
  data: Learner[];
  total: number;
  page: number;
  pageSize: number;
}

export interface CourseEnrollment {
  id: string;
  userId: string;
  userName: string;
  email: string;
  enrollmentDate: string;
  status: 'Enrolled' | 'Completed';
  courseId: string;
}

export interface EnrollmentFilters {
  search?: string;
  enrolledFrom?: string;
  enrolledTo?: string;
  status?: 'Enrolled' | 'Completed' | 'All';
  sortField?: string;
  ascending?: boolean;
}

export interface EnrollmentListResponse {
  success: boolean;
  message: string;
  data: CourseEnrollment[];
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  statusCounts: {
    totalEnrolled: number;
    completed: number;
    notCompleted: number;
  };
}

export interface StatsResponse {
  data: {
    totalCourses: number;
    activeCourses: number;
    completedEnrollments: number;
    totalEnrolledUsers: number;
  };
}

export interface TrainingProviderRequest {
  name: string;
  description: string;
  website: string;
  categories: string[];
  accreditations: string[];
  partnerId: string;
  createdById: string;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
}
