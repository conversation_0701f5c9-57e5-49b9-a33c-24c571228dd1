import axiosClient from '@/utils/axiosClient';
import type { Applicant, ApplicantResponse } from '@/type';

const API_URL = 'Jobs';

const applicantService = {
  getApplicants: async (
    page = 1,
    size = 10,
    sortField: string | null = null,
    ascending = true,
    status: string | null = null,
    createdById: string | null = null,
    search: string | null = null,
    IncludeStatusCounts = true,
    position?: string,
    AppliedOnFrom: string | null = null,
    AppliedOnTo: string | null = null
  ): Promise<ApplicantResponse> => {
    let url = `${API_URL}/applicants/?page=${page}&pageSize=${size}`;

    if (sortField) {
      url += `&sortField=${sortField}`;
    }

    url += `&ascending=${ascending}`;

    if (status) {
      url += `&status=${status}`;
    }

    if (createdById) {
      url += `&createdById=${createdById}`;
    }

    if (search) {
      url += `&search=${search}`;
    }

    if (IncludeStatusCounts) {
      url += `&IncludeStatusCounts=${IncludeStatusCounts}`;
    }

    if (position) {
      url += `&position=${position}`;
    }

    if (AppliedOnFrom) {
      url += `&appliedOnFrom=${AppliedOnFrom}`;
    }

    if (AppliedOnTo) {
      url += `&appliedOnTo=${AppliedOnTo}`;
    }

    const response = await axiosClient.get<ApplicantResponse>(url);
    return {
      ...response.data,
      data: response.data.data.map((applicant) => ({
        ...applicant,
      })),
    };
  },

  getApplicantsbyApplicantId: async (
    jobId: string,
    page = 1,
    size = 10,
    sortField: string | null = null,
    ascending = true,
    status: string | null = null,
    search: string | null = null,
    IncludeStatusCounts = true,
    appliedOnFrom: string | null = null,
    appliedOnTo: string | null = null
  ): Promise<ApplicantResponse> => {
    let url = `${API_URL}/${jobId}/applicants?page=${page}&size=${size}`;

    if (sortField) {
      url += `&sortField=${sortField}`;
    }

    if (ascending) {
      url += `&ascending=${ascending}`;
    }

    if (status) {
      url += `&status=${status}`;
    }

    if (search) {
      url += `&search=${search}`;
    }

    if (IncludeStatusCounts) {
      url += `&IncludeStatusCounts=${IncludeStatusCounts}`;
    }

    // Add AppliedOnFrom and AppliedOnTo to the URL if provided
    if (appliedOnFrom) {
      url += `&appliedOnFrom=${appliedOnFrom}`;
    }

    if (appliedOnTo) {
      url += `&appliedOnTo=${appliedOnTo}`;
    }

    const response = await axiosClient.get<ApplicantResponse>(url);
    return {
      ...response.data,
      data: response.data.data.map((applicant) => ({
        ...applicant,
      })),
    };
  },

  getApplicantById: async (applicantId: string) => {
    try {
      const response = await axiosClient.get(
        `${API_URL}/${applicantId}/applicant`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching applicant with id ${applicantId}:`, error);
      throw new Error('Failed to fetch applicant');
    }
  },

  addApplicant: async (applicant: Applicant): Promise<Applicant> => {
    const response = await axiosClient.post<Applicant>(API_URL, applicant);
    return response.data;
  },

  updateApplicant: async (
    applicantId: string,
    updatedApplicant: Partial<Applicant>
  ): Promise<Applicant> => {
    const response = await axiosClient.put<Applicant>(
      `${API_URL}/${applicantId}/application`,
      updatedApplicant
    );
    return response.data;
  },

  deleteApplicant: async (id: string): Promise<void> => {
    await axiosClient.delete(`${API_URL}/${id}`);
  },
};

export default applicantService;
