import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import React from 'react';
import ListingTable from '../components/listing-table';

function MyListingTemplate() {
  return (
    <div className="space-y-4">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'My Listings',
            href: '/dashboard/jobs-and-training/posting-a-new-listing',
          },
        ]}
      />
      <div className="space-y-3  md:max-w-[60%] leading-[30px]">
        <h4 className="text-neutral-900 font-semibold text-2xl mt-10 text-[40px]">
          My Listings
        </h4>
      </div>
      <div className="mt-20">
        <ListingTable />
      </div>
    </div>
  );
}

export default MyListingTemplate;
