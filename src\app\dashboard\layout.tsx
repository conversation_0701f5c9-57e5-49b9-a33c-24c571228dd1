import type { ReactNode } from 'react';
import React from 'react';
import DashboardLayout from './dashboardLayout';
import AuthGuard from '@/components/auth/authGuard';
// import { Metadata } from "next";
// import {  } from "@/utils/metaTags";

// export const metadata: Metadata = cvBuilderMetaTags

const Layout = ({ children }: { children: ReactNode }) => {
  return (
    <AuthGuard>
      <DashboardLayout>{children}</DashboardLayout>
    </AuthGuard>
  );
};

export default Layout;
