'use client';

import LoginFormTemplate from '@/components/auth/login/templates/LoginFormTemplate';
import { Card, CardContent } from '@/components/ui/card';

const PageLogin = () => {
  return (
    <div className="bg-[url('/images/ws/login-bg.png')] h-screen bg-cover bg-no-repeat bg-center flex flex-row ">
      <div className="w-full lg:w-1/2 z-10 mx-auto flex justify-center items-center">
        <Card className="max-w-[488px] flex-1 rounded-2xl px-5 py-12 md:px-8 min-h-screen md:min-h-0">
          <CardContent className="p-0 pt-6">
            <LoginFormTemplate />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PageLogin;
