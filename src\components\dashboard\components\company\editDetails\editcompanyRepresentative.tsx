/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react';
import { RepresentativeCard } from './representative';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useGetRepresentative,
  useInviteRepresentative,
  useDeleteUserProfile,
  useToggleStatus,
  useUpdateUserProfile,
} from '@/queries';
import { EditRepresentativeModal } from './editRepresentativeModal';
import { ConfirmationModal } from './confirmationModal';
import { SuccessModal } from './successModal';
import { AddRepresentativeModal } from './addRepresentativeModal';
import type { ApiRepresentative } from '@/type';

interface Representative {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  isActive?: boolean;
  permissions: {
    Full_Admin_Access: boolean;
    career: boolean;
    training: boolean;
    finance: boolean;
  };
}

export function EditCompanyRepresentatives() {
  const {
    data: apiResponse,
    isLoading,
    isError,
    refetch,
  } = useGetRepresentative();
  const { mutate: invite, isPending: isInviteLoading } =
    useInviteRepresentative();
  const { mutate: toggleStatus } = useToggleStatus();
  const { mutate: updateProfile } = useUpdateUserProfile();
  const { mutate: deleteUser } = useDeleteUserProfile();

  const [displayRepresentatives, setDisplayRepresentatives] = useState<
    Representative[]
  >([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeactivateModalOpen, setIsDeactivateModalOpen] = useState(false);
  const [isReactivateModalOpen, setIsReactivateModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeactivateSuccessModalOpen, setIsDeactivateSuccessModalOpen] =
    useState(false);
  const [isReactivateSuccessModalOpen, setIsReactivateSuccessModalOpen] =
    useState(false);
  const [isDeleteSuccessModalOpen, setIsDeleteSuccessModalOpen] =
    useState(false);
  const [isInviteSuccessModalOpen, setIsInviteSuccessModalOpen] =
    useState(false);
  const [currentRepresentative, setCurrentRepresentative] =
    useState<Representative | null>(null);
  const [newRepresentative, setNewRepresentative] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: {
      Full_Admin_Access: false,
      career: false,
      training: false,
      finance: false,
    },
  });

  const convertApiToRepresentative = (
    apiRep: ApiRepresentative
  ): Representative => {
    const [firstName, ...lastNameParts] = apiRep.user.fullName.split(' ');
    const lastName = lastNameParts.join(' ');

    const roles = apiRep.role.split(', ').map((r) => r.trim());
    const permissions = {
      Full_Admin_Access: roles.includes('Full_Admin_Access'),
      career: roles.includes('career'),
      training: roles.includes('training'),
      finance: roles.includes('finance'),
    };

    return {
      id: apiRep.userId,
      firstName: firstName || '',
      lastName: lastName || '',
      email: apiRep.user.email,
      role: apiRep.role,
      isActive: apiRep.isActive,
      permissions,
    };
  };

  useEffect(() => {
    if (apiResponse && Array.isArray(apiResponse)) {
      const convertedReps = apiResponse.map(convertApiToRepresentative);
      setDisplayRepresentatives(convertedReps);
    }
  }, [apiResponse]);

  const handleEditClick = (rep: Representative) => {
    setIsAddModalOpen(false);
    setCurrentRepresentative(JSON.parse(JSON.stringify(rep)));
    setIsEditModalOpen(true);
  };

  const handleAddClick = () => {
    setNewRepresentative({
      firstName: '',
      lastName: '',
      email: '',
      role: {
        Full_Admin_Access: false,
        career: false,
        training: false,
        finance: false,
      },
    });
    setIsAddModalOpen(true);
  };

  const formatRoleForAPI = (permissions: any) => {
    const selectedRoles: string[] = [];

    if (permissions.Full_Admin_Access) {
      selectedRoles.push('Full_Admin_Access');
    }
    if (permissions.career) {
      selectedRoles.push('career');
    }
    if (permissions.training) {
      selectedRoles.push('training');
    }
    if (permissions.finance) {
      selectedRoles.push('finance');
    }

    return selectedRoles.join(', ');
  };

  const handleInvite = (isEdit: boolean, representative: any) => {
    if (isEdit && currentRepresentative) {
      const updateData = {
        id: currentRepresentative.id,
        firstName: representative.firstName,
        lastName: representative.lastName,
        email: representative.email || currentRepresentative.email,
        fullName: `${representative.firstName} ${representative.lastName}`,
        role: formatRoleForAPI(representative.permissions),
      };

      updateProfile(updateData, {
        onSuccess: () => {
          const updatedRepresentatives = displayRepresentatives.map((rep) =>
            rep.id === currentRepresentative.id
              ? {
                  ...rep,
                  firstName: representative.firstName,
                  lastName: representative.lastName,
                  role: updateData.role,
                  permissions: representative.permissions,
                }
              : rep
          );
          setDisplayRepresentatives(updatedRepresentatives);
          refetch();
          setIsEditModalOpen(false);
        },
        onError: (error) => {
          alert(
            `Failed to update representative: ${error.message || 'Unknown error'}`
          );
        },
      });
    } else {
      const inviteData = {
        firstName: representative.firstName,
        lastName: representative.lastName,
        email: representative.email,
        role: formatRoleForAPI(representative.permissions),
      };

      invite(inviteData, {
        onSuccess: () => {
          refetch();
          setIsAddModalOpen(false);
          setIsInviteSuccessModalOpen(true);
          setNewRepresentative(representative);
        },
        onError: (error) => {
          alert(`Failed to send invite: ${error.message || 'Unknown error'}`);
        },
      });
    }
  };

  const handleDeactivate = () => {
    if (currentRepresentative) {
      toggleStatus(currentRepresentative.id.toString(), {
        onSuccess: () => {
          const updatedRepresentatives = displayRepresentatives.map((rep) =>
            rep.id === currentRepresentative.id
              ? { ...rep, isActive: false }
              : rep
          );
          setDisplayRepresentatives(updatedRepresentatives);
          setIsDeactivateModalOpen(false);
          setIsDeactivateSuccessModalOpen(true);
        },
        onError: (error) => {
          alert(
            `Failed to deactivate representative: ${error.message || 'Unknown error'}`
          );
        },
      });
    }
  };

  const handleReactivate = () => {
    if (currentRepresentative) {
      toggleStatus(currentRepresentative.id.toString(), {
        onSuccess: () => {
          const updatedRepresentatives = displayRepresentatives.map((rep) =>
            rep.id === currentRepresentative.id
              ? { ...rep, isActive: true }
              : rep
          );
          setDisplayRepresentatives(updatedRepresentatives);
          setIsReactivateModalOpen(false);
          setIsReactivateSuccessModalOpen(true);
        },
        onError: (error) => {
          alert(
            `Failed to reactivate representative: ${error.message || 'Unknown error'}`
          );
        },
      });
    }
  };

  const handleDelete = () => {
    if (currentRepresentative) {
      deleteUser(currentRepresentative.id.toString(), {
        onSuccess: () => {
          const updatedRepresentatives = displayRepresentatives.filter(
            (rep) => rep.id !== currentRepresentative.id
          );
          setDisplayRepresentatives(updatedRepresentatives);
          setIsDeleteModalOpen(false);
          setIsDeleteSuccessModalOpen(true);
        },
        onError: (error) => {
          alert(
            `Failed to delete representative: ${error.message || 'Unknown error'}`
          );
        },
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className="border border-neutral-200 rounded-lg p-4 flex items-start gap-4"
          >
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        ))}
        <div className="mt-6">
          <Skeleton className="h-10 w-48" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8 text-destructive-600">
        <p>Failed to load representatives. Please try again later.</p>
        <Button onClick={() => refetch()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  if (!displayRepresentatives.length) {
    return (
      <div className="text-center py-8 text-neutral-500">
        <p>No representatives found.</p>
        <Button onClick={handleAddClick} className="mt-4">
          <PlusIcon className="mr-2 h-4 w-4" /> Add First Representative
        </Button>
      </div>
    );
  }

  return (
    <div className="">
      <div className="space-y-4">
        {displayRepresentatives.map((rep) => (
          <RepresentativeCard
            key={rep.id}
            representative={rep}
            onEdit={() => handleEditClick(rep)}
            onDeactivate={() => {
              setCurrentRepresentative(rep);
              setIsDeactivateModalOpen(true);
            }}
            onReactivate={() => {
              setCurrentRepresentative(rep);
              setIsReactivateModalOpen(true);
            }}
            onDelete={() => {
              setCurrentRepresentative(rep);
              setIsDeleteModalOpen(true);
            }}
          />
        ))}

        <div className="mt-6">
          <Button
            variant="outline"
            type="button"
            className="border border-primary-500 text-primary-500"
            onClick={handleAddClick}
            disabled={isInviteLoading}
          >
            <PlusIcon className="mr-2 h-4 w-4" />
            {isInviteLoading
              ? 'Sending Invite...'
              : 'Add Another Representative'}
          </Button>
        </div>
      </div>

      <AddRepresentativeModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onInvite={(representative) => handleInvite(false, representative)}
        initialData={newRepresentative}
      />

      <EditRepresentativeModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={(representative) => handleInvite(true, representative)}
        representative={currentRepresentative}
      />

      <ConfirmationModal
        isOpen={isDeactivateModalOpen}
        onClose={() => setIsDeactivateModalOpen(false)}
        onConfirm={handleDeactivate}
        title="Deactivate Account"
        description="Are you sure you want to deactivate this user?"
        confirmText="Deactivate Account"
        representative={currentRepresentative}
        type="deactivate"
      />

      <ConfirmationModal
        isOpen={isReactivateModalOpen}
        onClose={() => setIsReactivateModalOpen(false)}
        onConfirm={handleReactivate}
        title="Reactivate Account"
        description="Are you sure you want to reactivate this user?"
        confirmText="Reactivate Account"
        representative={currentRepresentative}
        type="reactivate"
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title="Delete Account"
        description="Are you sure you want to delete this user?"
        confirmText="Delete Account"
        representative={currentRepresentative}
        type="delete"
        confirmButtonVariant="destructive"
      />

      <SuccessModal
        isOpen={isInviteSuccessModalOpen}
        onClose={() => setIsInviteSuccessModalOpen(false)}
        title="Invitation Sent Successfully!"
        description={`An invitation has been sent to ${newRepresentative.firstName} ${newRepresentative.lastName} at ${newRepresentative.email}.`}
        subDescription="Once they accept the invite, they'll be granted access based on the assigned permissions."
        icon="🎉"
      />

      <SuccessModal
        isOpen={isDeactivateSuccessModalOpen}
        onClose={() => setIsDeactivateSuccessModalOpen(false)}
        title="Account Deactivated"
        description="This user's account has been successfully deactivated."
      />

      <SuccessModal
        isOpen={isReactivateSuccessModalOpen}
        onClose={() => setIsReactivateSuccessModalOpen(false)}
        title="Account Reactivated"
        description="This user's account has been successfully reactivated."
      />

      <SuccessModal
        isOpen={isDeleteSuccessModalOpen}
        onClose={() => setIsDeleteSuccessModalOpen(false)}
        title="Account Deleted"
        description="This user's account has been permanently deleted from the platform."
      />
    </div>
  );
}
