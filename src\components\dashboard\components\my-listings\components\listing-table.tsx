// components/MyListings.tsx
'use client';

import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card } from '@/components/ui/card';
import { AlertCircle, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useMyListings } from '@/hooks/useJobListing';
import { TabSelector } from './template/table-selector';
import { JobTable } from './template/job-table';
import { PaginationControls } from './template/pagination-controller';
import { LoadingState } from './template/loading-state';
import { ListingFilterPanel } from './listingFilter';

export default function MyListings() {
  const {
    state: {
      activeTab,
      localSearch,
      jobs,
      total,
      totalActive,
      totalClosed,
      totalPages,
      page,
      sortField,
      ascending,
      uiFilters,
      isLoading,
      error,
    },
    actions: {
      setActiveTab,
      handleSort,
      handleFilterChange,
      handleApplyFilters,
      handleResetFilters,
      handlePageChange,
      handleRetry,
      handleSearchChange,
      handleSearchKeyDown,
      refreshJobs,
    },
  } = useMyListings();

  useEffect(() => {
    refreshJobs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="">
      <Card className="mt-10 rounded-lg py-10 px-5">
        {error ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
            <Button onClick={handleRetry} className="mt-2">
              Retry
            </Button>
          </Alert>
        ) : (
          <>
            <div className="flex items-center justify-between mb-4">
              <TabSelector
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                countActive={totalActive}
                countClosed={totalClosed}
              />
            </div>

            <p className="text-[16px] font-semibold leading-[24px] text-neutral-900 my-3">
              Filter:
            </p>
            <div className="flex items-center gap-2 border border-neutral-300 px-2 rounded-lg mb-4">
              <Search className="text-neutral-500" />
              <Input
                placeholder="Search keyword..."
                value={localSearch}
                onChange={handleSearchChange}
                onKeyDown={handleSearchKeyDown}
                className="w-full border-none text-[16pc] text-neutral-500"
              />
            </div>

            <div className="mb-6">
              <ListingFilterPanel
                filters={uiFilters}
                onFilterChange={handleFilterChange}
                onApplyFilters={handleApplyFilters}
                onResetFilters={handleResetFilters}
              />
            </div>

            {isLoading && <LoadingState />}
            {!isLoading && (
              <JobTable
                jobs={jobs}
                sortField={sortField}
                ascending={ascending}
                handleSort={handleSort}
                activeTab={activeTab}
              />
            )}

            {total > 0 && (
              <PaginationControls
                page={page}
                totalPages={totalPages}
                handlePageChange={handlePageChange}
              />
            )}
          </>
        )}
      </Card>

      <p className="pt-8 font-normal text-[20px] leading-[16px]">
        Need assistance?{' '}
        <span className="font-semibold text-[20px] leading-[16px] text-primary-500 ml-1">
          Contact Support
        </span>
      </p>
    </div>
  );
}
