import type { UseQueryResult } from '@tanstack/react-query';
import { useQuery, QueryClient } from '@tanstack/react-query';
import jobService from '@/zustand/services/jobServices';
import { useJobStore } from '@/zustand/store/jobStore';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: false,
    },
  },
});

// React Query hooks for data fetching
export const useJobs = <T = unknown>(filters?: {
  listingType?: string;
  createdOnFrom?: string;
  createdOnTo?: string;
  closeDateFrom?: string;
  closeDateTo?: string;
}): UseQueryResult<T, Error> => {
  const {
    page,
    size,
    searchTerm,
    status,
    userId,
    ascending,
    sortField,
    statusCounts,
  } = useJobStore();

  return useQuery({
    queryKey: [
      'jobs',
      page,
      size,
      searchTerm,
      status,
      userId,
      ascending,
      sortField,
      statusCounts,
      filters?.listingType,
      filters?.createdOnFrom,
      filters?.createdOnTo,
      filters?.closeDateFrom,
      filters?.closeDateTo,
    ],
    queryFn: async () => {
      const result = await jobService.getJobs(
        page,
        size,
        searchTerm,
        userId || undefined,
        status || undefined,
        ascending,
        sortField,
        true, // IncludeStatusCounts
        filters?.listingType,
        filters?.createdOnFrom,
        filters?.createdOnTo,
        filters?.closeDateFrom,
        filters?.closeDateTo
      );

      return result as T; // Cast the result to the generic type T
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Other hooks remain unchanged
export const useAllJobs = () => {
  const { searchTerm, status, userId, ascending, sortField } = useJobStore();

  return useQuery({
    queryKey: ['allJobs', searchTerm, status, userId, ascending, sortField],
    queryFn: () =>
      jobService.getJobs(
        1, // Default page
        1000, // Fetch all jobs
        searchTerm,
        userId || undefined,
        status || undefined,
        ascending,
        sortField
      ),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useJobById = (id: string) => {
  return useQuery({
    queryKey: ['job', id],
    queryFn: () => jobService.getJobById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useJobsByUserId = (userId: string) => {
  return useQuery({
    queryKey: ['userJobs', userId],
    queryFn: () => jobService.getJobsByUserId(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Helper function to manually invalidate job queries from outside React components
export const invalidateJobQueries = () => {
  queryClient.invalidateQueries({ queryKey: ['jobs'] });
  queryClient.invalidateQueries({ queryKey: ['allJobs'] });
  queryClient.invalidateQueries({ queryKey: ['userJobs'] });
};
