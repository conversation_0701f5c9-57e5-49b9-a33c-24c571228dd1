'use client';

import type React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface UnsavedChangesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDiscard: () => void;
  onKeepEditing: () => void;
}

export const UnsavedChangesDialog: React.FC<UnsavedChangesDialogProps> = ({
  isOpen,
  onClose,
  onDiscard,
  onKeepEditing,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogTitle>Unsaved Changes</DialogTitle>
        <DialogDescription>
          You have unsaved changes. If you leave now, your changes will be lost.
        </DialogDescription>
        <div className="flex justify-end gap-3 mt-4">
          <Button
            className="rounded-[--buttonStyle] cursor-pointer border border-destructive-500 !text-destructive-500 hover:bg-destructive-50"
            variant="outline"
            onClick={onDiscard}
          >
            Discard Changes
          </Button>
          <Button className="cursor-pointer" onClick={onKeepEditing}>
            Keep Editing
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
