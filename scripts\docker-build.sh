#!/bin/bash
# Docker build script for Linux/macOS
# Career Navigator Pro Partner UI

set -e

echo "Building Career Navigator Pro Partner UI Docker image..."

# Set variables
IMAGE_NAME="career-navigator-ui"
TAG="latest"

# Make sure the script is executable
chmod +x "$0"

# Build the Docker image
echo "Building production image..."
docker build -t "${IMAGE_NAME}:${TAG}" .

if [ $? -eq 0 ]; then
    echo ""
    echo "Docker image built successfully!"
    echo "Image: ${IMAGE_NAME}:${TAG}"
    echo ""
    echo "To run the container:"
    echo "docker run -p 3000:3000 ${IMAGE_NAME}:${TAG}"
    echo ""
    echo "Or use docker-compose:"
    echo "docker-compose up"
else
    echo ""
    echo "Docker build failed!"
    exit 1
fi
