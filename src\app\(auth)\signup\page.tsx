'use client';

import SignUpForm from '@/components/auth/signup';
import { Card, CardContent } from '@/components/ui/card';

const PageLogin = () => {
  return (
    <div className="bg-[url('/images/ws/login-bg.png')] h-screen bg-cover bg-no-repeat bg-center flex flex-row overflow-y-auto py-0 ">
      <div className="w-full lg:w-1/2 z-10 mx-auto flex justify-center items-center">
        <Card className="max-w-[600px] flex-1 rounded-2xl px-5 py-4 md:px-12 min-h-screen= md:min-h-0">
          <CardContent className="p-0 pt-6">
            <SignUpForm />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PageLogin;
