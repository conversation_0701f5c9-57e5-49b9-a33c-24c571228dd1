import { useCallback } from 'react';

/**
 * React hook to download a base64 file directly.
 *
 * @returns A function that accepts a base64 string and an optional filename, and triggers a download.
 */
export function useBase64Downloader() {
  const download = useCallback((base64Data: string, filename = 'download') => {
    if (!base64Data.startsWith('data:')) {
      console.error('Invalid base64 data URL.');
      return;
    }

    try {
      // Create a temporary <a> element
      const link = document.createElement('a');
      link.href = base64Data;

      // Try to determine extension from MIME type
      const mimeMatch = base64Data.match(/^data:(.*?);base64,/);
      const mimeType = mimeMatch ? mimeMatch[1] : '';
      let extension = '';

      if (mimeType) {
        if (mimeType.includes('pdf')) extension = 'pdf';
        else if (mimeType.includes('png')) extension = 'png';
        else if (mimeType.includes('jpeg') || mimeType.includes('jpg'))
          extension = 'jpg';
        else if (mimeType.includes('docx')) extension = 'docx';
        else if (mimeType.includes('msword')) extension = 'doc';
      }

      const finalFilename =
        extension && !filename.includes('.')
          ? `${filename}.${extension}`
          : filename;

      link.download = finalFilename;

      // Append, trigger click, clean up
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Failed to download base64 file:', error);
    }
  }, []);

  return download;
}
