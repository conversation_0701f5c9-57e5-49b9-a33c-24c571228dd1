'use client';

import type { ReactNode, ButtonHTMLAttributes } from 'react';
import { Button } from '@/components/ui/button';
import { usePermission } from '@/hooks/usePermission';
import type { Permission, PermissionCheckOptions } from '@/types/permission';

interface PermissionButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  permission: Permission | Permission[];
  children: ReactNode;
  options?: PermissionCheckOptions;
  fallback?: ReactNode;
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  hideWhenNoPermission?: boolean;
  disableWhenNoPermission?: boolean;
  tooltipText?: string;
}

export function PermissionButton({
  permission,
  children,
  options,
  fallback = null,
  variant = 'default',
  size = 'default',
  hideWhenNoPermission = false,
  disableWhenNoPermission = true,
  tooltipText,
  className,
  ...buttonProps
}: PermissionButtonProps) {
  const { checkPermission, isLoading } = usePermission();

  const hasPermission = checkPermission(permission, options);

  // Hide button completely if no permission and hideWhenNoPermission is true
  if (!hasPermission && hideWhenNoPermission) {
    return <>{fallback}</>;
  }

  // Show loading state
  if (isLoading) {
    return (
      <Button variant={variant} size={size} disabled className={className}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
        Loading...
      </Button>
    );
  }

  // Disable button if no permission and disableWhenNoPermission is true
  const isDisabled =
    (!hasPermission && disableWhenNoPermission) || buttonProps.disabled;

  return (
    <Button
      variant={variant}
      size={size}
      disabled={isDisabled}
      className={className}
      title={!hasPermission && tooltipText ? tooltipText : undefined}
      {...buttonProps}
    >
      {children}
    </Button>
  );
}
