import React from 'react';

function FolderIcon({ width = 48, height = 48, stopColor = '#4568DC' }) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.40335 20.992C8.61515 20.9597 8.83247 20.9429 9.05418 20.9429H38.9454C39.1671 20.9429 39.3845 20.9597 39.5963 20.992M8.40335 20.992C6.14232 21.3376 4.50864 23.455 4.84335 25.798L6.46377 37.1409C6.76313 39.2364 8.5578 40.7929 10.6746 40.7929H37.325C39.4418 40.7929 41.2365 39.2364 41.5358 37.1409L43.1563 25.798C43.491 23.455 41.8573 21.3376 39.5963 20.992M8.40335 20.992V13.8536C8.40335 11.5044 10.3077 9.59998 12.6569 9.59998H19.9895C20.7416 9.59998 21.4628 9.89874 21.9946 10.4305L26.005 14.4409C26.5368 14.9727 27.258 15.2714 28.0101 15.2714H35.3427C37.6919 15.2714 39.5963 17.1758 39.5963 19.525V20.992"
        stroke="url(#paint0_linear_4747_13051)"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4747_13051"
          x1="4.7998"
          y1="9.59998"
          x2="35.3307"
          y2="47.1851"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={stopColor} />
          <stop offset="1" stopColor={stopColor} />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default FolderIcon;
