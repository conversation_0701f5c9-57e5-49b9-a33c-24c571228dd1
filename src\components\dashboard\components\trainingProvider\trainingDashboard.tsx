'use client';

import type React from 'react';
import { <PERSON><PERSON><PERSON>, Users, Award } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export const TrainingDashboardWidgets: React.FC = () => {
  const router = useRouter();

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Training Provider</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-[16px] font-medium">
              Total Courses
            </CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-[16px] text-muted-foreground">
              No courses created yet
            </p>
            <Button
              className="w-full mt-4"
              size="sm"
              onClick={() => router.push('/dashboard/training/courses/new')}
            >
              Create Course
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-[16px] font-medium">
              Total Learners
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-[16px] text-muted-foreground">
              No learners enrolled yet
            </p>
            <Button
              className="w-full mt-4"
              size="sm"
              variant="outline"
              onClick={() => router.push('/dashboard/training/learners')}
            >
              View Learners
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-[16px] font-medium">
              Certifications
            </CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-[16px] text-muted-foreground">
              No certifications issued yet
            </p>
            <Button
              className="w-full mt-4"
              size="sm"
              variant="outline"
              onClick={() => router.push('/dashboard/training/certifications')}
            >
              Manage Certifications
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
