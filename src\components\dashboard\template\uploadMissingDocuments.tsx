'use client';

import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { DeleteIcon, Upload } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { RxCaretLeft } from 'react-icons/rx';

function UploadMissingDocuments() {
  const router = useRouter();

  const [pdf, setPdf] = useState<File | null>(null);

  const handlePdfUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;
    if (file) {
      if (file.type === 'application/pdf') {
        setPdf(file);
      } else {
        alert('Please upload a PDF file.');
      }
    }
  };

  const handleDeletePdf = () => {
    setPdf(null);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  return (
    <div>
      <DynamicBreadcrumb
        customBreadcrumbs={[
          { label: 'Pending Actions', href: '/dashboard/pending-actions' },
          {
            label: 'Upload missing documents',
            href: '/dashboard/pending-actions/upload-missing-document',
          },
        ]}
      />

      <Button
        onClick={() => router.back()}
        variant="outline"
        className="py-6 px-6  my-6 justify-between"
      >
        <RxCaretLeft className="w-16 h-16" />
        Back
      </Button>

      <Card className="py-10 px- rounded-md px-10">
        <CardTitle className=" text-[24px] font-semibold leading-[22px] text-neutral-900">
          Upload missing documents
        </CardTitle>
        <CardContent className="px-0 py-2">
          <p className=" text-[18px] font-normal leading-[28px] text-neutral-700">
            {' '}
            Employment contract and most recent payslip required for the
            National Savings Program.
          </p>

          <div className=" flex w-[90%] gap-6">
            <div className=" border border-neutral-200] rounded-lg p-6 mt-4 w-full">
              <h2 className="font-medium leading-[28px] text-[20px] text-neutral-900 py-2">
                {' '}
                Employment contract{' '}
              </h2>
              {pdf ? (
                <div className=" w-[50%] my-4 border border-black rounded-md p-3">
                  <div className="flex justify-between">
                    <h2 className="w-1/2">{pdf.name}</h2>
                    <button
                      className=" text-[#931035]"
                      onClick={handleDeletePdf}
                    >
                      <DeleteIcon />
                    </button>
                  </div>
                  <div className="flex gap-3">
                    <p className=" text-[#646A77]">
                      {Math.round(pdf.size / 1024)} KB
                    </p>
                    <p className=" text-[#646A77]">{formatDate(new Date())}</p>
                  </div>
                </div>
              ) : (
                <p className=" mb-4 text-[16px] font-normal leading-[18px] tracking-wider  text-[#646A77]">
                  PDF, DOCX, or JPG; Max size: 5MB
                </p>
              )}

              <input
                type="file"
                accept="application/pdf"
                onChange={handlePdfUpload}
                style={{ display: 'none' }}
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button className=" flex px-4 py-6  gap-2">
                  <Upload />
                  Upload PDF
                </Button>
              </label>
            </div>

            <div className=" border border-neutral-200] rounded-lg p-6 mt-4 w-full">
              <h2 className="font-medium leading-[28px] text-[20px] text-neutral-900 py-2">
                {' '}
                Employment contract{' '}
              </h2>
              {pdf ? (
                <div className=" w-[50%] my-4 border border-black rounded-md p-3">
                  <div className="flex justify-between">
                    <h2 className="w-1/2">{pdf.name}</h2>
                    <button
                      className=" text-[#931035]"
                      onClick={handleDeletePdf}
                    >
                      <DeleteIcon />
                    </button>
                  </div>
                  <div className="flex gap-3">
                    <p className=" text-[#646A77]">
                      {Math.round(pdf.size / 1024)} KB
                    </p>
                    <p className=" text-[#646A77]">{formatDate(new Date())}</p>
                  </div>
                </div>
              ) : (
                <p className=" mb-4 text-[16px] font-normal leading-[18px] tracking-wider  text-[#646A77]">
                  PDF, DOCX, or JPG; Max size: 5MB
                </p>
              )}

              <input
                type="file"
                accept="application/pdf"
                onChange={handlePdfUpload}
                style={{ display: 'none' }}
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button className=" flex px-4 py-6  gap-2">
                  <Upload />
                  Upload PDF
                </Button>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      <p className="py-8 font-normal  text-[20px] leading-[16px]">
        Need assistance?{' '}
        <span className="  font-semibold text-[20px] leading-[16px] text-primary-500">
          Contact Support
        </span>
      </p>
    </div>
  );
}

export default UploadMissingDocuments;
