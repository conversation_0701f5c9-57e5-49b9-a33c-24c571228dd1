'use client';
import React from 'react';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import CantFindWhatYouNeed from '../../Eligible/components/CantFindWhatYouNeed';
import PaymentTable from '../Components/PaymentTable';

const PaymentHistoryTemplate = () => {
  return (
    <div className="space-y-4">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Financial Benefits',
            href: '/dashboard/financial-benefits/payments',
          },
          {
            label: 'Benefits Payments',
            href: '/dashboard/financial-benefits/payments',
          },
        ]}
      />
      <div className="space-y-3  md:max-w-[60%] leading-[30px]">
        <h4 className="text-neutral-900 font-semibold text-2xl">
          Benefits History
        </h4>
        <p className="text-neutral-700 text-[16px] font-normal">
          Your upcoming and past payments are displayed here.
        </p>
      </div>
      <PaymentTable />
      <CantFindWhatYouNeed />
    </div>
  );
};

export default PaymentHistoryTemplate;
