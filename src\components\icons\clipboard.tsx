import React from 'react';

function ClipboardListIcon({ width = 48, height = 48, stopColor = '#4568DC' }) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 24H25.5M18 30H25.5M18 36H25.5M31.5 37.5H36C38.4853 37.5 40.5 35.4853 40.5 33V12.2164C40.5 9.94647 38.81 8.02031 36.5478 7.83255C35.8013 7.77058 35.0523 7.71716 34.301 7.67238M22.699 7.67238C22.5697 8.09203 22.5 8.53788 22.5 9C22.5 9.82843 23.1716 10.5 24 10.5H33C33.8284 10.5 34.5 9.82843 34.5 9C34.5 8.53788 34.4303 8.09203 34.301 7.67238M22.699 7.67238C23.2655 5.83514 24.9768 4.5 27 4.5H30C32.0232 4.5 33.7345 5.83514 34.301 7.67238M22.699 7.67238C21.9477 7.71716 21.1987 7.77058 20.4522 7.83255C18.19 8.02031 16.5 9.94648 16.5 12.2164V16.5M16.5 16.5H9.75C8.50736 16.5 7.5 17.5074 7.5 18.75V41.25C7.5 42.4926 8.50736 43.5 9.75 43.5H29.25C30.4926 43.5 31.5 42.4926 31.5 41.25V18.75C31.5 17.5074 30.4926 16.5 29.25 16.5H16.5ZM13.5 24H13.515V24.015H13.5V24ZM13.5 30H13.515V30.015H13.5V30ZM13.5 36H13.515V36.015H13.5V36Z"
        stroke="url(#paint0_linear_4747_13025)"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4747_13025"
          x1="7.5"
          y1="4.5"
          x2="45.9621"
          y2="37.0448"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={stopColor} />
          <stop offset="1" stopColor={stopColor} />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default ClipboardListIcon;
