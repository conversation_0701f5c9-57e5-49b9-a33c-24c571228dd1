'use client';

import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import type { Applicant } from '@/type';
import applicantService from '@/zustand/services/applicantServices';
import { motion, AnimatePresence } from 'framer-motion';
import ApplicantProgressTracker from '@/components/dashboard/components/applicants/template/ui/applicationProgressTracker';
import ApplicantDetails from '@/components/dashboard/components/applicants/template/ui/applicantDetails';
import ApplicantActions from '@/components/dashboard/components/applicants/template/ui/applicationActions';
import ApplicantDetailHeader from '@/components/dashboard/components/applicants/template/ui/applicantDetailHeader';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { Loader } from '@/components/dashboard/common/Loader';

export default function ApplicantDetailPage() {
  const router = useRouter();
  const { id } = useParams();
  const queryClient = useQueryClient();

  const userId = useAuthStore((state) => state?.user?.id);

  const [applicant, setApplicant] = useState<Applicant | null>(null);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isNavigating, setIsNavigating] = useState<boolean>(false);
  const [allApplicants, setAllApplicants] = useState<Applicant[]>([]);
  const [totalApplicants, setTotalApplicants] = useState<number>(0);

  const openHireFlow = (applicantId: string) => {
    console.warn(applicantId);
  };

  const { data: applicantDetails, isLoading: isLoadingApplicant } = useQuery({
    queryKey: ['applicant', id],
    queryFn: async () => {
      if (!id) return null;
      const response = await applicantService.getApplicantById(id.toString());
      return response;
    },
  });

  const {
    data: applicantsData,
    // isLoading: isLoadingApplicants,
    isFetching: isFetchingApplicants,
  } = useQuery({
    queryKey: ['applicants', userId],
    queryFn: async () => {
      const response = await applicantService.getApplicants(
        1,
        1000,
        null,
        false,
        '',
        userId
      );
      return response;
    },
  });

  const refreshJobs = () => {
    queryClient.invalidateQueries({ queryKey: ['applicant'] });
    queryClient.invalidateQueries({ queryKey: ['applicants'] });
  };

  useEffect(() => {
    refreshJobs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { mutateAsync: updateStatus } = useMutation({
    mutationFn: async ({
      id,
      status,
    }: {
      id: string;
      status: Applicant['status'];
    }) => {
      await applicantService.updateApplicant(id, { status });
    },
    onSuccess: (_, { id, status }) => {
      setApplicant((prev) => (prev ? { ...prev, status } : null));
      queryClient.invalidateQueries({ queryKey: ['applicant', id] });
      queryClient.invalidateQueries({
        queryKey: ['applicants', status, userId],
      });
    },
    onError: (error) => {
      console.error('Failed to update applicant status', error);
    },
  });

  useEffect(() => {
    if (applicantsData) {
      setAllApplicants(applicantsData.data);
      setTotalApplicants(applicantsData.total);
    }
  }, [applicantsData]);

  useEffect(() => {
    if (applicantDetails) {
      setApplicant(applicantDetails.data);
    }
  }, [applicantDetails]);

  useEffect(() => {
    if (!id || allApplicants.length === 0) return;
    const index = allApplicants.findIndex((app) => String(app.id) === id);
    setCurrentIndex(index !== -1 ? index : 0);
  }, [id, allApplicants]);

  const navigateToApplicant = async (direction: 'next' | 'prev') => {
    if (allApplicants.length === 0 || isNavigating || isLoadingApplicant)
      return;

    setIsNavigating(true);

    const newIndex = direction === 'next' ? currentIndex + 1 : currentIndex - 1;

    const nextApplicantId = allApplicants[newIndex]?.id;
    if (nextApplicantId) {
      router.replace(
        `/dashboard/jobs-and-training/applicants/${nextApplicantId}`
      );
      await new Promise((resolve) => setTimeout(resolve, 100));
      setCurrentIndex(newIndex);
    }

    setIsNavigating(false);
  };

  const handleStatusUpdate = async (status: Applicant['status']) => {
    if (!applicant || !applicant.id) {
      console.error(
        'Cannot update status: applicant or applicant.id is undefined'
      );
      return;
    }

    try {
      await updateStatus({ id: applicant.id.toString(), status });
      // setStatus(status);
    } catch (error) {
      console.error(`Failed to update status to ${status}`, error);
    }
  };

  if (isLoadingApplicant && !applicant) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
        <Loader />
      </div>
    );
  }

  if (!applicant) {
    return (
      <div className="p-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2  px-4 py-2 text-[16px] font-medium"
        >
          <ChevronLeft className="h-4 w-4" /> Back
        </Button>
        <div className="mt-4 p-6 bg-white rounded-lg border border-red-100">
          <h2 className="text-xl font-semibold text-destructive-500">
            Applicant not found
          </h2>
          <p className="mt-2 text-neutral-600">
            The applicant you`re looking for could not be found. They may have
            been removed or the ID is incorrect.
          </p>
          <Button
            className="mt-4"
            variant="outline"
            onClick={() =>
              router.push('/dashboard/jobs-and-training/applicants')
            }
          >
            View All Applicants
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto p-4 space-y-6 w-[1100px]">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Applicants',
            href: '/dashboard/jobs-and-training/applicants',
          },
          {
            label: `${applicantDetails?.data?.user?.userName || 'N/A'}`,
            href: `/dashboard/jobs-and-training/applicants`,
          },
        ]}
      />

      <div className="flex justify-between items-center mt-10">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2 px-[28px] py-[14px] text-[16px] font-medium mt-5 h-[46px]"
        >
          <ChevronLeft className="h-4 w-4" /> Back to Applicants
        </Button>
      </div>

      <AnimatePresence mode="wait">
        <motion.div
          key={applicant.id}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white rounded-lg p-6 h-auto w-full space-y-6">
            <ApplicantDetailHeader
              applicant={applicantDetails?.data}
              currentIndex={currentIndex}
              total={totalApplicants}
              navigateToApplicant={navigateToApplicant}
              isNavigating={isNavigating}
              isLoadingApplicant={isLoadingApplicant || isFetchingApplicants}
            />
            <ApplicantProgressTracker applicant={applicantDetails?.data} />
            <ApplicantDetails applicant={applicantDetails?.data} />
            <ApplicantActions
              applicant={applicantDetails?.data}
              handleStatusUpdate={handleStatusUpdate}
              isUpdating={false}
              openHireFlow={openHireFlow}
            />
          </div>
        </motion.div>
      </AnimatePresence>

      <p className="font-normal text-[20px] leading-[16px]">
        Need assistance?{' '}
        <span className="font-semibold text-[20px] leading-[16px] text-primary-500 ml-1">
          Contact Support
        </span>
      </p>
    </div>
  );
}
