'use client';

import { useEffect, useRef, useState } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface RichTextEditorProps {
  value: string;
  onChange: (_html: string) => void;
  placeholder?: string;
  className?: string;
  maxWords?: number;
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Enter text here…',
  className = '',
  maxWords = 1200,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [wordCount, setWordCount] = useState(0);
  const [isEmpty, setIsEmpty] = useState(true);
  const [, forceUpdate] = useState(0);

  const updateToolbar = () => forceUpdate((x) => x + 1);
  useEffect(() => {
    document.addEventListener('selectionchange', updateToolbar);
    return () => document.removeEventListener('selectionchange', updateToolbar);
  }, []);

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
      updateMeta(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const stripHtml = (html: string) => html.replace(/<[^>]*>/g, ' ').trim();

  const updateMeta = (html: string) => {
    const plain = stripHtml(html);
    const words = plain.split(/\s+/).filter(Boolean);
    setIsEmpty(!plain);
    setWordCount(words.length);
  };

  const execCommand = (command: string, arg = '') => {
    document.execCommand(command, false, arg);

    if (editorRef.current) {
      const html = editorRef.current.innerHTML;
      updateMeta(html);
      onChange(html);
      editorRef.current.focus();
    }
  };

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    const html = (e.currentTarget as HTMLDivElement).innerHTML;
    const plain = stripHtml(html);
    const words = plain.split(/\s+/).filter(Boolean);

    if (words.length > maxWords) {
      document.execCommand('undo');
      return;
    }

    updateMeta(html);
    onChange(html);
  };

  const isActive = (command: string) => {
    try {
      return document.queryCommandState(command);
    } catch {
      return false;
    }
  };

  const handleFontSizeChange = (size: string) => {
    document.execCommand('fontSize', false, '7');
    const fontElements = document.getElementsByTagName('font');
    for (let i = 0; i < fontElements.length; i++) {
      const fontEl = fontElements[i];
      if (fontEl.size === '7') {
        fontEl.removeAttribute('size');
        fontEl.style.fontSize = `${size}px`;
      }
    }
    if (editorRef.current) {
      const html = editorRef.current.innerHTML;
      updateMeta(html);
      onChange(html);
    }
  };

  const handleFontColorChange = (color: string) => {
    execCommand('foreColor', color);
  };

  return (
    <div className="flex flex-col border rounded-md w-full">
      {/* Toolbar */}
      <div className="flex flex-wrap gap-1 p-2 bg-muted/50 items-center">
        <TooltipProvider>
          <EditorButton
            command="bold"
            label="Bold"
            icon={Bold}
            isActive={isActive('bold')}
            execCommand={execCommand}
          />
          <EditorButton
            command="italic"
            label="Italic"
            icon={Italic}
            isActive={isActive('italic')}
            execCommand={execCommand}
          />
          <EditorButton
            command="underline"
            label="Underline"
            icon={Underline}
            isActive={isActive('underline')}
            execCommand={execCommand}
          />

          <div className="h-4 w-px bg-muted-foreground/20 mx-1" />

          <EditorButton
            command="insertUnorderedList"
            label="Bullet List"
            icon={List}
            isActive={isActive('insertUnorderedList')}
            execCommand={execCommand}
          />
          <EditorButton
            command="insertOrderedList"
            label="Numbered List"
            icon={ListOrdered}
            isActive={isActive('insertOrderedList')}
            execCommand={execCommand}
          />

          <div className="h-4 w-px bg-muted-foreground/20 mx-1" />

          <EditorButton
            command="justifyLeft"
            label="Align Left"
            icon={AlignLeft}
            isActive={isActive('justifyLeft')}
            execCommand={execCommand}
          />
          <EditorButton
            command="justifyCenter"
            label="Align Center"
            icon={AlignCenter}
            isActive={isActive('justifyCenter')}
            execCommand={execCommand}
          />
          <EditorButton
            command="justifyRight"
            label="Align Right"
            icon={AlignRight}
            isActive={isActive('justifyRight')}
            execCommand={execCommand}
          />

          <div className="h-4 w-px bg-muted-foreground/20 mx-1" />

          {/* Font Size Dropdown */}
          <Select onValueChange={handleFontSizeChange}>
            <SelectTrigger className="h-8 w-[100px] text-sm">
              <SelectValue placeholder="Font Size" />
            </SelectTrigger>
            <SelectContent>
              {[12, 14, 16, 18, 20, 24, 28].map((size) => (
                <SelectItem key={size} value={String(size)}>
                  {size}px
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Font Color Dropdown */}
          <Select onValueChange={handleFontColorChange}>
            <SelectTrigger className="h-8 w-[100px] text-sm">
              <SelectValue placeholder="Color" />
            </SelectTrigger>
            <SelectContent>
              {[
                'black',
                'gray',
                'red',
                'blue',
                'green',
                'orange',
                'purple',
              ].map((color) => (
                <SelectItem key={color} value={color}>
                  {color.charAt(0).toUpperCase() + color.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </TooltipProvider>
      </div>

      {/* Editable area */}
      <div className="relative min-h-[450px]">
        <div
          ref={editorRef}
          contentEditable
          suppressContentEditableWarning
          className={`min-h-[450px] p-3 focus:outline-none w-full prose prose-sm max-w-none ${className}`}
          onInput={handleInput}
          style={{ overflowY: 'auto' }}
        />
        {isEmpty && (
          <div className="absolute top-3 left-3 text-neutral-500 pointer-events-none select-none">
            {placeholder}
          </div>
        )}
      </div>

      {/* Word counter */}
      <div className="text-right text-[16px] text-muted-foreground p-2 border-t">
        {wordCount} / {maxWords} words
      </div>
    </div>
  );
}

function EditorButton({
  command,
  label,
  icon: Icon,
  isActive,
  execCommand,
}: {
  command: string;
  label: string;
  icon: React.ElementType;
  isActive: boolean;
  execCommand: (_command: string) => void;
}) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          type="button"
          variant={isActive ? 'default' : 'ghost'}
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => execCommand(command)}
        >
          <Icon
            className={`h-4 w-4 ${isActive ? 'text-white' : 'text-[--buttonColor]'}`}
          />
          <span className="sr-only">{label}</span>
        </Button>
      </TooltipTrigger>
      <TooltipContent>{label}</TooltipContent>
    </Tooltip>
  );
}
