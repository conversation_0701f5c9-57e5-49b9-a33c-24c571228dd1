'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import type { Applicant } from '@/type';
import { useUpdateJobStatus } from '@/mutations/useJobMutations';
import { ConfirmModal, MoreActionsDropdown } from './actionFlow';
import { useBase64Downloader } from '@/hooks/useBase64Downloader';
interface ApplicantActionsProps {
  applicant: Applicant;
  status: Applicant['status'];
  onUpdateStatus: (_id: string, _status: Applicant['status']) => Promise<void>;
}

export default function ApplicantActions({
  applicant,
  status,
  onUpdateStatus,
}: ApplicantActionsProps) {
  // Application state management
  const [showHireFlow, setShowHireFlow] = useState(false);
  const [hireFlowStep, setHireFlowStep] = useState(0);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showShortlistModal, setShowShortlistModal] = useState(false);
  const [showContactedModal, setShowContactedModal] = useState(false);

  // Loading states
  const [isRejectLoading, setIsRejectLoading] = useState(false);
  const [isShortlistLoading, setIsShortlistLoading] = useState(false);
  const [isContactedLoading, setIsContactedLoading] = useState(false);
  const [isKeepOpenLoading, setIsKeepOpenLoading] = useState(false);

  const updateJobStatusMutation = useUpdateJobStatus();

  const downloadBase64 = useBase64Downloader();

  // Hire flow functions
  const openHireFlow = () => {
    setHireFlowStep(0);
    setShowHireFlow(true);
  };

  const closeHireFlow = () => {
    setShowHireFlow(false);
    setHireFlowStep(0);
  };

  const handleProceedToListingOptions = () => setHireFlowStep(1);

  const handleKeepListingOpen = async () => {
    setIsKeepOpenLoading(true);
    try {
      await onUpdateStatus(applicant.id, 'Hired');
      closeHireFlow();
    } catch (error) {
      console.error('Error marking as hired:', error);
    } finally {
      setIsKeepOpenLoading(false);
    }
  };

  const handleCloseListing = async () => {
    try {
      await onUpdateStatus(applicant.id, 'Hired');
      if (applicant.job?.id) {
        await updateJobStatusMutation.mutateAsync({
          id: applicant.job.id,
          status: 'Closed',
        });
      }
      closeHireFlow();
    } catch (error) {
      console.error('Failed to close listing:', error);
    }
  };

  // Status update handlers
  const handleStatusUpdate = async (
    status: Applicant['status'],
    loadingSetter: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    loadingSetter(true);
    try {
      await onUpdateStatus(applicant.id, status);
      closeModal();
    } catch (error) {
      console.error(`Error updating status to ${status}:`, error);
    } finally {
      loadingSetter(false);
    }
  };
  const renderActions = () => {
    const commonButtons = (
      <>
        <Button
          variant="outline"
          onClick={() => {
            downloadBase64(
              applicant.cv?.base64Content,
              applicant?.cv?.fileName
            );
          }}
        >
          CV
        </Button>
      </>
    );

    switch (status) {
      case 'Pending':
        return (
          <>
            {commonButtons}
            <Button
              variant="outline"
              onClick={() => setShowShortlistModal(true)}
            >
              Shortlist
            </Button>
            <Button
              variant="outline"
              className="text-destructive-500 border-destructive-500 hover:bg-destructive-500 hover:text-white"
              onClick={() => setShowRejectModal(true)}
            >
              Reject
            </Button>
            <MoreActionsDropdown
              onHire={openHireFlow}
              onContacted={() => setShowContactedModal(true)}
            />
          </>
        );
      case 'Shortlisted':
        return (
          <>
            {commonButtons}
            <Button
              variant="outline"
              onClick={() => setShowContactedModal(true)}
            >
              Contact
            </Button>
            <Button
              variant="outline"
              className="rounded-full text-destructive-500 border-destructive-200"
              onClick={() => setShowRejectModal(true)}
            >
              Reject
            </Button>
            <MoreActionsDropdown onHire={openHireFlow} />
          </>
        );
      case 'Contacted':
        return (
          <>
            {commonButtons}
            <Button variant="outline" onClick={openHireFlow}>
              Hire
            </Button>
            <Button
              variant="outline"
              className="text-destructive-500 border-destructive-200 hover:bg-destructive-500 hover:text-white"
              onClick={() => setShowRejectModal(true)}
            >
              Reject
            </Button>
          </>
        );
      case 'Rejected':
      case 'Hired':
        return commonButtons;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex items-center gap-2">{renderActions()}</div>

      <Dialog open={showHireFlow} onOpenChange={setShowHireFlow}>
        <DialogContent className="w-[440px] p-[24px]">
          {hireFlowStep === 0 && (
            <div className="space-y-4">
              <h2 className="text-[28px] font-semibold">Mark as Hired</h2>
              <p className="text-[16px] text-neutral-500 font-normal">
                Are you sure you want to mark this applicant as hired?
              </p>
              <ul className="list-disc pl-6">
                <li className="font-medium">
                  {applicant.user?.userName || 'Applicant'} for{' '}
                  {applicant.job?.title || 'the position'}
                </li>
              </ul>
              <div className="flex gap-2 justify-end pt-4">
                <Button variant="outline" onClick={closeHireFlow}>
                  Cancel
                </Button>
                <Button onClick={handleProceedToListingOptions}>
                  Continue
                </Button>
              </div>
            </div>
          )}

          {hireFlowStep === 1 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold">Close Listing</h2>
              <p>Would you also like to close this job listing?</p>
              <ul className="list-disc pl-6">
                <li className="font-medium">
                  {applicant.job?.title || 'Current Position'}
                </li>
              </ul>
              <p className="text-muted-foreground">
                Keep this listing open if you would like to continue hiring for
                this role.
              </p>
              <div className="flex flex-col gap-3 pt-2">
                <Button
                  className="py-[14px] px-[28px] h-[48px]"
                  onClick={handleCloseListing}
                >
                  Close Listing
                </Button>
                <Button
                  variant="outline"
                  onClick={handleKeepListingOpen}
                  className="py-[14px] px-[28px] h-[48px]"
                  disabled={isKeepOpenLoading}
                >
                  {isKeepOpenLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Keep Listing Open'
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <ConfirmModal
        open={showRejectModal}
        onOpenChange={setShowRejectModal}
        title="Confirm Rejection"
        description="Are you sure you want to reject this applicant?"
        confirmText="Confirm Reject"
        loading={isRejectLoading}
        onConfirm={() =>
          handleStatusUpdate('Rejected', setIsRejectLoading, () =>
            setShowRejectModal(false)
          )
        }
        destructive
      />

      <ConfirmModal
        open={showShortlistModal}
        onOpenChange={setShowShortlistModal}
        title="Mark as Shortlisted"
        description="Are you sure you want to mark this applicant as shortlisted"
        confirmText="Mark as Shortlisted"
        loading={isShortlistLoading}
        onConfirm={() =>
          handleStatusUpdate('Shortlisted', setIsShortlistLoading, () =>
            setShowShortlistModal(false)
          )
        }
      />

      <ConfirmModal
        open={showContactedModal}
        onOpenChange={setShowContactedModal}
        title="Mark as Contacted"
        description="Are you sure you want to mark this applicant as contacted?"
        confirmText="Mark as Contacted"
        loading={isContactedLoading}
        onConfirm={() =>
          handleStatusUpdate('Contacted', setIsContactedLoading, () =>
            setShowContactedModal(false)
          )
        }
      />
    </>
  );
}
