/* eslint-disable @typescript-eslint/no-explicit-any */

import type { Partner } from '@/types';
import axiosClient from '@/utils/axiosClient';

const API_URL = 'users';

class UserService {
  async getPartnerProfile(userId: string) {
    const res = await axiosClient.get(`/partners`, {
      params: {
        userId,
      },
    });
    return res.data;
  }
  async updatePartnerProfile({
    partner,
    id,
  }: {
    partner: Partner;
    id: string;
  }) {
    const res = await axiosClient.put(`/partners/${id}`, partner);
    return res.data;
  }
  async createPartnerProfile({ partner }: { partner: Partner }) {
    const res = await axiosClient.post(`/partners`, partner);
    return res.data;
  }

  // async updateUserProfile(id: string) {
  //   const res = await axiosClient.put(`${API_URL}/${id}`);
  //   return res.data;
  // }

  async deleteUserProfile(id: string) {
    const res = await axiosClient.delete(`${API_URL}/${id}`);
    return res.data;
  }

  async updateUserProfile(id: string, data: any) {
    const res = await axiosClient.put(`${API_URL}/${id}`, data);
    return res.data;
  }
}

const userService = new UserService();
export default userService;
