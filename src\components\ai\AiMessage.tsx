interface MessageBubbleProps {
  text: string;
  isUser: boolean;
}

export const AiMessage = ({ text, isUser }: MessageBubbleProps) => {
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div
        className={`px-4 ${
          isUser
            ? 'bg-neutral-200 text-neutral-900 rounded-lg py-2 px-4'
            : ' rounded-bl-none'
        }`}
      >
        <p>{text}</p>
      </div>
    </div>
  );
};
