/* eslint-disable @typescript-eslint/no-non-null-assertion */
'use client';

import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { useEffect, useRef, useState } from 'react';
import { renderAsync } from 'docx-preview';

interface PDFModalProps {
  isOpen: boolean;
  onClose: () => void;
  blobUrl: string | null;
  fileType?: string;
}

export default function PDFModal({
  isOpen,
  onClose,
  blobUrl,
  fileType,
}: PDFModalProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isOpen || !blobUrl || !containerRef.current) return;

    const renderDocument = async () => {
      try {
        // Clear previous content
        containerRef.current!.innerHTML = '';
        setError(null);

        if (fileType?.includes('pdf')) {
          // Handle PDF files
          const iframe = document.createElement('iframe');
          iframe.src = blobUrl;
          iframe.style.width = '100%';
          iframe.style.height = '100%';
          iframe.style.border = 'none';
          containerRef.current!.appendChild(iframe);
        } else if (fileType?.includes('officedocument.wordprocessingml')) {
          // Handle DOCX files
          const response = await fetch(blobUrl);
          const blob = await response.blob();

          // Add some styling to make the document look better
          const styleElement = document.createElement('style');
          styleElement.textContent = `
            .docx-wrapper {
              background: white;
              padding: 20px;
              box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .docx {
              font-family: Arial, sans-serif;
            }
          `;
          document.head.appendChild(styleElement);

          await renderAsync(blob, containerRef.current!, undefined, {
            className: 'docx',
            inWrapper: true,
            ignoreWidth: false,
            ignoreHeight: false,
            ignoreFonts: false,
            breakPages: true,
            experimental: true,
          });
        } else {
          throw new Error('Unsupported file type');
        }
      } catch (error) {
        console.error('Error rendering document:', error);
        setError(
          'Failed to load document preview. Please download the file to view it.'
        );
      }
    };

    renderDocument();

    return () => {
      // Cleanup
      if (containerRef.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        containerRef.current.innerHTML = '';
      }
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [isOpen, blobUrl, fileType]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full max-w-4xl h-[90vh] p-0 overflow-hidden">
        <DialogTitle className="sr-only">Document Preview</DialogTitle>
        <div
          ref={containerRef}
          className="w-full h-full p-4 overflow-auto bg-white"
          style={{ minHeight: '80vh' }}
        >
          {error && <p className="text-red-500">{error}</p>}
          {!blobUrl && <p>Document not available</p>}
        </div>
      </DialogContent>
    </Dialog>
  );
}
