import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import { CourseForm } from '@/components/dashboard/components/courses/courseForm';
import React from 'react';

function Form() {
  return (
    <div>
      <div className="mb-4">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'Add a New Course',
              href: '/dashboard/courses/add-a-new-course',
            },
          ]}
        />
      </div>
      <h1 className="text-[28px] font-semibold leading-[36px] text-neutral-900 mb-6">
        Add a New Course
      </h1>
      <CourseForm />
    </div>
  );
}

export default Form;
