'use client';

import type React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface SuccessDialogProps {
  isOpen: boolean;
  onAddAnother: () => void;
  onReturnHome: () => void;
}

export const SuccessDialog: React.FC<SuccessDialogProps> = ({
  isOpen,
  onAddAnother,
  onReturnHome,
}) => {
  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 flex justify-center items-center bg-[#0000]/40" />
      )}

      <Dialog open={isOpen}>
        <DialogContent className="sm:max-w-md z-50">
          <div className="flex flex-col">
            <h2 className="text-[28px] font-semibold text-neutral-900 mt-4">
              🎉 Success!
            </h2>
            <DialogDescription className="mt-2">
              Your course has been submitted for approval.
            </DialogDescription>
            <DialogDescription className="mt-3">
              What would you like to do next?
            </DialogDescription>

            <div className="flex flex-col w-full gap-3 mt-6">
              <Button
                onClick={onAddAnother}
                className="w-full rounded-[--buttonStyle]"
              >
                Add Another Course
              </Button>
              <Button
                variant="outline"
                onClick={onReturnHome}
                className="w-full rounded-[--buttonStyle]"
              >
                Return Home
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
