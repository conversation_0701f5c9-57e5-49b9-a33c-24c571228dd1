/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Loader2, Search, X } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useCloseListing } from '@/hooks/useClosingListing';
import { useState, useEffect } from 'react';

interface CloseListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  jobId: string;
}

interface ConfirmCloseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  selectedApplicantIds: string[];
  jobId: string;
  hiringSource: string;
  allApplicants: any[];
  reset: () => void;
}

export function CloseListingModal({
  isOpen,
  onClose,
  jobId,
}: CloseListingModalProps) {
  const {
    state: {
      hiringSource,
      selectedApplicantIds,
      loading,
      searchTerm,
      confirmModalOpen,
      jobApplicants,
      hiredApplicants,
      eligibleApplicants,
      prefilledApplicantIds,
    },
    actions: {
      setSearchTerm,
      setConfirmModalOpen,
      handleAddAnother,
      handleSelectChange,
      handleRemoveApplicant,
      handleHiringSourceChange,
      handleConfirmClose,
      resetModal,
    },
  } = useCloseListing({ jobId, isOpen });

  const handleClose = () => {
    resetModal();
    onClose();
  };

  // Automatically select the first eligible applicant if none are selected
  useEffect(() => {
    if (
      hiringSource === 'On this platform' &&
      eligibleApplicants.length > 0 &&
      selectedApplicantIds.length === 0
    ) {
      handleSelectChange(eligibleApplicants[0].id, 0);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hiringSource, eligibleApplicants, selectedApplicantIds]);

  return (
    <>
      <Dialog open={isOpen && !confirmModalOpen} onOpenChange={handleClose}>
        <DialogContent className="w-[600px] p-[24px]">
          <DialogHeader>
            <DialogTitle className="text-[24px] font-semibold leading-[32px] text-neutral-900">
              Close Listing
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-[18px] font-normal leading-[24px] text-neutral-500">
              Where did you hire this applicant?
            </p>
            <RadioGroup
              value={hiringSource}
              onValueChange={handleHiringSourceChange}
              className="space-y-3"
            >
              {[
                'On this platform',
                'Outside of this platform',
                'Position was not filled',
              ].map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option}
                    id={`hiring-${option}`}
                    className="h-5 w-5 text-primary-500 border border-neutral-300 focus:ring-primary-500 focus:ring-offset-0 focus:border-2 focus:border-primary-500"
                  />
                  <label
                    htmlFor={`hiring-${option}`}
                    className="text-[16px] text-neutral-700 font-normal leading-[24px]"
                  >
                    {option}
                  </label>
                </div>
              ))}
            </RadioGroup>

            {hiringSource === 'On this platform' && (
              <div className="mt-4 space-y-4">
                {hiredApplicants.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-[18px] font-semibold text-neutral-700 leading-[28px]">
                      Hired Applicants
                    </p>
                    <ul className="list-disc pl-5 space-y-1">
                      {hiredApplicants.map((applicant) => (
                        <li
                          key={applicant.id}
                          className="text-[16px] text-neutral-700"
                        >
                          {applicant.user?.userName || 'Unknown Applicant'}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {selectedApplicantIds
                  .filter((id) => !prefilledApplicantIds.has(id))
                  .map((applicantId, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-[18px] font-semibold text-neutral-700 leading-[28px]">
                          {index === 0 && hiredApplicants.length === 0
                            ? 'Select Applicant'
                            : `Applicant ${index + 1}`}
                        </p>
                        {applicantId && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveApplicant(index)}
                            className="h-8 w-8 p-0 text-neutral-500 hover:text-neutral-700"
                          >
                            <X className="text-destructive-500" />
                          </Button>
                        )}
                      </div>
                      <Select
                        value={applicantId}
                        onValueChange={(value) =>
                          handleSelectChange(value, index)
                        }
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select applicant">
                            {jobApplicants?.find((a) => a.id === applicantId)
                              ?.user?.userName || 'Select applicant'}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent className="max-h-[400px]">
                          <div className="relative px-2 mb-2">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              placeholder="Search applicants..."
                              className="pl-8"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                          </div>
                          <ScrollArea className="h-[300px]">
                            {eligibleApplicants
                              .filter(
                                (app) =>
                                  !selectedApplicantIds.includes(app.id) ||
                                  app.id === applicantId
                              )
                              .map((applicant) => (
                                <SelectItem
                                  key={applicant.id}
                                  value={applicant.id}
                                >
                                  <div className="flex flex-col">
                                    <span className="font-medium text-[16px] text-neutral-900">
                                      {applicant.user?.userName ||
                                        'Unknown Applicant'}
                                    </span>
                                    <span className="text-[14px] text-neutral-500 font-normal">
                                      {applicant.status}
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                          </ScrollArea>
                        </SelectContent>
                      </Select>
                    </div>
                  ))}

                {eligibleApplicants.length > selectedApplicantIds.length && (
                  <button
                    onClick={handleAddAnother}
                    className="text-[16px] py-2 px-3 flex items-center border border-[--buttonColor] bg-tranparent text-[--buttonColor] rounded-[--buttonStyle] hover:bg-[--buttonColorLight]"
                  >
                    <Plus size={16} className="mr-1" />
                    Add Another Hired Applicant
                  </button>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleClose}
              className="text-[18px] h-[48px]  py-[14px] px-[28px] font-medium"
            >
              Cancel
            </Button>
            <Button
              onClick={() => setConfirmModalOpen(true)}
              className="text-[18px] flex items-center justify-center h-[48px] py-[14px] px-[28px] font-medium"
              disabled={
                !hiringSource ||
                (hiringSource === 'On this platform' &&
                  (selectedApplicantIds.length === 0 ||
                    selectedApplicantIds.some((id) => !id))) ||
                loading
              }
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader2 size={18} className="animate-spin" /> Close Listing
                </div>
              ) : (
                'Close Listing'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ConfirmCloseModal
        isOpen={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        onConfirm={handleConfirmClose}
        selectedApplicantIds={selectedApplicantIds}
        jobId={jobId}
        hiringSource={hiringSource}
        allApplicants={jobApplicants || []}
        reset={handleClose}
      />
    </>
  );
}

function ConfirmCloseModal({
  isOpen,
  onClose,
  onConfirm,
}: ConfirmCloseModalProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const handleConfirm = async () => {
    setLoading(true);
    try {
      await onConfirm();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[440px] p-[24px]">
        <DialogHeader>
          <DialogTitle className="text-[24px] font-semibold leading-[32px] text-neutral-900">
            Confirm Close
          </DialogTitle>
        </DialogHeader>
        <div className="px-1 pb-3">
          <p className="text-[18px] font-normal leading-[24px] text-neutral-500">
            Are you sure you want to close this listing?
          </p>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="text-[18px] h-[48px]  py-[14px] px-[28px] font-medium"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="text-[18px] flex items-center justify-center h-[48px] py-[14px] px-[28px] font-medium"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <Loader2 size={18} className="animate-spin" /> Confirm Close
                Listing
              </div>
            ) : (
              'Confirm Close Listing'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export const modals = {
  CloseListingModal,
  ConfirmCloseModal,
};
