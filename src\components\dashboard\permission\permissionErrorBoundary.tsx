/* eslint-disable no-unused-vars */
'use client';

import type React from 'react';
import { Component, type ReactNode } from 'react';
import { AccessDeniedPage } from './AccessDeniedPage';

interface PermissionErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface PermissionErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Error boundary specifically for permission-related errors
 */
export class PermissionErrorBoundary extends Component<
  PermissionErrorBoundaryProps,
  PermissionErrorBoundaryState
> {
  constructor(props: PermissionErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): PermissionErrorBoundaryState {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);

    // Log the error for debugging
    console.error(
      'Permission Error Boundary caught an error:',
      error,
      errorInfo
    );
  }

  render() {
    if (this.state.hasError) {
      // Check if it's a permission-related error
      const isPermissionError =
        this.state.error?.message.includes('permission') ||
        this.state.error?.message.includes('unauthorized') ||
        this.state.error?.message.includes('access denied');

      if (this.props.fallback) {
        return this.props.fallback;
      }

      if (isPermissionError) {
        return (
          <AccessDeniedPage
            title="Permission Error"
            description="An error occurred while checking your permissions. Please try refreshing the page or contact support."
            variant="card"
            showRefreshButton={true}
            customActions={
              <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <details className="text-sm">
                  <summary className="cursor-pointer font-medium">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs overflow-auto">
                    {this.state.error?.message}
                  </pre>
                </details>
              </div>
            }
          />
        );
      }

      // Generic error fallback
      return (
        <div className="min-h-[50vh] flex items-center justify-center p-4">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold text-gray-900">
              Something went wrong
            </h2>
            <p className="text-gray-600">
              An unexpected error occurred. Please try refreshing the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
