'use client';

import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import Declaration from '../../condtions/declaration';
import TermCondition from '../../condtions/term&condition';

const AgreementsCard: React.FC<{
  data: {
    termsAccepted: boolean;
    declarationAccepted: boolean;
  };
  updateData: (
    _data: Partial<{ termsAccepted: boolean; declarationAccepted: boolean }>
  ) => void;
}> = ({ data, updateData }) => {
  const [isTermsOpen, setIsTermsOpen] = React.useState(false);
  const [isDeclarationOpen, setIsDeclarationOpen] = React.useState(false);

  return (
    <div className="bg-white text-left pb-4 space-y-4 px-4">
      <label className="flex gap-2 items-center">
        <Checkbox
          checked={data.termsAccepted}
          onCheckedChange={(checked) =>
            updateData({ termsAccepted: Boolean(checked) })
          }
          className="rounded
                data-[state=checked]:bg-primary-500
                data-[state=checked]:border-primary-500
                data-[state=checked]:text-white"
          required
        />
        <Link
          href=""
          className="text-[16px] font-normal text-center text-neutral-700"
        >
          I have read and agreed to all the{' '}
          <span
            role="button"
            tabIndex={0}
            onClick={() => setIsTermsOpen(true)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setIsTermsOpen(true);
                e.preventDefault();
              }
            }}
            className="font-semibold text-[16px] ml-1 text-primary-400 hover:underline cursor-pointer"
            aria-pressed="false"
          >
            terms and conditions
          </span>
        </Link>
      </label>
      <label className="flex gap-2 items-center">
        <Checkbox
          checked={data.declarationAccepted}
          onCheckedChange={(checked) =>
            updateData({ declarationAccepted: Boolean(checked) })
          }
          className="rounded
                data-[state=checked]:bg-primary-500
                data-[state=checked]:border-primary-500
                data-[state=checked]:text-white"
          required
        />
        <p className="text-[16px] font-normal text-center text-neutral-700">
          I have read the joining{' '}
          <span
            role="button"
            tabIndex={0}
            onClick={() => setIsDeclarationOpen(true)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setIsDeclarationOpen(true);
                e.preventDefault();
              }
            }}
            className="font-semibold text-[16px] ml-1 text-primary-400 hover:underline cursor-pointer"
            aria-pressed="false"
          >
            declaration
          </span>
        </p>
      </label>

      <Dialog open={isTermsOpen} onOpenChange={setIsTermsOpen}>
        <DialogContent className="w-full max-w-4xl h-[700px] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Terms and Conditions</DialogTitle>
          </DialogHeader>
          <TermCondition />
        </DialogContent>
      </Dialog>

      <Dialog open={isDeclarationOpen} onOpenChange={setIsDeclarationOpen}>
        <DialogContent className="w-full max-w-4xl h-[700px] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Privacy Policy</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <Declaration />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgreementsCard;
