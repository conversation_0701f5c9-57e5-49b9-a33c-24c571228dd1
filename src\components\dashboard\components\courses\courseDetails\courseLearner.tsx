/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ChevronDown,
  ChevronUp,
  Search,
  ChevronsUpDown,
  Calendar as CalendarIcon,
} from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { getCourseEnrollments } from '@/zustand/services/courseServices';
import type { CourseEnrollment, EnrollmentFilters } from '@/types/courseType';
import { Pagination } from '../courseTable/pagination';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import ResetIcon from '@/components/icons/resetIcon';

interface CourseLearnersTableProps {
  courseId: string;
}

export default function CourseLearnersTable({
  courseId,
}: CourseLearnersTableProps) {
  // State for active tab
  const [activeTab, setActiveTab] = useState<
    'All Learners' | 'Enrolled' | 'Completed'
  >('All Learners');

  // State for filters
  const [searchTerm, setSearchTerm] = useState('');
  const [enrolledFrom, setEnrolledFrom] = useState<Date | undefined>(undefined);
  const [enrolledTo, setEnrolledTo] = useState<Date | undefined>(undefined);
  const [dateType, setDateType] = useState<'enrollmentDate' | 'completionDate'>(
    'enrollmentDate'
  );

  // State for sorting
  const [sortField, setSortField] = useState<string | null>(null);
  const [isAscending, setIsAscending] = useState<boolean | null>(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalLearners, setTotalLearners] = useState(0);
  const [pageSize] = useState(10);

  // State for learners data
  const [learners, setLearners] = useState<CourseEnrollment[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // State for enrollment counts
  const [counts, setCounts] = useState({
    all: 0,
    enrolled: 0,
    completed: 0,
  });

  // State for applied filters
  const [appliedFilters, setAppliedFilters] = useState<{
    search: string;
    enrolledFrom?: string;
    enrolledTo?: string;
    dateType: 'enrollmentDate' | 'completionDate';
  }>({
    search: '',
    dateType: 'enrollmentDate',
  });

  const fetchLearners = async () => {
    setIsLoading(true);

    try {
      // Map active tab to status filter
      let statusFilter: 'Enrolled' | 'Completed' | 'All' = 'All';
      if (activeTab === 'Enrolled') statusFilter = 'Enrolled';
      if (activeTab === 'Completed') statusFilter = 'Completed';

      // Build filters object
      const filters: EnrollmentFilters = {
        search: appliedFilters.search,
        enrolledFrom: appliedFilters.enrolledFrom,
        enrolledTo: appliedFilters.enrolledTo,
        // dateType: appliedFilters.dateType,
        status: statusFilter,
        sortField: sortField || undefined,
        ascending: isAscending === null ? undefined : isAscending,
      };

      // Fetch data from API
      const response = await getCourseEnrollments(
        courseId,
        filters,
        currentPage,
        pageSize
      );

      setLearners(response.data);
      setTotalLearners(response.total);

      setCounts({
        all: response.statusCounts.totalEnrolled,
        enrolled: response.statusCounts.notCompleted,
        completed: response.statusCounts.completed,
      });
    } catch (error) {
      console.error('Error fetching learners:', error);
      setLearners([]);
      setTotalLearners(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch learners based on filters and active tab
  useEffect(() => {
    fetchLearners();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    courseId,
    activeTab,
    appliedFilters,
    sortField,
    isAscending,
    currentPage,
    pageSize,
  ]);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      if (isAscending === true) {
        setIsAscending(false);
      } else if (isAscending === false) {
        setSortField(null);
        setIsAscending(null);
      } else {
        setIsAscending(true);
      }
    } else {
      setSortField(field);
      setIsAscending(true);
    }
  };

  // Render sort icon
  const renderSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ChevronsUpDown className="ml-2 h-4 w-4" />;
    }

    if (isAscending === true) {
      return <ChevronUp className="ml-2 h-4 w-4" />;
    }

    if (isAscending === false) {
      return <ChevronDown className="ml-2 h-4 w-4" />;
    }

    return <ChevronsUpDown className="ml-2 h-4 w-4" />;
  };

  // Handle search
  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleApplyFilters();
    }
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setSearchTerm('');
    setEnrolledFrom(undefined);
    setEnrolledTo(undefined);
    setDateType('enrollmentDate');
    setAppliedFilters({
      search: '',
      dateType: 'enrollmentDate',
    });
    setCurrentPage(1);
  };

  const handleApplyFilters = () => {
    // Convert dates to UTC at start of day (for from) and end of day (for to)
    const utcEnrolledFrom = enrolledFrom
      ? new Date(
          Date.UTC(
            enrolledFrom.getFullYear(),
            enrolledFrom.getMonth(),
            enrolledFrom.getDate(),
            0,
            0,
            0
          )
        ).toISOString()
      : undefined;

    const utcEnrolledTo = enrolledTo
      ? new Date(
          Date.UTC(
            enrolledTo.getFullYear(),
            enrolledTo.getMonth(),
            enrolledTo.getDate(),
            23,
            59,
            59
          )
        ).toISOString()
      : undefined;

    setAppliedFilters({
      search: searchTerm,
      enrolledFrom: utcEnrolledFrom,
      enrolledTo: utcEnrolledTo,
      dateType,
    });
    setCurrentPage(1);
  };

  // Update the formatDate function to use UTC
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      timeZone: 'UTC',
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  // Check if date range is valid
  const isDateRangeValid = () => {
    if (!enrolledFrom || !enrolledTo) return true;
    return enrolledFrom <= enrolledTo;
  };

  return (
    <div className="space-y-6 mt-8 border rounded-lg p-6 bg-white">
      <Tabs
        defaultValue="All Learners"
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as any)}
      >
        <TabsList className="mb-6 bg-transparent gap-10">
          <TabsTrigger
            value="All Learners"
            className={
              activeTab === 'All Learners'
                ? 'border-2 border-primary-500 text-primary-500 text-[20px]'
                : 'bg-white text-neutral-900 border-neutral-300'
            }
          >
            All Learners{' '}
            <span
              className={`ml-2 rounded-full w-6 h-6 inline-flex items-center justify-center text-[20px] p-2 ${
                activeTab === 'All Learners'
                  ? 'bg-primary-500 !text-white'
                  : 'bg-neutral-50 text-neutral-900'
              }`}
            >
              {counts.all}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="Enrolled"
            className={
              activeTab === 'Enrolled'
                ? 'border-2 border-primary-500 text-primary-500 text-[20px]'
                : 'bg-white text-neutral-900 border-neutral-300'
            }
          >
            Enrolled{' '}
            <span
              className={`ml-2 rounded-full w-6 h-6 inline-flex items-center justify-center text-[20px] p-2 ${
                activeTab === 'Enrolled'
                  ? 'bg-primary-500 !text-white'
                  : 'bg-neutral-50 text-neutral-900'
              }`}
            >
              {counts.enrolled}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="Completed"
            className={
              activeTab === 'Completed'
                ? 'border-2 border-primary-500 text-primary-500 text-[20px]'
                : 'bg-white text-neutral-900 border-neutral-300'
            }
          >
            Completed{' '}
            <span
              className={`ml-2 rounded-full w-6 h-6 inline-flex items-center justify-center text-[20px] p-2 ${
                activeTab === 'Completed'
                  ? 'bg-primary-500 !text-white'
                  : 'bg-neutral-50 text-neutral-900'
              }`}
            >
              {counts.completed}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="rounded-md">
            <div className="space-y-4">
              <div>
                <p className="text-[16px] font-medium mb-2">Filter:</p>
                <div className="relative flex-grow mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search keyword..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={handleSearch}
                    className="pl-10"
                  />
                </div>

                <p className=" text-[16px] font-semibold leading-7 text-neutral-900 mt-8">
                  {' '}
                  Enrolled Date:{' '}
                </p>
                <div className="flex flex-col md:flex-row gap-2 mb-4 max-w-2xl">
                  <div className="mb-4">
                    <Select
                      value={dateType}
                      onValueChange={(
                        value: 'enrollmentDate' | 'completionDate'
                      ) => setDateType(value)}
                    >
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Date Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enrollmentDate">Enrolled</SelectItem>
                        <SelectItem value="completionDate">
                          Completed
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-grow">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={`w-full justify-between text-left font-normal rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor] ${
                            !enrolledFrom && 'text-muted-foreground'
                          } ${!isDateRangeValid() ? 'border-destructive' : ''}`}
                        >
                          {enrolledFrom ? (
                            format(enrolledFrom, 'PPP')
                          ) : (
                            <span>From: DD/MM/YYYY</span>
                          )}
                          <CalendarIcon className="mr-2 h-4 w-4 !text-[--bodyTextColor]" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <div className="flex flex-col">
                          <Calendar
                            mode="single"
                            selected={enrolledFrom}
                            onSelect={setEnrolledFrom}
                            initialFocus
                          />
                          <Button
                            variant="ghost"
                            onClick={() => setEnrolledFrom(undefined)}
                            className="text-destructive hover:text-destructive-600"
                          >
                            Clear
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="flex-grow">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={`w-full text-left font-normal justify-between rounded-md border border-secondary-500 hover:bg-transparent !text-[--bodyTextColor] ${
                            !enrolledTo && 'text-muted-foreground'
                          } ${!isDateRangeValid() ? 'border-destructive' : ''}`}
                        >
                          {enrolledTo ? (
                            format(enrolledTo, 'PPP')
                          ) : (
                            <span>To: DD/MM/YYYY</span>
                          )}
                          <CalendarIcon className="mr-2 h-4 w-4 !text-[--bodyTextColor]" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <div className="flex flex-col">
                          <Calendar
                            mode="single"
                            selected={enrolledTo}
                            onSelect={setEnrolledTo}
                            initialFocus
                          />
                          <Button
                            variant="ghost"
                            onClick={() => setEnrolledTo(undefined)}
                            className="text-destructive-500 hover:text-destructive-600"
                          >
                            Clear
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handleResetFilters}
                    className="whitespace-nowrap"
                  >
                    <ResetIcon />
                    Reset
                  </Button>

                  <Button
                    onClick={handleApplyFilters}
                    disabled={!isDateRangeValid()}
                    className="whitespace-nowrap"
                  >
                    Apply
                  </Button>
                </div>
                {!isDateRangeValid() && (
                  <p className="text-red-500 text-[16px] mt-1">
                    Start date must be earlier than or equal to end date
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="border rounded-md overflow-hidden">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-neutral-900">Loading learners...</p>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <button
                          className="flex items-center focus:outline-none"
                          onClick={() => handleSort('userName')}
                        >
                          Name {renderSortIcon('userName')}
                        </button>
                      </TableHead>
                      <TableHead>
                        <button
                          className="flex items-center focus:outline-none"
                          onClick={() => handleSort('email')}
                        >
                          Email {renderSortIcon('email')}
                        </button>
                      </TableHead>
                      <TableHead>
                        <button
                          className="flex items-center focus:outline-none"
                          onClick={() => handleSort('enrollmentDate')}
                        >
                          Enrolled Date {renderSortIcon('enrollmentDate')}
                        </button>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {learners.length > 0 ? (
                      learners.map((learner) => (
                        <TableRow key={learner.id}>
                          <TableCell>{learner.userName}</TableCell>
                          <TableCell className="text--500">
                            {learner.email}
                          </TableCell>
                          <TableCell className="text-neutral-900">
                            {formatDate(learner.enrollmentDate)}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={3}
                          className="h-24 text-center text-neutral-900"
                        >
                          There are no records for{' '}
                          {activeTab === 'All Learners'
                            ? 'any'
                            : activeTab.toLowerCase()}{' '}
                          status.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>

                {learners.length > 0 && (
                  <div className="p-4 border-t">
                    <Pagination
                      currentPage={currentPage}
                      totalItems={totalLearners}
                      pageSize={pageSize}
                      onPageChange={setCurrentPage}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
