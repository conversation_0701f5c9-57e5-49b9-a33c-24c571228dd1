'use client';
import GraduationCap from '@/components/icons/graduation-cap';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { chartConfig } from '@/constants';
import { Pie, PieChart, Sector } from 'recharts';
import type { PieSectorDataItem } from 'recharts/types/polar/Pie';

export default function EducationLevelsPie() {
  const chartData = [
    { name: 'Bachelor', percentage: 68, fill: '#022437' },
    { name: 'Master', percentage: 16, fill: '#1D506B' },
    { name: 'Diploma', percentage: 9, fill: '#829EAD' },
    { name: 'Doctorate', percentage: 5, fill: '#B4C5CE' },
  ];

  const sortedChartData = [...chartData].sort(
    (a, b) => b.percentage - a.percentage
  );

  return (
    <div className="">
      <div className="flex items-center gap-2 mb-3">
        <GraduationCap />
        <h2 className="text-[16px] font-semibold text-black-100">
          Fields of Education
        </h2>
      </div>
      <div className="flex flex-col ">
        <div className="md:flex gap-6 transition-all w-full">
          <div className="flex pb-0 h-[220px] w-full md:mx-0 relative">
            <ChartContainer
              config={chartConfig}
              className="aspect-square max-h-[300px]"
            >
              <PieChart className="">
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />

                <Pie
                  data={sortedChartData}
                  dataKey="percentage"
                  nameKey="name"
                  innerRadius={57}
                  outerRadius={90}
                  strokeWidth={5}
                  activeIndex={0}
                  activeShape={({
                    outerRadius = 0,
                    ...props
                  }: PieSectorDataItem) => (
                    <Sector {...props} outerRadius={outerRadius + 0} />
                  )}
                ></Pie>
              </PieChart>
            </ChartContainer>
          </div>

          <div className="flex flex-col justify-start py-4">
            {sortedChartData.map((data) => (
              <div
                key={data.name}
                className="flex items-center gap-4 mb-2 py-2"
              >
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: data.fill }}
                />
                <span className="text-[16px] font-medium">{data.name}</span>
                <span className="text-[16px] font-medium">
                  {data.percentage}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
