/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Button } from '@/components/ui/customButton';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ImageUpload } from '@/components/dashboard/common/imageUpload';
import InputField from '@/components/common/InputField';

interface CompanyDetailsFormProps {
  values: {
    name: string;
    partnerId: string;
    logo: string;
    addressLine1: string;
    mailAddressLine1: string;
    city: string;
    state: string;
    country: string;
    postCode: string;
    industry: string;
    companySize: string;
  };
  errors: any;
  touched: any;
  handleChange: (e: any) => void;
  handleBlur: (e: any) => void;
  setFieldValue: (field: string, value: any) => void;
  isSubmitting: boolean;
  handleCancel: () => void;
  companySizeOptions: any[];
  industryOptions: any[];
  citiesOptions: any[];
  regionsOptions: any[];
  getCompanySize: { isLoading: boolean };
}

export function CompanyDetailsForm({
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  setFieldValue,
  isSubmitting,
  handleCancel,
  companySizeOptions,
  industryOptions,
  citiesOptions,
  regionsOptions,
  getCompanySize,
}: CompanyDetailsFormProps) {
  return (
    <div className="space-y-6">
      <div>
        <Label className="text-[18px] font-semibold leading-[28px] text-neutral-900">
          Company Logo
        </Label>
        <ImageUpload
          value={values.logo}
          onChange={(value) => setFieldValue('logo', value)}
          alt="Company logo"
        />

        {errors.logo && touched.logo && (
          <div className="text-destructive-500 text-sm mt-1">{errors.logo}</div>
        )}
      </div>

      <div>
        <InputField
          label="Company Name:"
          labelClass="text-[18px] font-semibold leading-[28px] text-neutral-900"
          name="name"
          error={errors?.name}
          type={'text'}
          placeholder="Company Name"
          value={values.name}
          handleChange={handleChange}
          handleBlur={handleBlur}
        />
      </div>

      <div>
        <InputField
          label="Company ID:"
          labelClass="text-[18px] font-semibold leading-[28px] text-neutral-900"
          name="partnerId"
          error={errors?.partnerId}
          type={'partnerId'}
          placeholder="Company ID"
          value={values.partnerId}
          handleChange={handleChange}
          handleBlur={handleBlur}
          disabled={true}
        />
      </div>

      <div>
        <InputField
          label="Address Line 1:"
          labelClass="text-[18px] font-semibold leading-[28px] text-neutral-900"
          name="addressLine1"
          error={errors?.addressLine1}
          type={'addressLine1'}
          placeholder="Address Line 1"
          value={values.addressLine1}
          handleChange={handleChange}
          handleBlur={handleBlur}
        />
      </div>

      <div>
        <InputField
          label="Address Line 2:"
          labelClass="text-[18px] font-semibold leading-[28px] text-neutral-900"
          name="mailAddressLine1"
          error={errors?.mailAddressLine1}
          type={'mailAddressLine1'}
          placeholder="Address Line 2"
          value={values.mailAddressLine1}
          handleChange={handleChange}
          handleBlur={handleBlur}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
            htmlFor="industry"
          >
            City:
          </Label>
          <Select
            value={values.city}
            onValueChange={(value) => setFieldValue('city', value)}
          >
            <SelectTrigger
              className={
                errors.city && touched.city ? 'border-destructive-500' : ''
              }
            >
              <SelectValue placeholder="Please select" />
            </SelectTrigger>
            <SelectContent>
              {citiesOptions.map((city: any) => (
                <SelectItem key={city.id} value={city.value}>
                  {city.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.industry && touched.industry && (
            <div className="text-destructive-500">{errors.industry}</div>
          )}
        </div>
        <div>
          <Label
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
            htmlFor="state"
          >
            State:
          </Label>
          <Select
            value={values.state}
            onValueChange={(value) => setFieldValue('state', value)}
          >
            <SelectTrigger
              id="state"
              className={
                errors.state && touched.state ? 'border-destructive-500' : ''
              }
            >
              <SelectValue placeholder="Select State" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="abudhabi">Abu Dhabi</SelectItem>
              <SelectItem value="dubai">Dubai</SelectItem>
              <SelectItem value="sharjah">Sharjah</SelectItem>
              <SelectItem value="ajman">Ajman</SelectItem>
              <SelectItem value="ummalquwain">Umm Al-Quwain</SelectItem>
              <SelectItem value="rasalkhaimah">Ras Al Khaimah</SelectItem>
              <SelectItem value="fujairah">Fujairah</SelectItem>
            </SelectContent>
          </Select>
          {errors.state && touched.state && (
            <div className="text-destructive-500">{errors.state}</div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label
            className="text-[18px] font-semibold leading-[28px] text-neutral-900"
            htmlFor="industry"
          >
            Country:
          </Label>
          <Select
            value={values.country}
            onValueChange={(value) => setFieldValue('country', value)}
          >
            <SelectTrigger
              className={
                errors.country && touched.country
                  ? 'border-destructive-500'
                  : ''
              }
            >
              <SelectValue placeholder="Please select" />
            </SelectTrigger>
            <SelectContent>
              {regionsOptions.map((country: any) => (
                <SelectItem key={country.id} value={country.value}>
                  {country.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.industry && touched.industry && (
            <div className="text-destructive-500">{errors.industry}</div>
          )}
        </div>
        <div>
          <InputField
            label="ZIP/Postal Code:"
            labelClass="text-[18px] font-semibold leading-[28px] text-neutral-900"
            name="postCode"
            error={errors?.postCode}
            type={'postCode'}
            placeholder="ZIP/Postal Code"
            value={values.postCode}
            handleChange={handleChange}
            handleBlur={handleBlur}
          />
        </div>
      </div>

      <div>
        <Label
          className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          htmlFor="industry"
        >
          Industry:
        </Label>
        <Select
          value={values.industry}
          onValueChange={(value) => setFieldValue('industry', value)}
        >
          <SelectTrigger
            className={
              errors.industry && touched.industry
                ? 'border-destructive-500'
                : ''
            }
          >
            <SelectValue placeholder="Please select" />
          </SelectTrigger>
          <SelectContent>
            {industryOptions.map((industry: any) => (
              <SelectItem key={industry.id} value={industry.value}>
                {industry.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.industry && touched.industry && (
          <div className="text-destructive-500">{errors.industry}</div>
        )}
      </div>

      <div>
        <Label
          className="text-[18px] font-semibold leading-[28px] text-neutral-900"
          htmlFor="companySize"
        >
          Company Size:
        </Label>
        <Select
          value={values.companySize}
          onValueChange={(value) => setFieldValue('companySize', value)}
        >
          <SelectTrigger
            className={
              errors.companySize && touched.companySize
                ? 'border-destructive-500'
                : ''
            }
          >
            <SelectValue
              placeholder={
                getCompanySize.isLoading ? 'Loading...' : 'Company size'
              }
              className="text-[18px]"
            />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {companySizeOptions.map((size: any) => (
                <SelectItem className="" key={size.id} value={size.label}>
                  {size.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        {errors.companySize && touched.companySize && (
          <div className="text-destructive-500">{errors.companySize}</div>
        )}
      </div>

      <div className="mt-8 flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={handleCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}
