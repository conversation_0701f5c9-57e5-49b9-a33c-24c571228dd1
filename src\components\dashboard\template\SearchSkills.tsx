'use client';

import React, { useState } from 'react';
import { Search, X, PlusIcon } from 'lucide-react';

const initialESCOList = [
  'JavaScript',
  'Python',
  'React',
  'Machine Learning',
  'Data Science',
  'Cybersecurity',
  'DevOps',
  'Cloud Computing',
  'AI',
  'Blockchain',
  'Cybersecurity Analysis',
  'Mobile Development',
  'Game Development',
];

const SearchSkills = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);

  // Filter ESCO skills based on search input
  const filteredSkills = initialESCOList.filter((skill) =>
    skill.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Add skill (either from ESCO list or custom)
  const addSkill = (skill: string) => {
    if (skill && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    }
    setSearchTerm(''); // Clear search field after adding
  };

  // Remove selected skill
  const removeSkill = (skill: string) => {
    setSelectedSkills(selectedSkills.filter((s) => s !== skill));
  };

  return (
    <div className="mt-6">
      {/* Selected Skills Display */}
      {selectedSkills.length > 0 && (
        <div className="mb-4 flex flex-wrap gap-2">
          {selectedSkills.map((skill) => (
            <span
              key={skill}
              className="bg-[#cccdd1] text-black px-4 py-2 rounded-md text-[12px] font-medium inline-flex items-center justify-between"
            >
              {skill}
              <X
                onClick={() => removeSkill(skill)}
                className="ml-2 cursor-pointer text-[#71757c]"
                size={16}
              />
            </span>
          ))}
        </div>
      )}

      <div className="flex gap-2 items-center relative w-full">
        <Search
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500"
          size={18}
        />

        <input
          type="text"
          placeholder="Search skill..."
          className="border px-10 py-2 w-[75%] rounded-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />

        <button
          onClick={() => addSkill(searchTerm)}
          className={`flex items-center justify-center gap-2 px-4 py-3 rounded-full ${
            searchTerm
              ? 'bg-primary-500 text-white'
              : 'bg-primary-500 text-white cursor-not-allowed'
          }`}
          disabled={!searchTerm}
        >
          <PlusIcon size={16} />
          <p className="text-[16px]">Add another skill</p>
        </button>
      </div>

      {/* Skills Suggestions */}
      {searchTerm && (
        <div className="border mt-2 bg-white shadow-md rounded-md max-h-40 overflow-y-auto">
          {filteredSkills.length > 0 &&
            filteredSkills.map((skill) => (
              <div
                key={skill}
                role="option"
                tabIndex={0}
                className="p-2 hover:bg-gray-200 cursor-pointer"
                onClick={() => addSkill(skill)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    addSkill(skill);
                  }
                }}
                onTouchStart={() => addSkill(skill)}
                aria-selected={false}
              >
                {skill}
              </div>
            ))}
        </div>
      )}
    </div>
  );
};

export default SearchSkills;
