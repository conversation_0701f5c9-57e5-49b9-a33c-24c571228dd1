import ArrowIcon from '@/components/icons/ArrowRight';
import { Card, CardTitle } from '@/components/ui/card';
import React from 'react';

interface ActionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick?: () => void;
}

export const ActionCard: React.FC<ActionCardProps> = ({
  icon,
  title,
  description,
  onClick,
}) => {
  return (
    <Card
      onClick={onClick}
      className="border bg-white rounded-lg shadow-md p-5 space-y-2 border-neutral-200 hover:border-primary-500"
    >
      <div className="w-[48px] h-[48px]">{icon}</div>
      <CardTitle className="text-neutral-900 font-semibold max-w-[70%] text-[20px] leading-[28px]">
        {title}
      </CardTitle>
      <div className=" flex items-center justify-between">
        <p className="text-[16px] font-normal text-neutral-500 space-y-1 w-[87%]">
          {description}
        </p>

        <div className=" items-end justify-end flex">
          <ArrowIcon />
        </div>
      </div>
    </Card>
  );
};
