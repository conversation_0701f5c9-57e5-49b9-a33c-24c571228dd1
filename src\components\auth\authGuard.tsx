'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Loader from '@/utils/loader';
import { useAuthStore } from '@/zustand/auth/useAuthStore';

const AuthGuard = ({ children }: { children: React.ReactNode }) => {
  const { token, role, isloading } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isloading) {
      if (!token || role !== 'partner') {
        router.push('/login');
      } else if (pathname === '/login' || pathname === '/signup') {
        router.push('/dashboard');
      }
    }
  }, [token, role, isloading, router, pathname]);

  if (isloading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-70 z-[1000]">
        <Loader />
      </div>
    );
  }

  return token && role === 'partner' ? <>{children}</> : null;
};

export default AuthGuard;
