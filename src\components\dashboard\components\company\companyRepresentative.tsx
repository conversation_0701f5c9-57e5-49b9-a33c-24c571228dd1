import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useGetRepresentative } from '@/queries';

interface ApiRepresentative {
  id: string;
  userId: string;
  isActive?: boolean;
  user: {
    fullName: string;
    email: string;
    emailConfirmed: boolean;
  };
  partnerId: string;
  partner: {
    name: string;
  };
  role: string;
  createdOn: string;
  modifiedOn: string;
}

// interface ApiResponse {
//   success: boolean;
//   message: string;
//   data: ApiRepresentative[];
// }

interface Representative {
  id: string;
  role: string;
  name: string;
  email: string;
  status: 'active' | 'inactive' | 'invite-sent';
}

// type CompanyRepresentativesProps = object;

export function CompanyRepresentatives() {
  const { data, isLoading, isError } = useGetRepresentative();
  const response = data || [];

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className="border border-neutral-200 rounded-lg p-4 flex items-start gap-4"
          >
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8 text-destructive-600">
        <p>Failed to load representatives. Please try again later.</p>
      </div>
    );
  }

  const representatives: Representative[] =
    response?.map((rep: ApiRepresentative) => ({
      id: rep.id,
      name: rep.user.fullName,
      email: rep.user.email,
      role: rep.role,
      status: rep.isActive ? 'active' : ('invite-sent' as const),
    })) || [];

  if (representatives.length === 0) {
    return (
      <div className="text-center py-8 text-neutral-500">
        No representatives found.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {representatives.map((rep) => (
        <RepresentativeCard key={rep.id} representative={rep} />
      ))}
    </div>
  );
}

function RepresentativeCard({
  representative,
}: {
  representative: Representative;
}) {
  const getInitials = (name: string) => {
    if (!name) return 'SF';
    if (name.includes('@')) return 'SF';

    const parts = name.split(' ');
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  const getBadgeStyles = () => {
    switch (representative.status) {
      case 'active':
        return {
          variant: 'outline' as const,
          className:
            'rounded-full px-2 py-0.5 text-xs flex items-center gap-1 border-primary-500 text-primary-600 h-[24px] py-[2px] px-[8px]',
          dotClass: 'w-1.5 h-1.5 rounded-full bg-primary-500',
          label: 'Active',
        };
      case 'inactive':
      default:
        return {
          variant: 'outline' as const,
          className:
            'rounded-full px-2 py-0.5 text-xs flex items-center gap-1 border-destructive-600 text-destructive-600 h-[24px] py-[2px] px-[8px]',
          dotClass: 'w-1.5 h-1.5 rounded-full bg-destructive-600',
          label: 'Inactive',
        };
    }
  };

  const badgeStyles = getBadgeStyles();

  const displayRole = representative.role.includes('Full_Admin_Access')
    ? 'Full Admin Access'
    : representative.role;

  return (
    <div className="border border-neutral-200 rounded-lg hover:shadow-sm transition-shadow">
      <div className="p-4 flex items-start gap-4">
        <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-primary-500 rounded-full !text-white font-medium mt-6">
          {getInitials(representative?.name)}
        </div>
        <div className="space-y-1 flex-1">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold text-neutral-700 leading-[24px] text-[16px]">
              {representative.name}
            </h3>
            <Badge
              variant={badgeStyles.variant}
              className={badgeStyles.className}
            >
              <div className={badgeStyles.dotClass} />
              {badgeStyles.label}
            </Badge>
          </div>

          <p className="text-sm text-neutral-500">{representative.email}</p>
          <p className="text-sm text-neutral-500">{displayRole}</p>
        </div>
      </div>
    </div>
  );
}
