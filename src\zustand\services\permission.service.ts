import axiosClient from '@/utils/axiosClient';
import type { Permission, PermissionsResponse } from '@/types/permission';

const permissionService = {
  async fetchUserPermissions(): Promise<Permission[]> {
    try {
      const response =
        await axiosClient.get<PermissionsResponse>(`/permissions`);

      if (response.data.success) {
        return response.data.data.permissions;
      }

      return [];
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      return [];
    }
  },

  async checkUserPermissions(
    userId: string,
    requiredPermissions: Permission[]
  ): Promise<boolean> {
    try {
      const response = await axiosClient.post<{ hasPermission: boolean }>(
        `/permissions/check`,
        {
          userId,
          permissions: requiredPermissions,
        }
      );

      return response.data.hasPermission;
    } catch (error) {
      console.error('Error checking user permissions:', error);
      return false;
    }
  },
};

export default permissionService;
