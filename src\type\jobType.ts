export interface Job {
  id?: string;
  createdById?: string;
  title: string;
  description: string;
  location: string;
  monthlyFrom: number;
  // monthlyTo: number;
  postedDate?: string;
  startDate?: string;
  expiryDate?: string;
  companyName: string;
  jobType: string;
  jobMode: string;
  industry: string;
  experienceLevel: string;
  listingType?: string;
  status?: string;
  jobPosition: string;
  lastModifiedById: string;
  statusCounts?: {
    Active: number;
    Closed: number;
    Draft: number;
    Archived: number;
  };
  [key: string]: string | number | boolean | undefined | object;
}

export interface StatusCounts {
  Active: number;
  Closed: number;
  Draft: number;
  Archived: number;
}

export interface JobState {
  id: string;
  userId: string | null;
  jobId: string;
  appliedDate: string;
  status: string;
  jobs: Job[];
  isLoading: boolean;
  currentJob: Job | null;
  page: number;
  createdById?: string | null;
  size: number;
  total: number;
  ascending: boolean;
  sortField: string;
  // allJobs: Job[]
  searchTerm?: string;
  IncludeStatusCounts: boolean;
  statusCounts: StatusCounts;
  createdOnFrom: string;
  createdOnTo: string;
}

export type UpdateJobPayload = {
  job: Job;
  status: string;
};

export type JobAction =
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' }
  | {
      type: 'SET_JOBS';
      payload: {
        jobs: Job[];
        page: number;
        size: number;
        total: number;
        sortField: string;
        sortOrder: 'asc' | 'desc';
        searchTerm?: string;
        cacheKey: string;
      };
    }
  | {
      type: 'SET_CACHED_PAGE';
      payload: {
        jobs: Job[];
        page: number;
        sortField: string;
        sortOrder: 'asc' | 'desc';
        searchTerm?: string;
      };
    }
  | { type: 'SET_ALL_JOBS'; payload: Job[] }
  | {
      type: 'SET_SORTED_JOBS';
      payload: {
        allJobs: Job[];
        sortField: string;
        sortOrder: 'asc' | 'desc';
      };
    }
  | {
      type: 'SET_PAGE_WITH_JOBS';
      payload: {
        page: number;
        jobs: Job[];
      };
    }
  | { type: 'SET_USER_JOBS'; payload: Job[] }
  | { type: 'SET_CURRENT_JOB'; payload: Job | null }
  | { type: 'ADD_JOB'; payload: Job }
  | { type: 'UPDATE_JOB'; payload: Job }
  | { type: 'UPDATE_JOB_STATUS'; payload: { id: string; status: string } }
  | { type: 'DELETE_JOB'; payload: string }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_SORT_FIELD'; payload: string }
  | { type: 'SET_SORT_ORDER'; payload: 'asc' | 'desc' }
  | { type: 'SET_SEARCH_TERM'; payload: string }
  | { type: 'CLEAR_CACHE' };

export interface JobFilters {
  search?: string;
  status?: string;
  listingType?: string;
  dateType?: 'createdOn' | 'closedDate';
  createdOnFrom?: string;
  createdOnTo?: string;
  closedDateFrom?: string;
  closedDateTo?: string;
  userId?: string;
  ascending?: boolean;
  sortField?: string;
}
