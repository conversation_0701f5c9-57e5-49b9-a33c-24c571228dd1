import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Image from 'next/image';
import React from 'react';

interface UpSkillCardProps {
  companyLogo: string;
  title: string;
  metaData?: string[];
  location: string;
}

export const UpSkillCard: React.FC<UpSkillCardProps> = ({
  companyLogo,
  title,
  metaData,
  location,
}) => {
  return (
    <Card className="border rounded-lg shadow-md bg-white">
      <CardHeader className="">
        <Image
          height={100}
          width={100}
          src={companyLogo}
          quality={50}
          className="rounded-t-lg"
          alt={'company logo'}
        />
      </CardHeader>
      <CardContent className="space-y-2 pt-3">
        <CardTitle className="font-semibold text-neutral-900 text-base">
          {title}
        </CardTitle>
        <div className="flex flex-row items-center gap-2 flex-wrap">
          {metaData?.length &&
            metaData.map((item) => (
              <div
                key={item}
                className="bg-neutral-100 rounded m w-fit px-2 py-1"
              >
                <p className="font-medium text-neutral-900 text-[16px]">
                  {item}
                </p>
              </div>
            ))}
        </div>
        <div className="text-[16px] font-normal text-neutral-500 space-y-1">
          <p>{location}</p>
          <p>Posted 3 days ago</p>
        </div>
      </CardContent>
      <CardFooter className="py-2">
        <Button className="px-4 py-3 mr-2 w-full font-medium text-[16px]">
          View and Apply
        </Button>
      </CardFooter>
    </Card>
  );
};
