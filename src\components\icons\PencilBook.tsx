const PencilBook: React.FC<{
  width?: number;
  height?: number;
  stroke?: string;
}> = ({ width = 32, height = 32, stroke = 'var(--primary-500)' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4541_35397)">
        <path
          d="M16 14.9623C14.2903 13.6983 11.728 11.4881 4.38172 11.4881V27.3143C11.728 27.3143 14.2903 29.5246 16 30.7886M16 14.9623V30.7886M16 14.9623C17.7097 13.6983 20.272 11.4881 27.6183 11.4881V27.3143C20.272 27.3143 17.7097 29.5246 16 30.7886M20.5394 1.31435V6.87321M24.6011 1.19092H9.33943C8.91304 1.1904 8.49501 1.30916 8.13258 1.53378L4.11658 4.03435L8.13258 6.53035C8.49501 6.75496 8.91304 6.87372 9.33943 6.87321H24.6011C24.9809 6.88398 25.3589 6.81849 25.7129 6.68061C26.0669 6.54274 26.3896 6.33527 26.662 6.07049C26.9344 5.8057 27.151 5.48898 27.2988 5.13905C27.4467 4.78912 27.5229 4.41309 27.5229 4.03321C27.5229 3.65332 27.4467 3.27729 27.2988 2.92736C27.151 2.57743 26.9344 2.26071 26.662 1.99592C26.3896 1.73114 26.0669 1.52367 25.7129 1.3858C25.3589 1.24792 24.9809 1.18015 24.6011 1.19092Z"
          stroke={stroke}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4541_35397">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PencilBook;
