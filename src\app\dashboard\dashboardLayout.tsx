'use client';

import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { ActionsAlert } from '@/components/dashboard/components/ActionsAlert';
import DashboardFooter from '@/components/dashboard/components/DashboardFooter';
import TopBar from '@/components/dashboard/components/TopBar';
import useSettingsStore from '@/zustand/store/settingsStore';
import { Loader } from '@/components/dashboard/common/loader/Loader';
import TopNavigation from '@/components/dashboard/components/TopNavigation';
import { CareerAssistant } from '@/components/ai/AssistantDrawer';

const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const [showActionsAlert, setShowActionsAlert] = useState(false);
  const { appearanceSettings, getSettings } = useSettingsStore();
  const [loading, setLoading] = useState(false);
  const mainRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();

  const toggleActionsAlert = () => {
    setShowActionsAlert((prev) => !prev);
  };

  const {
    isBgStyleColor,
    bgStyleColor,
    bgStyleImg,
    overlayColor,
    overlayPercentage,
  } = appearanceSettings;

  const bgStyle = isBgStyleColor
    ? {
        backgroundColor: bgStyleColor,
      }
    : {
        backgroundImage: `url(${bgStyleImg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      };

  useEffect(() => {
    if (mainRef.current) {
      mainRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [pathname]);

  const getData = async () => {
    setLoading(true);
    await getSettings();
    setLoading(false);
  };

  useEffect(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {loading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
          <Loader />
        </div>
      )}
      <div className="flex flex-col h-screen overflow-hidden">
        <TopBar toggleActionsAlert={toggleActionsAlert} />

        <div>
          <TopNavigation />
        </div>

        <div className="flex flex-1 overflow-hidden">
          <div className="flex flex-1 overflow-hidden relative" style={bgStyle}>
            {!isBgStyleColor && (
              <div
                className="fixed top-0 left-0 w-full h-full z-0"
                style={{
                  backgroundColor: overlayColor,
                  opacity: Number(overlayPercentage) / 100,
                }}
              />
            )}

            <div className="flex flex-col flex-1 overflow-hidden relative">
              <main
                ref={mainRef}
                className="flex-1 relative overflow-y-auto scrollbar-hide"
              >
                {showActionsAlert && <ActionsAlert />}
                <section className="py-8 min-h-full max-w-[1200px] mx-auto">
                  {children}
                </section>
              </main>
            </div>
          </div>

          <div className="bg-transparent overflow-hidden relative z-10 ">
            <CareerAssistant />
          </div>
        </div>
        <DashboardFooter />
      </div>
    </>
  );
};

export default DashboardLayout;
