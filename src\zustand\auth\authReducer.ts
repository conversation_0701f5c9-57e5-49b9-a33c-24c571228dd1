// authReducer.ts
import type { IUser } from '@/type';

export interface AuthState {
  user: IUser | null;
  token: string | null;
  isloading: boolean;
  showLoader: boolean;
}

export type AuthAction =
  | { type: 'START_LOADING' }
  | { type: 'STOP_LOADING' }
  | { type: 'SHOW_LOADER' }
  | { type: 'HIDE_LOADER' }
  | { type: 'SET_AUTH'; payload: { user: IUser; token: string } }
  | { type: 'LOGOUT' };

export const authReducer = (
  state: AuthState,
  action: AuthAction
): AuthState => {
  switch (action.type) {
    case 'START_LOADING':
      return { ...state, isloading: true };
    case 'STOP_LOADING':
      return { ...state, isloading: false };
    case 'SHOW_LOADER':
      return { ...state, showLoader: true };
    case 'HIDE_LOADER':
      return { ...state, showLoader: false };
    case 'SET_AUTH':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isloading: false,
        showLoader: false,
      };
    case 'LOGOUT':
      return { user: null, token: null, isloading: false, showLoader: false };
    default:
      return state;
  }
};
