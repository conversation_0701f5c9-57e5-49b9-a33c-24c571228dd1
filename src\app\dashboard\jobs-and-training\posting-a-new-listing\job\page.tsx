import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import PostingJob from '@/components/dashboard/components/posting-a-new-job/posting-job';
import React from 'react';

function Job() {
  return (
    <div className="px-10  pb-40">
      <DynamicBreadcrumb
        customBreadcrumbs={[
          {
            label: 'Post a New Listing',
            href: '/dashboard/jobs-and-training/posting-a-new-listing',
          },
          {
            label: 'Job',
            href: '/dashboard/jobs-and-training/posting-a-new-listing/job',
          },
        ]}
      />

      <PostingJob />
    </div>
  );
}

export default Job;
