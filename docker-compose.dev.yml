version: '3.8'

services:
  career-navigator-ui-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: career-navigator-pro-partner-ui-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-https://career-navigator-pro-api-dot-careernavigator-430608.el.r.appspot.com/api/}
      - NEXT_PUBLIC_PARTNER_BASE_URL=${NEXT_PUBLIC_PARTNER_BASE_URL:-https://career-navigator-pro-ui-dot-careernavigator-430608.el.r.appspot.com}
      - WATCHPACK_POLLING=true
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    networks:
      - career-navigator-network

networks:
  career-navigator-network:
    driver: bridge
