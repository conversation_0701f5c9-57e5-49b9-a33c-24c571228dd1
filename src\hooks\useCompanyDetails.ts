'use client';

import { useParams } from 'next/navigation';
import { usePartnerProfile } from '@/queries';
import { useEffect } from 'react';
import { useJobStore } from '@/zustand/store/jobStore';

export function useCompanyDetails() {
  const { id } = useParams();
  const { setUserId } = useJobStore();

  useEffect(() => {
    setUserId(id as string);
  }, [id, setUserId]);

  const { data: partnerData, isLoading: isLoadingCompany } =
    usePartnerProfile();

  const company = partnerData?.data?.items[0] ?? [];

  const companyDetails = {
    id: company?.partner?.id ?? '',
    name: company?.partner?.name ?? '',
    partnerId: company?.partner?.partnerId ?? '',
    logo: company?.partner?.logo ?? '',
    companyId: company?.partner?.partnerId ?? '',
    registrationDate: company?.partner?.createdOn
      ? new Date(company.partner.createdOn).toLocaleDateString()
      : 'N/A',
    companySize: company?.partner?.size ?? '',
    companyAddress: company?.partner?.addressLine1 ?? '',
    industry: company?.partner?.industry ?? '',
    postCode: company?.partner?.postCode ?? '',
    email: company?.partner?.email ?? '',
    location: company?.partner?.country ?? '',
  };

  const representatives = [
    { id: 1, role: 'Representative 1', name: '', email: '' },
    { id: 2, role: 'Representative 2', name: '', email: '' },
    { id: 3, role: 'Representative 3', name: '', email: '' },
    { id: 4, role: 'Representative 4', name: '', email: '' },
  ];

  const hiringInformation = {
    hiringStatus: company?.hiringStatus ?? 'Not Hiring',
    activeJobPosts: company?.activeJobCount,
    hiredOnPlatfom: '',
  };

  const benefits = {
    monthlyBenefits: '',
    lastPaymentDate: '',
    TotalBenefitsReceived: '',
    CompanyEligibility: '',
  };

  return {
    companyDetails,
    representatives,
    hiringInformation,
    benefits,
    isLoadingCompany,
  };
}
