'use client';

import type React from 'react';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PlusIcon, XIcon } from 'lucide-react';
import type { Course } from '@/types/courseType';
import { useAdminValues } from '@/queries';
import { AdminValuesCategories } from '@/constants';

interface Topic {
  id: string;
  value: string;
  label: string;
}

interface CourseSummaryTabProps {
  course: Course;
  updateCourse: <K extends keyof Course>(_field: K, _value: Course[K]) => void;
}

export const CourseSummaryTab: React.FC<CourseSummaryTabProps> = ({
  course,
  updateCourse,
}) => {
  const [descriptionChars, setDescriptionChars] = useState(
    800 - (course.description?.length || 0)
  );
  const [objectiveChars, setObjectiveChars] = useState(
    800 - (course.learningObjective?.length || 0)
  );

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { value } = e.target;
    if (value.length <= 800) {
      updateCourse('description', value);
      setDescriptionChars(800 - value.length);
    }
  };

  const handleObjectiveChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    if (value.length <= 800) {
      updateCourse('learningObjective', value);
      setObjectiveChars(800 - value.length);
    }
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (value.split(/\s+/).length <= 50) {
      updateCourse('name', value);
    }
  };

  const addProgramOutline = () => {
    const newOutlines = [...course.programOutlines];
    if (newOutlines.length >= 30) {
      alert('Maximum of 30 program outline sections allowed');
      return;
    }

    const sectionNumber = newOutlines.length + 1;
    newOutlines.push({ key: `Section ${sectionNumber}`, value: '' });
    updateCourse('programOutlines', newOutlines);
  };

  const updateProgramOutline = (
    index: number,
    field: 'key' | 'value',
    value: string
  ) => {
    const newOutlines = [...course.programOutlines];
    newOutlines[index] = { ...newOutlines[index], [field]: value };
    updateCourse('programOutlines', newOutlines);
  };

  const removeProgramOutline = (index: number) => {
    const newOutlines = [...course.programOutlines];
    newOutlines.splice(index, 1);
    updateCourse('programOutlines', newOutlines);
  };

  const trainingTopics = useAdminValues({
    category: AdminValuesCategories?.trainingTopics.category,
  });

  const mainTopics: Topic[] =
    trainingTopics.data?.data?.data?.customValues || [];

  return (
    <div className="space-y-6">
      <div>
        <label htmlFor="name" className="block text-[16px] font-medium mb-1">
          Course Name: <span className="text-destructive-500">*</span>
        </label>
        <Input
          id="name"
          value={course.name}
          onChange={handleNameChange}
          placeholder="Enter the course title"
          required
        />
      </div>

      <div>
        <label
          htmlFor="mainTopic"
          className="block text-[16px] font-medium mb-1"
        >
          Main Topic: <span className="text-destructive-500">*</span>
        </label>
        <Select
          value={course.mainTopic}
          onValueChange={(value) => updateCourse('mainTopic', value)}
          required
        >
          <SelectTrigger>
            <SelectValue placeholder="Please select" />
          </SelectTrigger>
          <SelectContent>
            {mainTopics.map((topic: Topic) => (
              <SelectItem key={topic.id} value={topic?.value}>
                {topic?.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <label
          htmlFor="description"
          className="block text-[16px] font-medium mb-1"
        >
          Course Description: <span className="text-destructive-500">*</span>
        </label>
        <Textarea
          id="description"
          value={course.description}
          onChange={handleDescriptionChange}
          placeholder="Provide a brief overview of what this course covers"
          className="min-h-[120px]"
          required
        />
        <p className="text-[16px] text-neutral-500 mt-1">
          {descriptionChars} characters remaining
        </p>
      </div>

      <div>
        <label
          htmlFor="learningObjective"
          className="block text-[16px] font-medium mb-1"
        >
          Learning Objectives: <span className="text-destructive-500">*</span>
        </label>
        <Textarea
          id="learningObjective"
          value={course.learningObjective}
          onChange={handleObjectiveChange}
          placeholder="Describe the key skills or knowledge participants will gain"
          className="min-h-[120px]"
          required
        />
        <p className="text-[16px] text-neutral-500 mt-1">
          {objectiveChars} characters remaining
        </p>
      </div>

      <div>
        <div className="flex justify-between items-center mb-2">
          <label
            htmlFor="programOutline"
            className="block text-[16px] font-medium"
          >
            Program Outline: <span className="text-destructive-500">*</span>
          </label>
        </div>

        {course.programOutlines.map((outline, index) => (
          <div
            key={outline.key}
            className="space-y-2 mb-4 p-4 border rounded-md"
          >
            <div className="flex justify-between items-center">
              <label className="block text-[16px] font-medium">
                {`Section ${index + 1} Title:`}{' '}
                <span className="text-destructive-500">*</span>
              </label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeProgramOutline(index)}
                className="h-8 w-8 p-0"
                disabled={course.programOutlines.length <= 1}
              >
                <XIcon className="h-4 w-4" />
                <span className="sr-only">Remove</span>
              </Button>
            </div>
            <Input
              value={outline.key}
              onChange={(e) =>
                updateProgramOutline(index, 'key', e.target.value)
              }
              placeholder="Enter section or module name"
              required
            />

            <div>
              <label className="block text-[16px] font-medium mb-1">
                {`Section ${index + 1} Description:`}{' '}
                <span className="text-destructive-500">*</span>
              </label>
              <Textarea
                value={outline.value}
                onChange={(e) =>
                  updateProgramOutline(index, 'value', e.target.value)
                }
                placeholder="Describe what this section or module is about"
                className="min-h-[80px]"
                required
              />
              <p className="text-[16px] text-neutral-500 mt-1">
                {400 - outline.value.length} characters remaining
              </p>
            </div>
          </div>
        ))}

        <Button
          variant="outline"
          size="sm"
          onClick={addProgramOutline}
          className="mt-2 !rounded-[--buttonStyle]"
          disabled={course.programOutlines.length >= 30}
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add another program outline section
        </Button>
      </div>
    </div>
  );
};
