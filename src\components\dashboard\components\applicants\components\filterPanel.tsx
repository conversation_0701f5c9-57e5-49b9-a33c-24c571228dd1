/* eslint-disable @typescript-eslint/no-explicit-any */

'use client';

import { useState, useEffect } from 'react';
import { Search, Calendar as CalendarIcon, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatDateDDMonthYYYY } from '@/utils';
import ResetIcon from '@/components/icons/resetIcon';
import { useAuthStore } from '@/zustand/auth/useAuthStore';
import { useApplicantListing } from '@/hooks/useApplicantListing';

interface FilterPanelProps {
  search: string;
  filters: {
    name: string;
    position: string;
    appliedOnFrom: string;
    appliedOnTo: string;
  };
  onSearchChange: (_value: string) => void;
  onApplyFilters: (_filters: any) => void;
  onResetFilters: () => void;
  hideJobTitleDropdown?: boolean;
}

export default function FilterPanel({
  search,
  filters,
  onSearchChange,
  onApplyFilters,
  onResetFilters,
  hideJobTitleDropdown = false,
}: FilterPanelProps) {
  const [localSearch, setLocalSearch] = useState(search);
  const [localFilters, setLocalFilters] = useState(filters);
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);
  // const [isResetDisabled, setIsResetDisabled] = useState(true);
  const [showAppliedFilters, setShowAppliedFilters] = useState(false);

  const { user } = useAuthStore();
  const {
    state: { jobTitles },
  } = useApplicantListing(user?.id || null);

  const isDateRangeValid = () => {
    if (!localFilters.appliedOnFrom || !localFilters.appliedOnTo) return true;
    return (
      new Date(localFilters.appliedOnFrom) <= new Date(localFilters.appliedOnTo)
    );
  };

  // useEffect(() => {
  //   const hasFilters = Object.values(localFilters).some(
  //     (value) => value !== undefined && value !== ''
  //   );
  //   setIsResetDisabled(!hasFilters && !localSearch);
  // }, [localFilters, localSearch]);

  useEffect(() => {
    const newAppliedFilters: string[] = [];

    if (filters.position) {
      newAppliedFilters.push(filters.position);
    }
    if (filters.appliedOnFrom || filters.appliedOnTo) {
      const dateValue = [];
      if (filters.appliedOnFrom) {
        dateValue.push(`From: ${formatDateDDMonthYYYY(filters.appliedOnFrom)}`);
      }
      if (filters.appliedOnTo) {
        dateValue.push(`To: ${formatDateDDMonthYYYY(filters.appliedOnTo)}`);
      }
      if (dateValue.length > 0) {
        newAppliedFilters.push(dateValue.join(' '));
      }
    }

    setAppliedFilters(newAppliedFilters);
    setShowAppliedFilters(newAppliedFilters.length > 0);
  }, [filters]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearch(e.target.value);
  };

  const handlePositionChange = (value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      position: value,
    }));
  };

  const handleDateChange = (
    field: 'appliedOnFrom' | 'appliedOnTo',
    date: Date | undefined
  ) => {
    if (!date) {
      setLocalFilters((prev) => ({
        ...prev,
        [field]: '',
      }));
      return;
    }

    // Format date with proper time component
    const formattedDate =
      field === 'appliedOnFrom'
        ? new Date(
            Date.UTC(
              date.getFullYear(),
              date.getMonth(),
              date.getDate(),
              0,
              0,
              0
            )
          ).toISOString()
        : new Date(
            Date.UTC(
              date.getFullYear(),
              date.getMonth(),
              date.getDate(),
              23,
              59,
              59
            )
          ).toISOString();

    setLocalFilters((prev) => ({
      ...prev,
      [field]: formattedDate,
    }));
  };

  const removeFilter = (index: number) => {
    const updatedFilters = { ...filters };

    if (index === 0 && filters.name) {
      updatedFilters.name = '';
      setLocalSearch('');
    } else if (
      (index === 0 && !filters.name && filters.position) ||
      (index === 1 && filters.position)
    ) {
      updatedFilters.position = '';
    } else {
      updatedFilters.appliedOnFrom = '';
      updatedFilters.appliedOnTo = '';
    }

    setLocalFilters(updatedFilters);

    const hasRemainingFilters = Object.values(updatedFilters).some(
      (value) => value !== undefined && value !== ''
    );

    if (!hasRemainingFilters && !localSearch) {
      onResetFilters();
    } else {
      onApplyFilters({
        ...updatedFilters,
        name: updatedFilters.name === '' ? '' : localSearch,
      });
    }
  };

  const handleApply = () => {
    const filtersPayload = {
      ...localFilters,
      name: localSearch,
    };

    onSearchChange(localSearch);
    onApplyFilters(filtersPayload);
  };

  const handleReset = () => {
    setLocalSearch('');
    setLocalFilters({
      name: '',
      position: '',
      appliedOnFrom: '',
      appliedOnTo: '',
    });
    onSearchChange('');
    onResetFilters();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleApply();
    }
  };

  // Helper function to format date for display
  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return formatDateDDMonthYYYY(date.toISOString().split('T')[0]);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <h3 className="font-medium">Filter:</h3>

        {/* Search */}
        <div className="flex items-center gap-4">
          <div className="flex items-center w-full border border-neutral-200 px-2 rounded-md gap-2">
            <Search className="text-neutral-400" />
            <Input
              placeholder="Search Keyword..."
              value={localSearch}
              onChange={handleSearchChange}
              className="border-none focus:border-none ring-0 outline-none"
              onKeyDown={handleKeyDown}
            />
          </div>
        </div>

        <div className="space-y-4">
          {/* Position Select - Conditionally rendered */}
          {!hideJobTitleDropdown && (
            <div className="relative w-[248px]">
              <Select
                value={localFilters.position}
                onValueChange={handlePositionChange}
              >
                <SelectTrigger
                  className={`w-full ${localFilters.position ? 'border-secondary-500 border-2' : ''}`}
                >
                  <SelectValue placeholder="Job Title" />
                </SelectTrigger>
                <SelectContent>
                  {jobTitles.map((position) => (
                    <SelectItem key={position} value={position}>
                      {position}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Date Range */}
          <div className="space-y-2 w-[35%] gap-2">
            <p className="text-[16px] font-medium">Applied Date:</p>
            <div className="grid grid-cols-2 w-[32rem] gap-2">
              {/* From */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`justify-between text-left font-normal rounded-md w-[248px] border border-neutral-200 hover:bg-transparent !text-[--bodyTextColor] ${
                      !localFilters.appliedOnFrom ? 'text-muted-foreground' : ''
                    } ${localFilters.appliedOnFrom ? 'border-secondary-500' : ''}`}
                  >
                    {localFilters.appliedOnFrom
                      ? formatDisplayDate(localFilters.appliedOnFrom)
                      : 'From: DD/MM/YYYY'}
                    <CalendarIcon className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <div className="flex flex-col">
                    <Calendar
                      mode="single"
                      selected={
                        localFilters.appliedOnFrom
                          ? new Date(localFilters.appliedOnFrom)
                          : undefined
                      }
                      onSelect={(date) =>
                        handleDateChange('appliedOnFrom', date || undefined)
                      }
                      initialFocus
                    />
                    <Button
                      variant="ghost"
                      onClick={() =>
                        handleDateChange('appliedOnFrom', undefined)
                      }
                      className="text-destructive-500 hover:text-destructive-600"
                    >
                      Clear
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              {/* To */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`justify-between text-left font-normal rounded-md w-[248px] border border-neutral-200 hover:bg-transparent !text-[--bodyTextColor] ${
                      !localFilters.appliedOnTo ? 'text-muted-foreground' : ''
                    } ${localFilters.appliedOnTo ? 'border-secondary-500' : ''}`}
                  >
                    {localFilters.appliedOnTo
                      ? formatDisplayDate(localFilters.appliedOnTo)
                      : 'To: DD/MM/YYYY'}
                    <CalendarIcon className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <div className="flex flex-col">
                    <Calendar
                      mode="single"
                      selected={
                        localFilters.appliedOnTo
                          ? new Date(localFilters.appliedOnTo)
                          : undefined
                      }
                      onSelect={(date) =>
                        handleDateChange('appliedOnTo', date || undefined)
                      }
                      initialFocus
                    />
                    <Button
                      variant="ghost"
                      onClick={() => handleDateChange('appliedOnTo', undefined)}
                      className="text-destructive-500 hover:text-destructive-600"
                    >
                      Clear
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            {!isDateRangeValid() && (
              <p className="text-destructive text-[14px] mt-1">
                Start date must be earlier than or equal to end date
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 items-center">
            <Button variant="outline" onClick={handleReset}>
              <ResetIcon className="text-[--buttonColor]" />
              Reset
            </Button>
            <Button
              className="px-4"
              onClick={handleApply}
              disabled={!isDateRangeValid()}
            >
              Apply
            </Button>
          </div>
        </div>
      </div>

      {/* Applied Filters */}
      {showAppliedFilters && appliedFilters.length > 0 && (
        <div className="mt-4">
          <p className="text-[16px] font-medium mb-2">Filters Applied:</p>
          <div className="flex flex-wrap gap-2">
            {appliedFilters.map((filter, index) => (
              <div
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                className="flex items-center bg-neutral-100 rounded-md px-3 py-1 text-[16px]"
              >
                <span>{filter}</span>
                <button
                  className="ml-2 text-neutral-500 hover:text-neutral-700"
                  onClick={() => removeFilter(index)}
                >
                  <X className="h-3 w-3 text-[--bodyTextColor]" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
