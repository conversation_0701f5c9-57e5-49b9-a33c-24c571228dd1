import React from 'react';
import { Skeleton } from '../../ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../ui/table';

function ListingTableSkeleton() {
  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-[300px]" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>

      <Table>
        <TableHeader className="bg-neutral-50 rounded border-0">
          <TableRow>
            <TableHead className="text-neutral-700 text-[16px] leading-[24px] font-semibold">
              Position
            </TableHead>
            <TableHead className="text-neutral-700 text-[16px] leading-[24px] font-semibold">
              Listing Type
            </TableHead>
            <TableHead className="text-neutral-700 text-[16px] leading-[24px] font-semibold">
              Applicants
            </TableHead>
            <TableHead className="text-neutral-700 text-[16px] leading-[24px] font-semibold">
              Created On
            </TableHead>
            <TableHead className="text-neutral-700 text-[16px] leading-[24px] font-semibold">
              Close Date
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array(4)
            .fill(0)
            .map((_, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-6 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-28" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </div>
  );
}

export default ListingTableSkeleton;
