'use client';

import { Button } from '@/components/ui/customButton';

import { ChevronLeft } from 'lucide-react';
import { useCompanyDetails } from '@/hooks/useCompanyDetails';
import { useRouter } from 'next/navigation';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import { CompanyProfile } from '@/components/dashboard/components/company/companyProfile';

export default function CommunityListings() {
  const router = useRouter();
  const {
    companyDetails,
    representatives,
    hiringInformation,
    benefits,
    isLoadingCompany,
  } = useCompanyDetails();

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-4">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            { label: 'Companies', href: '/dashboard/company' },
            {
              label: companyDetails?.name || 'N/A',
              href: `/dashboard/company/${companyDetails?.id || ''}`,
            },
          ]}
        />
      </div>

      <div className="mx-auto">
        <Button
          variant="outline"
          className="mb-6 gap-2 py-6 px-7"
          onClick={() => router.back()}
        >
          <ChevronLeft className="h-4 w-4" />
          Back
        </Button>

        <CompanyProfile
          companyDetails={companyDetails}
          representatives={representatives}
          hiringInformation={hiringInformation}
          benefits={benefits}
          isLoading={isLoadingCompany}
        />

        <p className="pt-8 font-normal text-[20px] leading-[16px]">
          Need assistance?{' '}
          <span className="font-semibold text-[20px] leading-[16px] text-primary-500 ml-1">
            Contact Support
          </span>
        </p>
      </div>
    </div>
  );
}
