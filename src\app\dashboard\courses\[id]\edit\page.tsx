'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getCourseById } from '@/zustand/services/courseServices';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import DynamicBreadcrumb from '@/components/dashboard/common/DynamicBreadcrumb';
import type { Course } from '@/types/courseType';
import { EditCourseForm } from '@/components/dashboard/components/courses/edit/editCourseForm';
import { UnsavedChangesDialog } from '@/components/dashboard/components/courses/unsavedChanges';
import { useCourseFormStore } from '@/zustand/store/coursesStore';
import { Loader } from '@/components/dashboard/common/Loader';

export default function EditCoursePage() {
  const router = useRouter();
  const { id } = useParams<{ id: string }>();
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const { isDirty, setIsDirty } = useCourseFormStore();

  const fetchCourse = async () => {
    try {
      setIsLoading(true);
      const data = await getCourseById(id);
      setCourse(data.data);
    } catch (err) {
      console.error('Error fetching course:', err);
      setError('Failed to load course details. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchCourse();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handleDiscardChanges = () => {
    setIsDirty(false);
    setShowUnsavedDialog(false);
    router.back();
  };

  const handleBack = () => {
    if (isDirty) {
      setShowUnsavedDialog(true);
    } else {
      router.back();
    }
  };

  const handleKeepEditing = () => {
    setShowUnsavedDialog(false);
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#0000]/40 ">
        <Loader />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="text-destructive-500 px-4 py-3">
        <p>{error || 'Course not found'}</p>
        <Button
          onClick={() => router.back()}
          variant="outline"
          className="mt-4"
        >
          Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="mb-4">
        <DynamicBreadcrumb
          customBreadcrumbs={[
            {
              label: 'My Courses',
              href: '/dashboard/courses',
            },
            {
              label: course.name,
              href: `/dashboard/courses/${course.id}`,
            },
            {
              label: 'Edit Course',
              href: `/dashboard/courses/${course.id}/edit`,
            },
          ]}
        />
      </div>

      <Button variant="outline" className="py-6 px-6 my-6" onClick={handleBack}>
        <ChevronLeft className="h-4 w-4 mr-1" />
        Back
      </Button>

      <EditCourseForm initialCourse={course} mode="edit" />

      <UnsavedChangesDialog
        isOpen={showUnsavedDialog}
        onClose={() => setShowUnsavedDialog(false)}
        onDiscard={handleDiscardChanges}
        onKeepEditing={handleKeepEditing}
      />
    </div>
  );
}
