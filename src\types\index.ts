export type Partner = {
  id?: string;
  name?: string;
  partnerId?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  industry?: string;
  size?: string;
  type?: number;
  status?: string;
  logo?: string;
  cover?: string;
  socialMediaLinks?: string;
  country?: string;
  state?: string;
  addressLine1?: string;
  city?: string;
  postCode?: string;
  mailAddressLine1?: string;
  mailCity?: string;
  mailPostCode?: string;
  lastModifiedById?: string;
  CreatedById?: string;
};

export interface JobType {
  id: string;
  value: string;
  label?: string;
}
