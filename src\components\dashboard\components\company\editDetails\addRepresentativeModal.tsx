'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useInviteRepresentative } from '@/queries';
import { useNotificationStore } from '@/zustand/user/notificationStore';
import { useState, useEffect } from 'react';

interface AddRepresentativeModalProps {
  isOpen: boolean;
  onClose: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onInvite: (_representative: any) => void;
  initialData?: {
    firstName: string;
    lastName: string;
    email: string;
    role: {
      Full_Admin_Access: boolean;
      career: boolean;
      training: boolean;
      finance: boolean;
    };
  };
}

export function AddRepresentativeModal({
  isOpen,
  onClose,
  onInvite,
  initialData = {
    firstName: '',
    lastName: '',
    email: '',
    role: {
      Full_Admin_Access: false,
      career: false,
      training: false,
      finance: false,
    },
  },
}: AddRepresentativeModalProps) {
  const [representative, setRepresentative] = useState(initialData);
  const { mutate: invite, status } = useInviteRepresentative();
  const { showNotification } = useNotificationStore();
  const isLoading = status === 'pending';

  useEffect(() => {
    if (initialData) {
      setRepresentative(initialData);
    }
  }, [initialData]);

  const handleFullAdminChange = (checked: boolean) => {
    setRepresentative({
      ...representative,
      role: {
        Full_Admin_Access: checked,
        career: checked,
        training: checked,
        finance: checked,
      },
    });
  };

  const handlePermissionChange = (permission: string, value: boolean) => {
    const newPermissions = {
      ...representative.role,
      [permission]: value,
      Full_Admin_Access: false,
    };

    setRepresentative({
      ...representative,
      role: newPermissions,
    });
  };

  const formatRoleForAPI = () => {
    const selectedRoles: string[] = [];

    if (representative.role.Full_Admin_Access) {
      selectedRoles.push('Full_Admin_Access');
    } else {
      if (representative.role.career) {
        selectedRoles.push('Career');
      }
      if (representative.role.training) {
        selectedRoles.push('Training');
      }
      if (representative.role.finance) {
        selectedRoles.push('Finance');
      }
    }

    return selectedRoles.join(', ');
  };

  const handleSendInvite = () => {
    if (
      !representative.role.Full_Admin_Access &&
      !representative.role.career &&
      !representative.role.training &&
      !representative.role.finance
    ) {
      showNotification('Please select at least one permission', 'error');
      return;
    }

    const inviteData = {
      firstName: representative.firstName,
      lastName: representative.lastName,
      email: representative.email,
      role: formatRoleForAPI(),
    };

    invite(inviteData, {
      onSuccess: () => {
        onInvite(inviteData);
        onClose();
      },
      onError: () => {},
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[800px] p-[24px]">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle className="text-[24px] font-semibold leading-[32px] text-neutral-900">
              Add Company Representative
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 py-4">
          <div>
            <Label
              className="text-[18px] font-semibold text-neutral-900 leading-[28px]"
              htmlFor="firstName"
            >
              First Name:
            </Label>
            <Input
              id="firstName"
              value={representative.firstName}
              onChange={(e) =>
                setRepresentative({
                  ...representative,
                  firstName: e.target.value,
                })
              }
              placeholder="John"
            />
          </div>
          <div>
            <Label
              className="text-[18px] font-semibold text-neutral-900 leading-[28px]"
              htmlFor="lastName"
            >
              Last Name:
            </Label>
            <Input
              id="lastName"
              value={representative.lastName}
              onChange={(e) =>
                setRepresentative({
                  ...representative,
                  lastName: e.target.value,
                })
              }
              placeholder="Smith"
            />
          </div>
          <div className="col-span-2">
            <Label
              className="text-[18px] font-semibold text-neutral-900 leading-[28px]"
              htmlFor="email"
            >
              Work Email:
            </Label>
            <Input
              id="email"
              value={representative.email}
              onChange={(e) =>
                setRepresentative({ ...representative, email: e.target.value })
              }
              placeholder="<EMAIL>"
            />
          </div>

          <div className="col-span-2 mt-2">
            <Label className="text-[18px] font-semibold text-neutral-900 leading-[28px]">
              Permissions:
            </Label>
            <div className="space-y-3 mt-2">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="Full_Admin_Access"
                  checked={representative?.role?.Full_Admin_Access}
                  onCheckedChange={(checked) =>
                    handleFullAdminChange(checked as boolean)
                  }
                />
                <div>
                  <Label htmlFor="Full_Admin_Access" className="font-medium">
                    Full Admin Access
                  </Label>
                  <p className="text-sm text-neutral-500">
                    Access to all modules, edit company info and manage
                    representatives.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="career"
                  checked={representative?.role?.career}
                  onCheckedChange={(checked) =>
                    handlePermissionChange('career', checked as boolean)
                  }
                  disabled={representative?.role?.Full_Admin_Access}
                  className={
                    representative?.role?.Full_Admin_Access
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }
                />
                <div
                  className={
                    representative?.role?.Full_Admin_Access
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }
                >
                  <Label htmlFor="career" className="font-medium">
                    Career
                  </Label>
                  <p className="text-sm text-neutral-500">
                    Access to job postings, candidate management, and career
                    analytics.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="training"
                  checked={representative?.role?.training}
                  onCheckedChange={(checked) =>
                    handlePermissionChange('training', checked as boolean)
                  }
                  disabled={representative?.role?.Full_Admin_Access}
                  className={
                    representative?.role?.Full_Admin_Access
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }
                />
                <div
                  className={
                    representative?.role?.Full_Admin_Access
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }
                >
                  <Label htmlFor="training" className="font-medium">
                    Training
                  </Label>
                  <p className="text-sm text-neutral-500">
                    Access to course listings, applications, and training
                    dashboards.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="finance"
                  checked={representative?.role?.finance}
                  onCheckedChange={(checked) =>
                    handlePermissionChange('finance', checked as boolean)
                  }
                  disabled={representative?.role?.Full_Admin_Access}
                  className={
                    representative?.role?.Full_Admin_Access
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }
                />
                <div
                  className={
                    representative?.role?.Full_Admin_Access
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }
                >
                  <Label htmlFor="finance" className="font-medium">
                    Finance
                  </Label>
                  <p className="text-sm text-neutral-500">
                    Access to benefit tracking, financial dashboards, and
                    disbursement reports.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="bg-primary-500 hover:bg-primary-500 text-white"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleSendInvite();
            }}
            disabled={isLoading}
          >
            {isLoading ? 'Sending...' : 'Invite'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
