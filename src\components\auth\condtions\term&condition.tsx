import React from 'react';
import { privacyDefinitions } from '@/constants/privacyDefinition';
import Link from 'next/link';
import LawEnforcementData from './templates/lawEnforcement';
import BusinessTransactionsData from './templates/businessTransaction';
import DeletePersonal from './templates/deletePersonalData';
import RetentionTransferData from './templates/retention&transferData';
import UsePersonalData from './templates/personalData';
import CookiesData from './templates/cookiesData';
import Details from './templates/detail';
import GDPRPrivacy from './templates/gdprPrivacy';

function TermCondition() {
  return (
    <div>
      <h1 className="text-[16px] text-neutral-900 font-medium mb-4">
        Last updated: March 22, 2024
      </h1>
      <p className="text-[16px] text-neutral-900 font-normal">
        This Privacy Policy describes Our policies and procedures on the
        collection, use, and disclosure of Your information when You use the
        Service and tells You about Your privacy rights and how the law protects
        You.
      </p>
      <p className="text-[16px] text-neutral-900 font-normal mt-4">
        We use Your Personal data to provide and improve the Service. By using
        the Service, You agree to the collection and use of information in
        accordance with this Privacy Policy.
      </p>

      <div className="mt-6">
        <h5 className="text-blue-300 text-[18px] font-medium mb-4">
          Interpretation and Definitions
        </h5>

        <h5 className="text-blue-300 text-[16px] font-medium">
          Interpretation
        </h5>
        <p className="text-[16px] font-normal text-neutral-900 leading-6">
          The words of which the initial letter is capitalized have meanings
          defined under the following conditions. The following definitions
          shall have the same meaning regardless of whether they appear in
          singular or in plural.
        </p>

        <h5 className="text-blue-300 text-[16px] font-medium mt-4">
          Definitions
        </h5>

        <div className="mt-4">
          <p className="mb-4">For the purposes of this Privacy Policy:</p>
          <ul className="ml-8 flex flex-col gap-3">
            {privacyDefinitions.map((item) => (
              <li
                key={item.link}
                className="text-[16px] font-normal leading-6 text-neutral-900"
              >
                <span className="font-semibold">{item.term}</span>{' '}
                {item.definition}
                {item.link && (
                  <Link
                    href={item.link}
                    className="text-blue-500 underline ml-1"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {item.link}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex flex-col gap-2">
          <h5 className="text-blue-300 text-[16px] font-medium mt-4">
            Collecting and Using Your Personal Data{' '}
          </h5>
          <h5 className="text-blue-300 text-[16px] font-medium mt-4">
            Types of Data Collected
          </h5>
        </div>
        <div>
          <h5 className="text-blue-300 text-[16px] font-medium mt-4 italic">
            Personal Data
          </h5>
          <p className=" text-[16px] font-normal text-neutral-900">
            While using Our Service, We may ask You to provide Us with certain
            personally identifiable information that can be used to contact or
            identify You. Personally identifiable information may include, but
            is not limited to:
          </p>
          <ul className="ml-8 flex flex-col mt-4 list-disc">
            <li> Email address</li>
            <li> First name and last name </li>
            <li> Usage Data</li>
          </ul>
        </div>

        <div>
          <h5 className="text-blue-300 text-[16px] font-medium mt-4 italic">
            Usage Data
          </h5>

          <p className=" text-[16px] font-normal text-neutral-900">
            Usage Data is collected automatically when using the Service.
            <br />
            <br />
            UsageData may include information such as Your Device`s Internet
            Protocol address (e.g. IP address), browser type, browser version,
            the pages of our Service that You visit, the time and date of Your
            visit, the time spent on those pages, unique device identifiers and
            other diagnostic data. <br /> <br />
            When You access the Service by or through a mobile device, We may
            collect certain information automatically, including, but not
            limited to, the type of mobile device You use, Your mobile device
            unique ID, the IP address of Your mobile device, Your mobile
            operating system, the type of mobile Internet browser You use,
            unique device identifiers and other diagnostic data. <br /> <br />
            We may also collect information that Your browser sends whenever You
            visit our Service or when You access the Service by or through a
            mobile device.
          </p>
        </div>

        <CookiesData />
        <UsePersonalData />
        <RetentionTransferData />
        <DeletePersonal />
        <BusinessTransactionsData />
        <LawEnforcementData />
        <Details />
        <GDPRPrivacy />
      </div>
    </div>
  );
}

export default TermCondition;
