import React from 'react';

// Mock data for personal information
const mockData = {
  firstName: 'Sara',
  lastName: 'Najjar',
  NationalityId: '289123456789',
  Nationality: 'Kuwait',
  Mobile: '+965 5000 1234',
  Email: '<EMAIL>',
};

interface UserInfo {
  label: string;
  value: string;
}

function InfoSection({
  title,
  data,
  isLast = false,
}: {
  title: string;
  data: UserInfo[];
  isLast?: boolean;
}) {
  return (
    <div className={`my-4 py-4 ${!isLast ? 'border-b border-[#D7DAED]' : ''}`}>
      <h3 className="text-[#111827] text-[18px] font-semibold leading-[28px] mb-2">
        {title}
      </h3>
      {data.map((item) => (
        <div key={item.label} className="flex justify-between mb-2">
          <p className="text-[16px] font-semibold leading-[24px] text-[#646A77]">
            {item.label}
          </p>
          <p className="text-[16px] font-semibold leading-[24px] text-[#646A77]">
            {item.value}
          </p>
        </div>
      ))}
    </div>
  );
}

function PersonalInformation({
  user,
}: {
  user: {
    firstName: string;
    lastName: string;
    NationalityId: string;
    Nationality: string;
    Mobile: string;
    Email: string;
  };
}) {
  return (
    <div className="py-4 px-4 mt-4 border border-[#D7DAED] rounded-md">
      <h4 className="text-[20px] tracking-tight leading-[20px] font-semibold text-[#111827]">
        Personal Information
      </h4>

      <InfoSection
        title="Name"
        data={[
          { label: 'First Name', value: user.firstName },
          { label: 'Last Name', value: user.lastName },
        ]}
      />
      <InfoSection
        title="Nationality"
        data={[
          { label: 'Kuwait ID', value: user.NationalityId },
          { label: 'Nationality', value: user.Nationality },
        ]}
      />
      <InfoSection
        title="Contact"
        data={[
          { label: 'Mobile', value: user.Mobile },
          { label: 'Email', value: user.Email },
        ]}
        isLast={true}
      />
    </div>
  );
}

export default function PersonalInformationWrapper() {
  return <PersonalInformation user={mockData} />;
}
