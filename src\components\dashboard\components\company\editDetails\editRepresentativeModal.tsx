/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import type React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect } from 'react';
import { useUpdateUserProfile } from '@/queries';
import { useNotificationStore } from '@/zustand/user/notificationStore';

interface Representative {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  permissions: {
    Full_Admin_Access: boolean;
    career: boolean;
    training: boolean;
    finance: boolean;
  };
}

interface EditRepresentativeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (_representative: Representative) => void;
  representative: Representative | null;
}

export function EditRepresentativeModal({
  isOpen,
  onClose,
  onSave,
  representative,
}: EditRepresentativeModalProps) {
  const [localRepresentative, setLocalRepresentative] =
    useState<Representative | null>(null);
  const { mutate: updateProfile, isPending: isUpdating } =
    useUpdateUserProfile();
  const { showNotification } = useNotificationStore();

  // Parse role string into permissions object
  const parseRoleToPermissions = (roleString: string) => {
    // Try different splitting approaches
    const rolesBySplit = roleString.split(', ').map((r) => r.trim());
    const rolesByComma = roleString.split(',').map((r) => r.trim());

    // Use the approach that gives us more meaningful results
    const roles =
      rolesBySplit.length > rolesByComma.length ? rolesBySplit : rolesByComma;

    // Try multiple matching strategies
    const exactMatch = roles.includes('Full_Admin_Access');
    const trimmedMatch = roles
      .map((r) => r.trim())
      .includes('Full_Admin_Access');
    const caseInsensitiveMatch = roles.some(
      (r) => r.trim().toLowerCase() === 'full_admin_access'
    );

    // Use the most permissive match that makes sense
    const hasFullAdmin = exactMatch || trimmedMatch || caseInsensitiveMatch;

    return {
      Full_Admin_Access: hasFullAdmin,
      career:
        hasFullAdmin || roles.some((r) => r.trim().toLowerCase() === 'career'),
      training:
        hasFullAdmin ||
        roles.some((r) => r.trim().toLowerCase() === 'training'),
      finance:
        hasFullAdmin || roles.some((r) => r.trim().toLowerCase() === 'finance'),
    };
  };

  useEffect(() => {
    if (representative) {
      const repCopy = JSON.parse(JSON.stringify(representative));
      // Parse the role string to set correct permissions
      repCopy.permissions = parseRoleToPermissions(representative.role);
      setLocalRepresentative(repCopy);
    }
  }, [representative]);

  const handleFullAdminChange = (checked: boolean) => {
    if (!localRepresentative) return;

    const newPermissions = {
      Full_Admin_Access: checked,
      career: checked, // When Full Admin is checked, all others should be checked too
      training: checked,
      finance: checked,
    };

    setLocalRepresentative({
      ...localRepresentative,
      permissions: newPermissions,
    });
  };

  const handlePermissionChange = (permission: string, value: boolean) => {
    if (!localRepresentative) return;

    const newPermissions = {
      ...localRepresentative.permissions,
      [permission]: value,
    };

    // If any individual permission is unchecked, uncheck Full Admin Access
    if (!value) {
      newPermissions.Full_Admin_Access = false;
    }

    // If all individual permissions are checked, check Full Admin Access
    if (
      newPermissions.career &&
      newPermissions.training &&
      newPermissions.finance
    ) {
      newPermissions.Full_Admin_Access = true;
    }

    setLocalRepresentative({
      ...localRepresentative,
      permissions: newPermissions,
    });
  };

  const formatRoleForAPI = (permissions: Representative['permissions']) => {
    const selectedRoles: string[] = [];

    if (permissions.Full_Admin_Access) {
      selectedRoles.push('Full_Admin_Access');
    } else {
      if (permissions.career) {
        selectedRoles.push('career');
      }
      if (permissions.training) {
        selectedRoles.push('training');
      }
      if (permissions.finance) {
        selectedRoles.push('finance');
      }
    }

    return selectedRoles.join(', ');
  };

  const validateForm = () => {
    if (!localRepresentative) return false;

    const hasAnyPermission =
      localRepresentative.permissions.Full_Admin_Access ||
      localRepresentative.permissions.career ||
      localRepresentative.permissions.training ||
      localRepresentative.permissions.finance;

    if (!hasAnyPermission) {
      showNotification('Please select at least one role', 'error');
      return false;
    }

    return true;
  };

  const handleSaveClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!localRepresentative || !validateForm()) return;

    const updateData = {
      id: localRepresentative.id,
      firstName: localRepresentative.firstName.trim(),
      lastName: localRepresentative.lastName.trim(),
      email: localRepresentative.email,
      fullName: `${localRepresentative.firstName.trim()} ${localRepresentative.lastName.trim()}`,
      role: formatRoleForAPI(localRepresentative.permissions),
    };

    updateProfile(updateData, {
      onSuccess: (response) => {
        showNotification('Representative updated successfully!', 'success');

        const updatedRepresentative = {
          ...localRepresentative,
          role: updateData.role,
        };
        onSave(updatedRepresentative);
        onClose();
      },
      onError: (error) => {
        showNotification(
          `Failed to update representative: ${error.message}`,
          'error'
        );
      },
    });
  };

  const handleClose = () => {
    if (isUpdating) return;
    onClose();
  };

  if (!localRepresentative && isOpen) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-[800px] p-[24px]">
          <div className="flex items-center justify-center p-6">Loading...</div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[800px] p-[24px]">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle className="text-[24px] font-semibold leading-[32px] text-neutral-900">
              Edit Company Representative
            </DialogTitle>
          </div>
        </DialogHeader>

        {localRepresentative && (
          <>
            <div className="grid grid-cols-2 gap-4 py-4">
              <div>
                <Label
                  className="text-[18px] font-semibold text-neutral-900 leading-[28px]"
                  htmlFor="editFirstName"
                >
                  First Name:
                </Label>
                <Input
                  id="editFirstName"
                  value={localRepresentative.firstName}
                  onChange={(e) =>
                    setLocalRepresentative({
                      ...localRepresentative,
                      firstName: e.target.value,
                    })
                  }
                  disabled={isUpdating}
                />
              </div>
              <div>
                <Label
                  className="text-[18px] font-semibold text-neutral-900 leading-[28px]"
                  htmlFor="editLastName"
                >
                  Last Name:
                </Label>
                <Input
                  id="editLastName"
                  value={localRepresentative.lastName}
                  onChange={(e) =>
                    setLocalRepresentative({
                      ...localRepresentative,
                      lastName: e.target.value,
                    })
                  }
                  disabled={isUpdating}
                />
              </div>
              <div className="col-span-2">
                <Label
                  className="text-[18px] font-semibold text-neutral-900 leading-[28px]"
                  htmlFor="editEmail"
                >
                  Work Email:
                </Label>
                <Input
                  id="editEmail"
                  value={localRepresentative.email}
                  readOnly
                  className="bg-neutral-100 opacity-75 cursor-not-allowed"
                />
              </div>

              <div className="col-span-2 mt-2">
                <Label className="text-[18px] font-semibold text-neutral-900 leading-[28px]">
                  Roles:
                </Label>
                <div className="space-y-3 mt-2">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="editFullAdmin"
                      checked={
                        localRepresentative.permissions.Full_Admin_Access
                      }
                      onCheckedChange={(checked) =>
                        handleFullAdminChange(checked as boolean)
                      }
                      disabled={isUpdating}
                      className="mt-1"
                    />
                    <div>
                      <Label htmlFor="editFullAdmin" className="font-medium">
                        Full Admin Access
                      </Label>
                      <p className="text-sm text-neutral-500">
                        Access to all modules, edit company info and manage
                        representatives.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="editCareer"
                      checked={localRepresentative.permissions.career}
                      onCheckedChange={(checked) =>
                        handlePermissionChange('career', checked as boolean)
                      }
                      disabled={
                        localRepresentative.permissions.Full_Admin_Access ||
                        isUpdating
                      }
                      className={
                        localRepresentative.permissions.Full_Admin_Access
                          ? 'opacity-50 cursor-not-allowed'
                          : 'mt-1'
                      }
                    />
                    <div
                      className={
                        localRepresentative.permissions.Full_Admin_Access
                          ? 'opacity-50 cursor-not-allowed'
                          : ''
                      }
                    >
                      <Label htmlFor="editCareer" className="font-medium">
                        Career
                      </Label>
                      <p className="text-sm text-neutral-500">
                        Access to job postings, candidate management, and career
                        analytics.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="editTraining"
                      checked={localRepresentative.permissions.training}
                      onCheckedChange={(checked) =>
                        handlePermissionChange('training', checked as boolean)
                      }
                      disabled={
                        localRepresentative.permissions.Full_Admin_Access ||
                        isUpdating
                      }
                      className={
                        localRepresentative.permissions.Full_Admin_Access
                          ? 'opacity-50 cursor-not-allowed'
                          : 'mt-1'
                      }
                    />
                    <div
                      className={
                        localRepresentative.permissions.Full_Admin_Access
                          ? 'opacity-50 cursor-not-allowed'
                          : ''
                      }
                    >
                      <Label htmlFor="editTraining" className="font-medium">
                        Training
                      </Label>
                      <p className="text-sm text-neutral-500">
                        Access to course listings, applications, and training
                        dashboards.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="editFinance"
                      checked={localRepresentative.permissions.finance}
                      onCheckedChange={(checked) =>
                        handlePermissionChange('finance', checked as boolean)
                      }
                      disabled={
                        localRepresentative.permissions.Full_Admin_Access ||
                        isUpdating
                      }
                      className={
                        localRepresentative.permissions.Full_Admin_Access
                          ? 'opacity-50 cursor-not-allowed'
                          : 'mt-1'
                      }
                    />
                    <div
                      className={
                        localRepresentative.permissions.Full_Admin_Access
                          ? 'opacity-50 cursor-not-allowed'
                          : ''
                      }
                    >
                      <Label htmlFor="editFinance" className="font-medium">
                        Finance
                      </Label>
                      <p className="text-sm text-neutral-500">
                        Access to benefit tracking, financial dashboards, and
                        disbursement reports.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleClose();
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                type="button"
                className="bg-primary-500 hover:bg-primary-600 text-white"
                onClick={handleSaveClick}
                disabled={isUpdating}
              >
                {isUpdating ? 'Updating...' : 'Save'}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
