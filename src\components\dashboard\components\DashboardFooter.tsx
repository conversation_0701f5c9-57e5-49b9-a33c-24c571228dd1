import React from 'react';
import { FaFacebook } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import { IoLogoInstagram } from 'react-icons/io';
import { FaLinkedin } from 'react-icons/fa6';
import Link from 'next/link';

function DashboardFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative  bottom-0 px-5 py-5 bg-white flex items-center justify-between">
      <p className="text-[16px] font-normal text-neutral-500">
        © {currentYear} Career Navigator. All rights reserved.
      </p>
      <div className="flex items-center gap-3">
        <Link
          href="https://www.facebook.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <FaFacebook className="text-neutral-500" />
        </Link>
        <Link
          href="https://twitter.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <FaXTwitter className="text-neutral-500" />
        </Link>
        <Link
          href="https://www.instagram.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <IoLogoInstagram className="text-neutral-500" />
        </Link>
        <Link
          href="https://www.linkedin.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <FaLinkedin className="text-neutral-500" />
        </Link>
      </div>
    </footer>
  );
}

export default DashboardFooter;
