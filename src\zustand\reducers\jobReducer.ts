// import type { JobState, JobAction } from '@/type/jobType'

// export const jobReducer = (state: JobState, action: JobAction): JobState => {
//   switch (action.type) {
//     case 'SET_JOBS':
//       // Update the cache with the new data
//       return {
//         ...state,
//         jobs: action.payload.jobs,
//         page: action.payload.page,
//         size: action.payload.size,
//         total: action.payload.total,
//         // sortField: action.payload.sortField,
//         // sortOrder: action.payload.sortOrder,
//         searchTerm: action.payload.searchTerm || state.searchTerm,
//         isLoading: false,
//         // cachedPages: {
//         //   ...state.cachedPages,
//         //   [action.payload.cacheKey]: action.payload.jobs,
//         // },
//       }

//     case 'SET_CACHED_PAGE':
//       // Use cached data without making a new request
//       return {
//         ...state,
//         jobs: action.payload.jobs,
//         page: action.payload.page,
//         // sortField: action.payload.sortField,
//         // sortOrder: action.payload.sortOrder,
//         searchTerm: action.payload.searchTerm || state.searchTerm,
//         isLoading: false,
//       }

//     case 'SET_ALL_JOBS':
//       // Store all jobs for client-side sorting
//       return {
//         ...state,
//         allJobs: action.payload,
//         isLoading: false,
//       }

//     case 'SET_SORTED_JOBS':
//       // Update all jobs with sorted data
//       return {
//         ...state,
//         allJobs: action.payload.allJobs,
//         // sortField: action.payload.sortField,
//         // sortOrder: action.payload.sortOrder,
//         isLoading: false,
//       }

//     case 'SET_PAGE_WITH_JOBS':
//       // Update current page and jobs
//       return {
//         ...state,
//         page: action.payload.page,
//         jobs: action.payload.jobs,
//         isLoading: false,
//       }

//     case 'SET_USER_JOBS':
//       return {
//         ...state,
//         jobs: action.payload,
//         isLoading: false,
//       }

//     case 'SET_CURRENT_JOB':
//       return {
//         ...state,
//         currentJob: action.payload,
//         isLoading: false,
//       }

//     case 'ADD_JOB':
//       return {
//         ...state,
//         jobs: [action.payload, ...state.jobs],
//         allJobs: [action.payload, ...state.allJobs],
//       }

//     case 'UPDATE_JOB':
//       return {
//         ...state,
//         jobs: state.jobs.map((job) =>
//           job.id === action.payload.id ? action.payload : job
//         ),
//         allJobs: state.allJobs.map((job) =>
//           job.id === action.payload.id ? action.payload : job
//         ),
//         currentJob:
//           state.currentJob?.id === action.payload.id
//             ? action.payload
//             : state.currentJob,
//       }

//     case 'UPDATE_JOB_STATUS':
//       // Add the missing case for updating job status
//       return {
//         ...state,
//         jobs: state.jobs.map((job) =>
//           job.id === action.payload.id
//             ? { ...job, status: action.payload.status }
//             : job
//         ),
//         allJobs: state.allJobs.map((job) =>
//           job.id === action.payload.id
//             ? { ...job, status: action.payload.status }
//             : job
//         ),
//         currentJob:
//           state.currentJob?.id === action.payload.id
//             ? { ...state.currentJob, status: action.payload.status }
//             : state.currentJob,
//       }

//     case 'DELETE_JOB':
//       return {
//         ...state,
//         jobs: state.jobs.filter((job) => job.id !== action.payload),
//         allJobs: state.allJobs.filter((job) => job.id !== action.payload),
//         currentJob:
//           state.currentJob?.id === action.payload ? null : state.currentJob,
//       }

//     case 'START_LOADING':
//       return { ...state, isLoading: true }

//     case 'STOP_LOADING':
//       return { ...state, isLoading: false }

//     case 'SET_PAGE':
//       return { ...state, page: action.payload }

//     // case 'SET_SORT_FIELD':
//     //   return { ...state, sortField: action.payload }

//     // case 'SET_SORT_ORDER':
//     //   return { ...state, sortOrder: action.payload }

//     case 'SET_SEARCH_TERM':
//       return { ...state, searchTerm: action.payload, page: 1 }

//     // case 'CLEAR_CACHE':
//     //   return { ...state, cachedPages: {} }

//     default:
//       return state
//   }
// }
