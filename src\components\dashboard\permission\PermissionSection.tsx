'use client';

import type { ReactNode } from 'react';
import type { Permission, PermissionCheckOptions } from '@/types/permission';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield } from 'lucide-react';
import { usePermission } from '@/hooks/usePermission';

interface PermissionSectionProps {
  permission: Permission | Permission[];
  children: ReactNode;
  options?: PermissionCheckOptions;
  title?: string;
  description?: string;
  fallback?: ReactNode;
  showAccessDenied?: boolean;
  className?: string;
  loadingComponent?: ReactNode;
}

export function PermissionSection({
  permission,
  children,
  options,
  title,
  description,
  fallback,
  showAccessDenied = true,
  className = '',
  loadingComponent,
}: PermissionSectionProps) {
  const { checkPermission, isLoading } = usePermission();

  // Show loading state
  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {loadingComponent || (
          <>
            {title && (
              <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
            )}
            {description && (
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            )}
            <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
          </>
        )}
      </div>
    );
  }

  const hasPermission = checkPermission(permission, options);

  if (!hasPermission) {
    if (fallback) {
      return <div className={className}>{fallback}</div>;
    }

    if (showAccessDenied) {
      return (
        <div className={className}>
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              {title
                ? `You don't have permission to access "${title}".`
                : "You don't have permission to access this section."}
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    return null;
  }

  return (
    <div className={className}>
      {title && <h2 className="text-2xl font-bold mb-2">{title}</h2>}
      {description && <p className="text-gray-600 mb-4">{description}</p>}
      {children}
    </div>
  );
}
